PODS:
  - Capacitor (5.7.8):
    - Capacitor<PERSON>ordova
  - CapacitorApp (5.0.8):
    - Capacitor
  - CapacitorCamera (5.0.10):
    - Capacitor
  - CapacitorCordova (5.7.8)
  - CapacitorDevice (5.0.8):
    - Capacitor
  - CapacitorHaptics (5.0.8):
    - Capacitor
  - CapacitorKeyboard (5.0.9):
    - Capacitor
  - CapacitorLocalNotifications (5.0.8):
    - Capacitor
  - CapacitorNetwork (5.0.8):
    - Capacitor
  - CapacitorPreferences (5.0.8):
    - Capacitor
  - CapacitorPushNotifications (5.1.2):
    - Capacitor
  - CapacitorSplashScreen (5.0.8):
    - Capacitor
  - CapacitorStatusBar (5.0.8):
    - Capacitor

DEPENDENCIES:
  - "Capacitor (from `../../node_modules/@capacitor/ios`)"
  - "CapacitorApp (from `../../node_modules/@capacitor/app`)"
  - "CapacitorCamera (from `../../node_modules/@capacitor/camera`)"
  - "CapacitorCordova (from `../../node_modules/@capacitor/ios`)"
  - "CapacitorDevice (from `../../node_modules/@capacitor/device`)"
  - "CapacitorHaptics (from `../../node_modules/@capacitor/haptics`)"
  - "CapacitorKeyboard (from `../../node_modules/@capacitor/keyboard`)"
  - "CapacitorLocalNotifications (from `../../node_modules/@capacitor/local-notifications`)"
  - "CapacitorNetwork (from `../../node_modules/@capacitor/network`)"
  - "CapacitorPreferences (from `../../node_modules/@capacitor/preferences`)"
  - "CapacitorPushNotifications (from `../../node_modules/@capacitor/push-notifications`)"
  - "CapacitorSplashScreen (from `../../node_modules/@capacitor/splash-screen`)"
  - "CapacitorStatusBar (from `../../node_modules/@capacitor/status-bar`)"

EXTERNAL SOURCES:
  Capacitor:
    :path: "../../node_modules/@capacitor/ios"
  CapacitorApp:
    :path: "../../node_modules/@capacitor/app"
  CapacitorCamera:
    :path: "../../node_modules/@capacitor/camera"
  CapacitorCordova:
    :path: "../../node_modules/@capacitor/ios"
  CapacitorDevice:
    :path: "../../node_modules/@capacitor/device"
  CapacitorHaptics:
    :path: "../../node_modules/@capacitor/haptics"
  CapacitorKeyboard:
    :path: "../../node_modules/@capacitor/keyboard"
  CapacitorLocalNotifications:
    :path: "../../node_modules/@capacitor/local-notifications"
  CapacitorNetwork:
    :path: "../../node_modules/@capacitor/network"
  CapacitorPreferences:
    :path: "../../node_modules/@capacitor/preferences"
  CapacitorPushNotifications:
    :path: "../../node_modules/@capacitor/push-notifications"
  CapacitorSplashScreen:
    :path: "../../node_modules/@capacitor/splash-screen"
  CapacitorStatusBar:
    :path: "../../node_modules/@capacitor/status-bar"

SPEC CHECKSUMS:
  Capacitor: 9686e5711b48a1aa84671ede795d0175848abcba
  CapacitorApp: 8026f7ce1ac72780258d05a90c07deb5c724c0fa
  CapacitorCamera: 916a67c6726fabcc623df79d3f934828f2520103
  CapacitorCordova: 31ab98dca2ddcee051027a1afe7e8c85c82f7297
  CapacitorDevice: 4aceb502ce1142ea45a2aece603169d7479cd271
  CapacitorHaptics: acd823c6ed0ec2cd968a09316768144b0495a1e7
  CapacitorKeyboard: 38db46e3e9a8024814994b5a909f102f3d27c207
  CapacitorLocalNotifications: 73765422be07829b2d0a4c7c22a77ea2252ca210
  CapacitorNetwork: b76949446d62b4832ecd5b652ef95b5294c25f00
  CapacitorPreferences: 5331d7a33044d252dcb2f7fee712f0322c04bae7
  CapacitorPushNotifications: 95dc889e9f3540e3c4f118f7ed53fd61db82100b
  CapacitorSplashScreen: 0b962a2bee014151569dc59b089ba0e93daeeaf0
  CapacitorStatusBar: 8e0a8f213504695505dc8c4f808dffbe03aa0e0f

PODFILE CHECKSUM: 91d583fbfbeffa2c256fab7b73106223110a929c

COCOAPODS: 1.16.2
