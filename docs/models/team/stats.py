from sqlalchemy import Column, Integer, DateTime, ForeignKey, func, Float, JSON
from sqlalchemy.orm import relationship

from app.db.base_class import Base


class TeamStats(Base):
    """团队统计数据模型"""
    
    __tablename__ = "team_stats"
    
    id = Column(Integer, primary_key=True, index=True)
    team_id = Column(Integer, ForeignKey("teams.id", ondelete="CASCADE"), index=True)
    
    # 统计数据
    total_members = Column(Integer, default=0)  # 团队成员总数
    total_clients = Column(Integer, default=0)  # 团队客户总数
    active_clients_30d = Column(Integer, default=0)  # 30天内活跃客户数
    total_sessions = Column(Integer, default=0)  # 总训练课程数
    completed_sessions = Column(Integer, default=0)  # 已完成训练课程数
    completion_rate = Column(Float, default=0.0)  # 完成率
    growth_rate = Column(Float, default=0.0)  # 增长率
    monthly_stats = Column(JSON, default=dict)  # 月度统计数据
    
    # 时间戳
    created_at = Column(DateTime, server_default=func.now())
    updated_at = Column(DateTime, server_default=func.now(), onupdate=func.now())
    
    # 关系
    team = relationship("Team", back_populates="stats") 