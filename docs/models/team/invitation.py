from sqlalchemy import Column, Integer, String, DateTime, ForeignKey, func
from sqlalchemy.orm import relationship

from app.db.base_class import Base


class TeamInvitation(Base):
    """团队邀请模型"""
    
    __tablename__ = "team_invitations"
    
    id = Column(Integer, primary_key=True, index=True)
    team_id = Column(Integer, ForeignKey("teams.id", ondelete="CASCADE"))
    inviter_id = Column(Integer, ForeignKey("users.id", ondelete="SET NULL"), nullable=True)
    invitee_id = Column(Integer, ForeignKey("users.id", ondelete="SET NULL"), nullable=True)
    role = Column(Integer)
    status = Column(Integer)
    created_at = Column(DateTime, server_default=func.now())
    expired_at = Column(DateTime, nullable=True)
    
    # 关系
    team = relationship("Team")
    inviter = relationship("User", foreign_keys=[inviter_id])
    invitee = relationship("User", foreign_keys=[invitee_id]) 