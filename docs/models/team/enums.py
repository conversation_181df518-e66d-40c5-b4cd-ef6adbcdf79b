import enum

# 保留 IntEnum 类型，用于 Python 代码中的引用
class TeamRole(enum.IntEnum):
    """团队角色枚举"""
    OWNER = 1  # 拥有者
    ADMIN = 2  # 管理员
    COACH = 3  # 教练
    MEMBER = 4  # 普通成员

class MembershipStatus(enum.IntEnum):
    """团队成员状态枚举"""
    PENDING = 1  # 待确认
    ACTIVE = 2  # 活跃
    SUSPENDED = 3  # 暂停
    TERMINATED = 4  # 终止

class TeamStatus(enum.IntEnum):
    """团队状态枚举"""
    ACTIVE = 1  # 活跃
    SUSPENDED = 2  # 暂停
    TERMINATED = 3  # 终止

class ClientStatus(enum.IntEnum):
    """客户状态枚举"""
    PENDING = 1  # 待确认
    ACTIVE = 2  # 活跃
    PAUSED = 3  # 暂停
    TERMINATED = 4  # 终止

class InvitationStatus(enum.IntEnum):
    """邀请状态枚举"""
    PENDING = 1  # 待确认
    ACCEPTED = 2  # 已接受
    REJECTED = 3  # 已拒绝
    EXPIRED = 4  # 已过期
