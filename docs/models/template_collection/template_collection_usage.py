from sqlalchemy import Column, Integer, String, DateTime, ForeignKey
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from app.db.base_class import Base


class TemplateCollectionUsage(Base):
    """模板集合使用统计表
    
    记录用户对模板集合的各种操作，用于数据分析和推荐算法。
    包括查看、下载、应用、完成等操作类型。
    """
    __tablename__ = "template_collection_usage"

    # 基础字段
    id = Column(Integer, primary_key=True, index=True)
    collection_id = Column(Integer, ForeignKey("template_collections.id"), nullable=False, comment="集合ID")
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False, comment="用户ID")
    
    # 使用类型
    action_type = Column(String(20), nullable=False, comment="操作类型：view/download/apply/complete")
    
    # 相关记录ID（可选）
    training_plan_id = Column(Integer, ForeignKey("training_plans.id"), nullable=True, comment="关联的训练计划ID")
    workout_id = Column(Integer, ForeignKey("workouts.id"), nullable=True, comment="关联的训练ID")
    
    # 使用上下文（用于分析）
    user_agent = Column(String(500), nullable=True, comment="用户代理")
    ip_address = Column(String(45), nullable=True, comment="IP地址")
    referrer = Column(String(500), nullable=True, comment="来源页面")
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    
    # 关系定义
    collection = relationship("TemplateCollection", back_populates="usage_stats")
    user = relationship("User", back_populates="template_usage")
    training_plan = relationship("TrainingPlan")
    workout = relationship("Workout")

    def __repr__(self):
        return f"<TemplateCollectionUsage collection_id={self.collection_id} user_id={self.user_id} action={self.action_type}>"
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            "id": self.id,
            "collection_id": self.collection_id,
            "user_id": self.user_id,
            "action_type": self.action_type,
            "training_plan_id": self.training_plan_id,
            "workout_id": self.workout_id,
            "user_agent": self.user_agent,
            "ip_address": self.ip_address,
            "referrer": self.referrer,
            "created_at": self.created_at.isoformat() if self.created_at else None,
        } 