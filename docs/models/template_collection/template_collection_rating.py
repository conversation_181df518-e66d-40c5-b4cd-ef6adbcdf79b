from sqlalchemy import Column, Integer, DateTime, ForeignKey, SmallInteger, Text, UniqueConstraint
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from app.db.base_class import Base


class TemplateCollectionRating(Base):
    """模板集合评分表
    
    用户对模板集合的评分和评价系统。
    支持1-5星评分和详细评价内容。
    """
    __tablename__ = "template_collection_ratings"

    # 基础字段
    id = Column(Integer, primary_key=True, index=True)
    collection_id = Column(Integer, ForeignKey("template_collections.id"), nullable=False, comment="集合ID")
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False, comment="用户ID")
    
    # 评分信息
    rating = Column(SmallInteger, nullable=False, comment="1-5星评分")
    review = Column(Text, nullable=True, comment="评价内容")
    
    # 详细评分维度（可选）
    difficulty_rating = Column(SmallInteger, nullable=True, comment="难度评分 1-5")
    effectiveness_rating = Column(SmallInteger, nullable=True, comment="效果评分 1-5")
    instruction_clarity_rating = Column(SmallInteger, nullable=True, comment="指导清晰度评分 1-5")
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), comment="更新时间")
    
    # 关系定义
    collection = relationship("TemplateCollection", back_populates="ratings")
    user = relationship("User", back_populates="template_ratings")
    
    # 唯一约束：一个用户只能对一个集合评分一次
    __table_args__ = (
        UniqueConstraint('collection_id', 'user_id', name='unique_user_collection_rating'),
    )

    def __repr__(self):
        return f"<TemplateCollectionRating collection_id={self.collection_id} user_id={self.user_id} rating={self.rating}>"
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            "id": self.id,
            "collection_id": self.collection_id,
            "user_id": self.user_id,
            "rating": self.rating,
            "review": self.review,
            "difficulty_rating": self.difficulty_rating,
            "effectiveness_rating": self.effectiveness_rating,
            "instruction_clarity_rating": self.instruction_clarity_rating,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
        } 