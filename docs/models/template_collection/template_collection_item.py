from sqlalchemy import Column, Integer, String, DateTime, ForeignKey, SmallInteger, Boolean, Text, UniqueConstraint
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from app.db.base_class import Base


class TemplateCollectionItem(Base):
    """模板集合项关联表
    
    建立模板集合与训练模板之间的多对多关系。
    每个集合项表示集合中的一个模板，包含在集合中的特定属性。
    """
    __tablename__ = "template_collection_items"

    # 基础字段
    id = Column(Integer, primary_key=True, index=True)
    collection_id = Column(Integer, ForeignKey("template_collections.id"), nullable=False, comment="集合ID")
    template_id = Column(Integer, ForeignKey("workout_templates.id"), nullable=False, comment="模板ID")
    
    # 在集合中的属性
    order_index = Column(SmallInteger, nullable=False, default=1, comment="在集合中的顺序")
    day_label = Column(String(50), nullable=True, comment="天数标签，如'Day 1'、'第一天'")
    day_of_week = Column(SmallInteger, nullable=True, comment="建议的星期几 1-7")
    is_rest_day = Column(Boolean, default=False, comment="是否为休息日")
    
    # 集合特定的自定义
    custom_name = Column(String(255), nullable=True, comment="在此集合中的自定义名称")
    custom_description = Column(Text, nullable=True, comment="在此集合中的自定义描述")
    custom_notes = Column(Text, nullable=True, comment="特殊说明")
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    
    # 关系定义
    collection = relationship("TemplateCollection", back_populates="collection_items")
    template = relationship("WorkoutTemplate", back_populates="collection_items")
    
    # 唯一约束
    __table_args__ = (
        UniqueConstraint('collection_id', 'template_id', name='unique_collection_template'),
        UniqueConstraint('collection_id', 'order_index', name='unique_collection_order'),
    )

    def __repr__(self):
        return f"<TemplateCollectionItem collection_id={self.collection_id} template_id={self.template_id} order={self.order_index}>"
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            "id": self.id,
            "collection_id": self.collection_id,
            "template_id": self.template_id,
            "order_index": self.order_index,
            "day_label": self.day_label,
            "day_of_week": self.day_of_week,
            "is_rest_day": self.is_rest_day,
            "custom_name": self.custom_name,
            "custom_description": self.custom_description,
            "custom_notes": self.custom_notes,
            "created_at": self.created_at.isoformat() if self.created_at else None,
        } 