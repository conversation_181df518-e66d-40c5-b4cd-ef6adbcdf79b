from sqlalchemy import Column, Integer, String, DateTime, ForeignKey, Text, SmallInteger, ARRAY, Boolean, Float
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from app.db.base_class import Base


class TemplateCollection(Base):
    """训练模板集合表
    
    用于管理训练模板的集合，如"4天分化训练"、"初学者训练计划"等。
    一个集合可以包含多个相关的训练模板。
    """
    __tablename__ = "template_collections"

    # 基础字段
    id = Column(Integer, primary_key=True, index=True)
    creator_id = Column(Integer, ForeignKey("users.id"), nullable=False, index=True)
    title = Column(String(255), nullable=False, comment="集合标题")
    description = Column(Text, nullable=True, comment="集合详细描述")
    
    # 集合属性
    level = Column(SmallInteger, nullable=True, comment="难度等级 1-5")
    fitness_goals = Column(ARRAY(Integer), nullable=True, comment="健身目标ID列表")
    duration_days = Column(SmallInteger, nullable=True, comment="总训练天数")
    sessions_per_week = Column(SmallInteger, nullable=True, comment="每周训练次数")
    estimated_duration_per_session = Column(SmallInteger, nullable=True, comment="每次训练时长（分钟）")
    
    # 媒体和展示
    cover_image_url = Column(String(500), nullable=True, comment="封面图片URL")
    preview_images = Column(ARRAY(String), nullable=True, comment="预览图片列表")
    color_theme = Column(String(20), nullable=True, default="#557B86", comment="主题颜色")
    
    # 可见性和权限
    visibility = Column(String(20), nullable=False, default="private", comment="可见性：private/public/unlisted")
    is_featured = Column(Boolean, default=False, comment="是否为精选集合")
    is_official = Column(Boolean, default=False, comment="是否为官方集合")
    
    # 统计数据
    download_count = Column(Integer, default=0, comment="下载次数")
    view_count = Column(Integer, default=0, comment="查看次数")
    like_count = Column(Integer, default=0, comment="点赞次数")
    rating_average = Column(Float, default=0.0, comment="平均评分")
    rating_count = Column(Integer, default=0, comment="评分次数")
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), comment="更新时间")
    published_at = Column(DateTime(timezone=True), nullable=True, comment="发布时间")
    
    # 关系定义
    creator = relationship("User", back_populates="created_collections")
    collection_items = relationship(
        "TemplateCollectionItem", 
        back_populates="collection", 
        cascade="all, delete-orphan",
        order_by="TemplateCollectionItem.order_index"
    )
    ratings = relationship(
        "TemplateCollectionRating", 
        back_populates="collection", 
        cascade="all, delete-orphan"
    )
    usage_stats = relationship(
        "TemplateCollectionUsage", 
        back_populates="collection", 
        cascade="all, delete-orphan"
    )

    def __repr__(self):
        return f"<TemplateCollection {self.id}: {self.title}>"
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            "id": self.id,
            "creator_id": self.creator_id,
            "title": self.title,
            "description": self.description,
            "level": self.level,
            "fitness_goals": self.fitness_goals,
            "duration_days": self.duration_days,
            "sessions_per_week": self.sessions_per_week,
            "estimated_duration_per_session": self.estimated_duration_per_session,
            "cover_image_url": self.cover_image_url,
            "preview_images": self.preview_images,
            "color_theme": self.color_theme,
            "visibility": self.visibility,
            "is_featured": self.is_featured,
            "is_official": self.is_official,
            "download_count": self.download_count,
            "view_count": self.view_count,
            "like_count": self.like_count,
            "rating_average": self.rating_average,
            "rating_count": self.rating_count,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
            "published_at": self.published_at.isoformat() if self.published_at else None,
        } 