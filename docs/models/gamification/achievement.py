from sqlalchemy import Column, Integer, String, DateTime, ForeignKey, Boolean, Text, Enum
from sqlalchemy.orm import relationship
from datetime import datetime
import enum

from app.db.base_class import Base


class Achievement(Base):
    """成就模型，定义用户可获得的成就"""
    __tablename__ = "achievements"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, nullable=False)
    description = Column(Text)
    image_url = Column(String)
    category = Column(String, nullable=False)  # milestone(里程碑), challenge(挑战), collection(收藏), social(社交), combined(综合)
    
    # 成就要求
    requirement_type = Column(String, nullable=False)  # workout_count(训练次数), food_record_count(饮食记录), exercise_level(运动等级), etc.
    requirement_value = Column(Integer, nullable=False)  # 达成要求的数值
    
    # 奖励
    currency_reward = Column(Integer, default=0)  # 像素杠铃奖励
    experience_reward_type = Column(String, nullable=True)  # exercise(运动经验), diet(饮食经验), both(两者都有)
    experience_reward_value = Column(Integer, default=0)  # 经验值奖励
    card_reward_id = Column(Integer, ForeignKey("cards.id"), nullable=True)  # 卡片奖励
    
    # 其他属性
    is_active = Column(Boolean, default=True)
    is_hidden = Column(Boolean, default=False)  # 是否隐藏成就（惊喜机制）
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # 关系
    card_reward = relationship("Card")
    user_achievements = relationship("UserAchievement", back_populates="achievement")


class UserAchievement(Base):
    """用户成就模型，记录用户的成就进度和完成情况"""
    __tablename__ = "user_achievements"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), index=True)
    achievement_id = Column(Integer, ForeignKey("achievements.id"), index=True)
    progress = Column(Integer, default=0)  # 当前进度
    completed = Column(Boolean, default=False)  # 是否完成
    completed_at = Column(DateTime, nullable=True)  # 完成时间
    reward_claimed = Column(Boolean, default=False)  # 是否已领取奖励
    
    # 关系
    user = relationship("User", back_populates="achievements")
    achievement = relationship("Achievement", back_populates="user_achievements")


class Milestone(Base):
    """里程碑模型，记录长期留存的里程碑奖励"""
    __tablename__ = "milestones"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, nullable=False)
    description = Column(Text)
    days_required = Column(Integer, nullable=False)  # 需要活跃的天数
    
    # 奖励
    currency_reward = Column(Integer, default=0)  # 像素杠铃奖励
    experience_reward_type = Column(String, nullable=True)  # exercise(运动经验), diet(饮食经验), both(两者都有)
    experience_reward_value = Column(Integer, default=0)  # 经验值奖励
    card_reward_id = Column(Integer, ForeignKey("cards.id"), nullable=True)  # 卡片奖励
    
    # 其他属性
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # 关系
    card_reward = relationship("Card")
    user_milestones = relationship("UserMilestone", back_populates="milestone")


class UserMilestone(Base):
    """用户里程碑模型，记录用户的里程碑进度和完成情况"""
    __tablename__ = "user_milestones"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), index=True)
    milestone_id = Column(Integer, ForeignKey("milestones.id"), index=True)
    progress = Column(Integer, default=0)  # 当前进度（天数）
    last_active_date = Column(DateTime, default=datetime.utcnow)  # 上次活跃日期
    completed = Column(Boolean, default=False)  # 是否完成
    completed_at = Column(DateTime, nullable=True)  # 完成时间
    reward_claimed = Column(Boolean, default=False)  # 是否已领取奖励
    
    # 关系
    user = relationship("User", back_populates="milestones")
    milestone = relationship("Milestone", back_populates="user_milestones") 