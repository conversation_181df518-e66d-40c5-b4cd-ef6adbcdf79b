# 导入所有游戏化系统模型
from app.models.gamification.level import UserLevel, UserAttribute, UserTitle
from app.models.gamification.card import Card, CardType, UserCard, CardSynthesisRecipe, CardSynthesisIngredient
from app.models.gamification.currency import Currency, CurrencyTransaction, ShopItem, UserPurchase
from app.models.gamification.achievement import Achievement, UserAchievement, Milestone, UserMilestone
from app.models.gamification.task import Task, TaskType, TaskCategory, UserTask, DailyCheckIn

# 模型列表，用于数据库迁移
__all__ = [
    "UserLevel", "UserAttribute", "UserTitle",
    "Card", "CardType", "UserCard", "CardSynthesisRecipe", "CardSynthesisIngredient",
    "Currency", "CurrencyTransaction", "ShopItem", "UserPurchase",
    "Achievement", "UserAchievement", "Milestone", "UserMilestone",
    "Task", "TaskType", "TaskCategory", "UserTask", "DailyCheckIn"
]
