from sqlalchemy import Column, Integer, String, DateTime, ForeignKey, <PERSON><PERSON>an, Enum, Text
from sqlalchemy.orm import relationship
from datetime import datetime
import enum

from app.db.base_class import Base


class CardType(enum.Enum):
    """卡片类型枚举"""
    FOOD = "food"  # 食物卡片
    EQUIPMENT = "equipment"  # 装备卡片
    SPECIAL = "special"  # 特殊卡片


class Card(Base):
    """卡片基础模型，定义不同类型的卡片"""
    __tablename__ = "cards"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, index=True, nullable=False)
    description = Column(Text)
    image_url = Column(String)
    card_type = Column(Enum(CardType), nullable=False)
    rarity = Column(Integer, default=1)  # 稀有度：1-5星级
    
    # 属性加成
    # 运动属性
    strength_bonus = Column(Integer, default=0)
    endurance_bonus = Column(Integer, default=0)
    flexibility_bonus = Column(Integer, default=0)
    # 饮食属性
    nutrition_knowledge_bonus = Column(Integer, default=0)
    cooking_skill_bonus = Column(Integer, default=0)
    diet_planning_bonus = Column(Integer, default=0)
    
    # 其他属性
    duration_hours = Column(Integer, nullable=True)  # 效果持续时间（小时），null表示永久
    is_active = Column(Boolean, default=True)  # 卡片是否可获取
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # 关系
    user_cards = relationship("UserCard", back_populates="card")


class UserCard(Base):
    """用户卡片模型，记录用户拥有的卡片"""
    __tablename__ = "user_cards"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), index=True)
    card_id = Column(Integer, ForeignKey("cards.id"), index=True)
    quantity = Column(Integer, default=1)  # 拥有数量
    is_equipped = Column(Boolean, default=False)  # 是否已装备
    obtained_at = Column(DateTime, default=datetime.utcnow)
    expires_at = Column(DateTime, nullable=True)  # 过期时间，null表示永不过期
    
    # 关系
    user = relationship("User", back_populates="cards")
    card = relationship("Card", back_populates="user_cards")


class CardSynthesisRecipe(Base):
    """卡片合成配方，定义如何合成高级卡片"""
    __tablename__ = "card_synthesis_recipes"
    
    id = Column(Integer, primary_key=True, index=True)
    result_card_id = Column(Integer, ForeignKey("cards.id"), index=True)
    description = Column(Text)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # 关系
    result_card = relationship("Card")
    ingredients = relationship("CardSynthesisIngredient", back_populates="recipe")


class CardSynthesisIngredient(Base):
    """卡片合成材料，定义合成配方中需要的材料"""
    __tablename__ = "card_synthesis_ingredients"
    
    id = Column(Integer, primary_key=True, index=True)
    recipe_id = Column(Integer, ForeignKey("card_synthesis_recipes.id"), index=True)
    card_id = Column(Integer, ForeignKey("cards.id"), index=True)
    quantity = Column(Integer, default=1)  # 需要的数量
    
    # 关系
    recipe = relationship("CardSynthesisRecipe", back_populates="ingredients")
    card = relationship("Card") 