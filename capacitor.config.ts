import { CapacitorConfig } from '@capacitor/cli';

const config: CapacitorConfig = {
  appId: 'com.fitmaster.app',
  appName: 'FitMaster',
  webDir: 'dist',
  server: {
    androidScheme: 'https',
    // 允许导航到外部URL
    allowNavigation: [
      '**************:8000',
      'localhost:*'
    ]
  },
  plugins: {
    // 🔧 状态栏插件 - 优化Safe Area初始化
    StatusBar: {
      overlaysWebView: false, // 不覆盖WebView，确保Safe Area正确计算
      style: 'default', // 设置默认样式，避免初始化延迟
      backgroundColor: '#ffffff' // 设置背景色，提升初始化速度
    },
    
    // 启动屏插件配置
    SplashScreen: {
      launchShowDuration: 2000,
      launchAutoHide: true,
      backgroundColor: "#ffffff",
      androidSplashResourceName: "splash",
      androidScaleType: "CENTER_CROP",
      showSpinner: false,
      androidSpinnerStyle: "large",
      iosSpinnerStyle: "small",
      spinnerColor: "#3b82f6",
      splashFullScreen: true,
      splashImmersive: true,
    },
    
    // CapacitorHttp插件配置 - 改善网络请求
    CapacitorHttp: {
      enabled: true
    }
  },
  
  // 🔧 iOS特定配置 - 优化Safe Area初始化
  ios: {
    // Safe Area和WebView配置
    contentInset: 'automatic', // 自动处理Safe Area
    backgroundColor: '#ffffff',

    // 自定义用户代理
    appendUserAgent: 'FitMaster/1.0',

    // 🔧 WebView配置优化 - 提升Safe Area初始化速度
    webContentsDebuggingEnabled: true,
    limitsNavigationsToAppBoundDomains: false, // 允许导航，避免限制影响初始化

    // 🔧 网络和渲染优化
    scheme: 'capacitor'
  },
  
  // Android特定配置
  android: {
    // WebView最小版本
    minWebViewVersion: 60,
    
    // 允许混合内容
    allowMixedContent: true,
    
    // 构建选项
    buildOptions: {
      keystorePath: undefined,
      keystorePassword: undefined,
      keystoreAlias: undefined,
      keystoreAliasPassword: undefined,
      releaseType: 'AAB',
      signingType: 'jarsigner'
    }
  }
};

export const CAPACITOR_CONFIG = {
  appId: 'com.fitmaster.app',
  appName: 'FitMaster',
  webDir: 'dist',
  server: {
    // 修复：使用HTTP协议匹配服务器配置
    url: 'http://**************:8000',
    // 允许明文HTTP连接
    cleartext: true
  }
} as const;

export default config; 