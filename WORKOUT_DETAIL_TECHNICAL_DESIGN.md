# 🏗️ WorkoutDetail 页面技术方案设计

## 📋 项目概述

基于前期需求分析，本文档提供 WorkoutDetail 页面的完整技术实现方案，包含组件架构、数据流设计、文件结构和详细的代码实现指南。

**核心目标**：
- 支持两种业务场景（Feed社交 + Profile纯数据）
- 遵循 FitMaster iOS优先开发规范
- 最大化复用现有组件和基础设施
- 提供原生级别的用户体验

---

## 🏗️ 组件架构设计

### 1. 整体架构图

```mermaid
graph TD
    A[WorkoutDetailPage] --> B[WorkoutHeader]
    A --> C[ExerciseList]
    A --> D[MuscleDetailSection]
    
    B --> E[SocialSection]
    B --> F[WorkoutStatsCard]
    
    C --> G[TrainingExerciseCard]
    
    D --> H[MuscleInfoCard]
    
    I[useWorkoutDetail] --> A
    J[WorkoutDetailTransformer] --> I
    K[CommunityService] --> I
    L[WorkoutService] --> I
    
    style A fill:#e1f5fe
    style I fill:#f3e5f5
    style J fill:#fff3e0
```

### 2. 组件层级结构

```
WorkoutDetailPage/
├── WorkoutHeader/                    # 页面头部区域
│   ├── SocialSection/               # 社交信息（仅Feed场景）
│   │   ├── UserAvatar              # 用户头像
│   │   ├── UserInfo                # 用户名称、关注状态
│   │   └── PostContent             # 帖子文本内容
│   └── WorkoutStatsCard/           # 训练统计卡片（复用）
├── ExerciseList/                    # 动作列表区域
│   └── TrainingExerciseCard/       # 训练动作卡片（新建）
│       ├── ExerciseHeader          # 动作基本信息
│       ├── SetRecords              # 组记录详情
│       └── ExerciseNotes           # 动作备注
└── MuscleDetailSection/            # 肌肉详情区域
    └── MuscleInfoCard/             # 肌肉信息卡片（复用）
```

### 3. 核心组件职责

#### WorkoutDetailPage (主页面)
- **职责**: 路由参数解析、状态管理、数据获取协调
- **数据**: 通过 useWorkoutDetail Hook 获取统一的 UIWorkoutDetail
- **渲染**: 根据 source 字段条件渲染不同的UI区域

#### useWorkoutDetail (核心Hook)
- **职责**: 数据获取、状态管理、业务逻辑封装
- **输入**: 数据源类型和ID、初始数据（可选）
- **输出**: 统一的 UIWorkoutDetail、加载状态、错误状态、交互方法

#### WorkoutDetailTransformer (数据转换器)
- **职责**: API数据到UI数据的转换、肌肉数据聚合
- **转换**: ApiPostDetail/ApiWorkoutResponse → UIWorkoutDetail
- **聚合**: 从动作列表聚合目标肌肉和协同肌肉数据

#### TrainingExerciseCard (新建组件)
- **职责**: 展示单个训练动作的详细记录
- **功能**: 展开/折叠、组记录展示、重量次数统计
- **交互**: iOS原生触摸反馈、流畅动画

---

## 📊 数据流设计

### 1. 数据流架构图

```mermaid
sequenceDiagram
    participant U as User
    participant F as FeedPage/ProfilePage
    participant W as WorkoutDetailPage
    participant H as useWorkoutDetail
    participant T as Transformer
    participant S as Service
    participant C as Cache

    U->>F: 点击训练记录
    F->>W: navigate(路由+参数)
    W->>H: 调用Hook(dataSource)
    
    alt Feed场景 (postId)
        H->>S: CommunityService.getPostDetail(postId)
        S->>C: 检查缓存
        C-->>S: 返回缓存/未命中
        S-->>H: ApiPostDetail
        H->>T: transformPostDetail()
    else Profile场景 (workoutId)
        H->>S: WorkoutService.getWorkoutDetail(workoutId)
        S->>C: 检查缓存
        C-->>S: 返回缓存/未命中
        S-->>H: ApiWorkoutResponse
        H->>T: transformWorkoutResponse()
    end
    
    T->>T: 聚合肌肉数据
    T-->>H: UIWorkoutDetail
    H-->>W: 返回统一数据
    W-->>U: 渲染页面
```

### 2. 数据模型设计

#### UIWorkoutDetail (统一UI数据模型)
```typescript
interface UIWorkoutDetail {
  // 基础信息
  id: string;
  name: string;
  source: 'feed' | 'profile';
  
  // 社交信息（仅feed场景）
  socialInfo?: {
    user: {
      id: string;
      name: string;
      avatar: string;
      isFollowing: boolean;
      isVerified: boolean;
    };
    post: {
      id: string;
      content: string;
      timestamp: string;
      images?: string[];
    };
    stats: {
      likes: number;
      comments: number;
      views: number;
      isLiked: boolean;
    };
  };
  
  // 训练统计（共用）
  workoutStats: WorkoutData;
  
  // 训练动作（共用）
  exercises: UITrainingExercise[];
  
  // 肌肉数据（共用）
  muscleData: {
    targetMuscleIds: number[];
    synergistMuscleIds: number[];
  };
  
  // 元数据
  metadata: {
    createdAt: string;
    updatedAt: string;
    status: 'completed' | 'in_progress' | 'planned';
    visibility: 'public' | 'private' | 'friends';
  };
}
```

#### UITrainingExercise (训练动作UI模型)
```typescript
interface UITrainingExercise {
  id: string;
  name: string;
  imageUrl?: string;
  description?: string;
  order: number;
  
  // 基础参数
  sets: number;
  targetReps: number;
  targetWeight: number;
  restSeconds: number;
  
  // 执行记录
  setRecords: UISetRecord[];
  
  // 统计信息
  totalVolume: number;      // 总重量
  completedSets: number;    // 完成组数
  averageWeight: number;    // 平均重量
  
  // 其他信息
  notes?: string;
  exerciseType: 'weight_reps' | 'time' | 'distance';
  superset?: string;
}

interface UISetRecord {
  id: string;
  setNumber: number;
  setType: 'normal' | 'warmup' | 'dropset' | 'failure';
  weight: number;
  reps: number;
  completed: boolean;
  notes?: string;
  duration?: number;  // 秒，用于时间类动作
  distance?: number;  // 米，用于距离类动作
}
```

### 3. 数据转换逻辑

#### 核心转换器实现
```typescript
// src/models/transformers/workout/WorkoutDetailTransformer.ts

export class WorkoutDetailTransformer {
  /**
   * 将Feed帖子详情转换为UI模型
   */
  static transformPostDetailToUIModel(
    apiPost: ApiPostDetail
  ): UIWorkoutDetail {
    const workoutData = apiPost.related_workout_detail;
    
    return {
      id: apiPost.id.toString(),
      name: workoutData.name,
      source: 'feed',
      
      socialInfo: {
        user: {
          id: apiPost.user.id.toString(),
          name: apiPost.user.nickname,
          avatar: apiPost.user.avatar_url,
          isFollowing: false, // 需要从其他接口获取
          isVerified: false   // 需要从其他接口获取
        },
        post: {
          id: apiPost.id.toString(),
          content: apiPost.content,
          timestamp: apiPost.created_at,
          images: apiPost.image_urls || []
        },
        stats: {
          likes: apiPost.like_count,
          comments: apiPost.comment_count,
          views: apiPost.view_count,
          isLiked: apiPost.is_liked_by_current_user
        }
      },
      
      workoutStats: this.transformToWorkoutData(workoutData),
      exercises: this.transformExercises(workoutData.workout_exercises),
      muscleData: this.aggregateMuscleData(workoutData.workout_exercises),
      
      metadata: {
        createdAt: workoutData.created_at,
        updatedAt: workoutData.updated_at,
        status: workoutData.status,
        visibility: apiPost.status === 'ACTIVE' ? 'public' : 'private'
      }
    };
  }
  
  /**
   * 将训练详情响应转换为UI模型
   */
  static transformWorkoutResponseToUIModel(
    apiWorkout: ApiWorkoutResponse
  ): UIWorkoutDetail {
    return {
      id: apiWorkout.id.toString(),
      name: apiWorkout.name,
      source: 'profile',
      
      // profile场景不包含社交信息
      socialInfo: undefined,
      
      workoutStats: this.transformToWorkoutData(apiWorkout),
      exercises: this.transformExercises(apiWorkout.workout_exercises),
      muscleData: this.aggregateMuscleData(apiWorkout.workout_exercises),
      
      metadata: {
        createdAt: apiWorkout.created_at,
        updatedAt: apiWorkout.updated_at,
        status: apiWorkout.status,
        visibility: 'private' // Profile中的训练默认私有
      }
    };
  }
  
  /**
   * 聚合肌肉数据
   */
  private static aggregateMuscleData(
    exercises: ApiWorkoutExercise[]
  ): { targetMuscleIds: number[], synergistMuscleIds: number[] } {
    const targetMuscles = new Set<number>();
    const synergistMuscles = new Set<number>();
    
    exercises.forEach(exercise => {
      // 使用现有的肌肉推断逻辑
      const muscleGroups = inferMuscleGroupsFromExerciseName(exercise.exercise_name);
      
      muscleGroups.primary.forEach(id => targetMuscles.add(id));
      muscleGroups.secondary.forEach(id => synergistMuscles.add(id));
    });
    
    // 应用智能映射
    const { adjustedPrimary, adjustedSecondary } = smartMuscleMappingForExercise(
      Array.from(targetMuscles),
      Array.from(synergistMuscles)
    );
    
    return {
      targetMuscleIds: adjustedPrimary,
      synergistMuscleIds: adjustedSecondary.filter(id => !adjustedPrimary.includes(id))
    };
  }
  
  /**
   * 转换动作列表
   */
  private static transformExercises(
    apiExercises: ApiWorkoutExercise[]
  ): UITrainingExercise[] {
    return apiExercises.map(exercise => ({
      id: exercise.id.toString(),
      name: exercise.exercise_name,
      imageUrl: exercise.exercise_image,
      description: exercise.exercise_description,
      order: exercise.order,
      
      sets: exercise.sets,
      targetReps: this.parseReps(exercise.reps),
      targetWeight: exercise.weight || 0,
      restSeconds: exercise.rest_seconds,
      
      setRecords: exercise.set_records.map(record => ({
        id: record.id.toString(),
        setNumber: record.set_number,
        setType: record.set_type,
        weight: record.weight,
        reps: record.reps,
        completed: record.completed,
        notes: record.notes
      })),
      
      totalVolume: exercise.set_records.reduce(
        (sum, record) => sum + (record.weight * record.reps), 0
      ),
      completedSets: exercise.set_records.filter(r => r.completed).length,
      averageWeight: exercise.set_records.length > 0 
        ? exercise.set_records.reduce((sum, r) => sum + r.weight, 0) / exercise.set_records.length
        : 0,
      
      notes: exercise.notes,
      exerciseType: exercise.exercise_type
    }));
  }
}
```

---

## 🎯 Hook设计

### useWorkoutDetail 核心Hook实现

```typescript
// src/pages/WorkoutDetailPage/hooks/useWorkoutDetail.ts

interface DataSource {
  type: 'feed' | 'profile';
  id: string;
}

interface UseWorkoutDetailResult {
  workoutDetail: UIWorkoutDetail | null;
  loading: boolean;
  error: string | null;
  
  // 交互状态
  expandedExercises: string[];
  
  // 交互方法
  toggleExerciseExpansion: (exerciseId: string) => void;
  handleSocialAction: (action: SocialAction) => Promise<void>;
  refreshData: () => Promise<void>;
}

export const useWorkoutDetail = (
  dataSource: DataSource,
  initialData?: any
): UseWorkoutDetailResult => {
  const [workoutDetail, setWorkoutDetail] = useState<UIWorkoutDetail | null>(null);
  const [loading, setLoading] = useState(!initialData);
  const [error, setError] = useState<string | null>(null);
  const [expandedExercises, setExpandedExercises] = useState<string[]>([]);
  
  // 数据获取
  const fetchWorkoutDetail = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      
      let result: UIWorkoutDetail;
      
      if (dataSource.type === 'feed') {
        const apiPost = await CommunityService.getPostDetail(dataSource.id);
        result = WorkoutDetailTransformer.transformPostDetailToUIModel(apiPost);
      } else {
        const apiWorkout = await workoutService.getWorkoutDetail(dataSource.id);
        if (!apiWorkout.success) {
          throw new Error(apiWorkout.error?.message || '获取训练详情失败');
        }
        result = WorkoutDetailTransformer.transformWorkoutResponseToUIModel(apiWorkout.data);
      }
      
      setWorkoutDetail(result);
      
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '未知错误';
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  }, [dataSource]);
  
  // 初始化数据获取
  useEffect(() => {
    if (initialData) {
      // 使用初始数据，避免重复网络请求
      try {
        const result = dataSource.type === 'feed'
          ? WorkoutDetailTransformer.transformPostDetailToUIModel(initialData)
          : WorkoutDetailTransformer.transformWorkoutResponseToUIModel(initialData);
        
        setWorkoutDetail(result);
        setLoading(false);
      } catch (err) {
        console.error('初始数据转换失败:', err);
        fetchWorkoutDetail(); // 降级到网络请求
      }
    } else {
      fetchWorkoutDetail();
    }
  }, [dataSource, initialData, fetchWorkoutDetail]);
  
  // 动作展开/折叠
  const toggleExerciseExpansion = useCallback((exerciseId: string) => {
    setExpandedExercises(prev => 
      prev.includes(exerciseId)
        ? prev.filter(id => id !== exerciseId)
        : [...prev, exerciseId]
    );
  }, []);
  
  // 社交操作处理
  const handleSocialAction = useCallback(async (action: SocialAction) => {
    if (!workoutDetail?.socialInfo) return;
    
    try {
      switch (action.type) {
        case 'like':
          await CommunityService.togglePostLike(workoutDetail.socialInfo.post.id);
          // 乐观更新
          setWorkoutDetail(prev => prev ? {
            ...prev,
            socialInfo: {
              ...prev.socialInfo!,
              stats: {
                ...prev.socialInfo!.stats,
                isLiked: !prev.socialInfo!.stats.isLiked,
                likes: prev.socialInfo!.stats.isLiked 
                  ? prev.socialInfo!.stats.likes - 1
                  : prev.socialInfo!.stats.likes + 1
              }
            }
          } : null);
          break;
          
        case 'follow':
          // TODO: 实现关注逻辑
          break;
          
        case 'share':
          // TODO: 实现分享逻辑
          break;
      }
    } catch (err) {
      console.error('社交操作失败:', err);
      // 错误处理和状态回滚
    }
  }, [workoutDetail]);
  
  // 刷新数据
  const refreshData = useCallback(async () => {
    await fetchWorkoutDetail();
  }, [fetchWorkoutDetail]);
  
  return {
    workoutDetail,
    loading,
    error,
    expandedExercises,
    toggleExerciseExpansion,
    handleSocialAction,
    refreshData
  };
};
```

---

## 🎨 组件实现设计

### 1. TrainingExerciseCard 组件

```typescript
// src/pages/WorkoutDetailPage/components/ExerciseCard/TrainingExerciseCard.tsx

interface TrainingExerciseCardProps {
  exercise: UITrainingExercise;
  isExpanded: boolean;
  onToggleExpansion: () => void;
  className?: string;
}

export const TrainingExerciseCard: React.FC<TrainingExerciseCardProps> = ({
  exercise,
  isExpanded,
  onToggleExpansion,
  className = ''
}) => {
  const { provideiOSTouchFeedback } = useCapacitorFeatures();
  
  const handleToggle = useCallback(() => {
    provideiOSTouchFeedback();
    onToggleExpansion();
  }, [onToggleExpansion, provideiOSTouchFeedback]);
  
  return (
    <div className={`training-exercise-card ${isExpanded ? 'expanded' : ''} ${className}`}>
      {/* 练习头部 */}
      <div className="exercise-header" onClick={handleToggle}>
        <div className="exercise-main-info">
          <h3 className="exercise-name">{exercise.name}</h3>
          <div className="exercise-summary">
            <span className="sets-reps">
              {exercise.completedSets}/{exercise.sets}组 × {exercise.targetReps}次
            </span>
            {exercise.totalVolume > 0 && (
              <span className="volume">
                {exercise.totalVolume}kg
              </span>
            )}
          </div>
        </div>
        
        <div className="exercise-actions">
          <div className="completion-badge">
            {exercise.completedSets === exercise.sets ? (
              <CheckCircleIcon className="completed" />
            ) : (
              <div className="progress-ring">
                <span>{exercise.completedSets}/{exercise.sets}</span>
              </div>
            )}
          </div>
          
          <button className="expand-button" aria-label={isExpanded ? '收起' : '展开'}>
            <ChevronDownIcon className={isExpanded ? 'rotated' : ''} />
          </button>
        </div>
      </div>
      
      {/* 练习详情 */}
      <div className={`exercise-details ${isExpanded ? 'expanded' : 'collapsed'}`}>
        <div className="set-records-container">
          <div className="set-records-header">
            <span>组</span>
            <span>重量</span>
            <span>次数</span>
            <span>状态</span>
          </div>
          
          <div className="set-records-list">
            {exercise.setRecords.map((record, index) => (
              <div 
                key={record.id} 
                className={`set-record ${record.completed ? 'completed' : 'pending'}`}
              >
                <span className="set-number">{record.setNumber}</span>
                <span className="weight">{record.weight}kg</span>
                <span className="reps">{record.reps}</span>
                <span className="status">
                  {record.completed ? (
                    <CheckIcon className="check-icon" />
                  ) : (
                    <ClockIcon className="pending-icon" />
                  )}
                </span>
              </div>
            ))}
          </div>
        </div>
        
        {/* 练习统计 */}
        <div className="exercise-stats">
          <div className="stat-item">
            <span className="label">平均重量</span>
            <span className="value">{exercise.averageWeight.toFixed(1)}kg</span>
          </div>
          <div className="stat-item">
            <span className="label">休息时间</span>
            <span className="value">{exercise.restSeconds}s</span>
          </div>
          <div className="stat-item">
            <span className="label">总重量</span>
            <span className="value">{exercise.totalVolume}kg</span>
          </div>
        </div>
        
        {/* 练习备注 */}
        {exercise.notes && (
          <div className="exercise-notes">
            <h4>备注</h4>
            <p>{exercise.notes}</p>
          </div>
        )}
      </div>
    </div>
  );
};
```

### 2. WorkoutDetailPage 主页面

```typescript
// src/pages/WorkoutDetailPage/WorkoutDetailPage.tsx

const WorkoutDetailPage: React.FC = () => {
  const { postId, workoutId } = useParams<{ postId?: string; workoutId?: string }>();
  const location = useLocation();
  const navigate = useNavigate();
  
  // 确定数据源
  const dataSource = useMemo(() => {
    if (postId) return { type: 'feed' as const, id: postId };
    if (workoutId) return { type: 'profile' as const, id: workoutId };
    throw new Error('Invalid route parameters');
  }, [postId, workoutId]);
  
  // 使用核心Hook
  const {
    workoutDetail,
    loading,
    error,
    expandedExercises,
    toggleExerciseExpansion,
    handleSocialAction,
    refreshData
  } = useWorkoutDetail(dataSource, location.state?.initialData);
  
  // 返回按钮处理
  const handleGoBack = useCallback(() => {
    navigate(-1);
  }, [navigate]);
  
  // 加载状态
  if (loading) {
    return <WorkoutDetailSkeleton />;
  }
  
  // 错误状态
  if (error) {
    return (
      <ErrorStateComponent 
        error={{ code: 'NETWORK_ERROR', message: error, retryable: true }}
        onRetry={refreshData}
      />
    );
  }
  
  // 数据为空
  if (!workoutDetail) {
    return <NotFoundState />;
  }
  
  return (
    <div className="workout-detail-page">
      {/* 页面头部 */}
      <header className="page-header">
        <button className="back-button" onClick={handleGoBack}>
          <ArrowLeftIcon />
        </button>
        <h1 className="page-title">{workoutDetail.name}</h1>
        <div className="header-actions">
          {workoutDetail.source === 'feed' && (
            <button className="share-button">
              <ShareIcon />
            </button>
          )}
          {workoutDetail.source === 'profile' && (
            <button className="edit-button">
              <EditIcon />
            </button>
          )}
        </div>
      </header>
      
      {/* 内容区域 */}
      <main className="page-content">
        {/* 社交信息区域（仅Feed场景） */}
        {workoutDetail.socialInfo && (
          <SocialSection 
            socialInfo={workoutDetail.socialInfo}
            onSocialAction={handleSocialAction}
          />
        )}
        
        {/* 训练统计 */}
        <section className="workout-stats-section">
          <WorkoutStatsCard
            workout={workoutDetail.workoutStats}
            className="detail-workout-stats"
            showDividers={true}
            stats={['duration', 'weight', 'calories', 'exercises']}
          />
        </section>
        
        {/* 动作列表 */}
        <section className="exercises-section">
          <h2 className="section-title">训练动作</h2>
          <div className="exercises-list">
            {workoutDetail.exercises.map(exercise => (
              <TrainingExerciseCard
                key={exercise.id}
                exercise={exercise}
                isExpanded={expandedExercises.includes(exercise.id)}
                onToggleExpansion={() => toggleExerciseExpansion(exercise.id)}
              />
            ))}
          </div>
        </section>
        
        {/* 肌肉详情 */}
        <section className="muscle-detail-section">
          <h2 className="section-title">目标肌群</h2>
          <MuscleInfoCard
            targetMuscleIds={workoutDetail.muscleData.targetMuscleIds}
            synergistMuscleIds={workoutDetail.muscleData.synergistMuscleIds}
            theme="light"
            className="detail-muscle-card"
          />
        </section>
      </main>
    </div>
  );
};
```

---

## 📁 文件结构设计

### 完整目录结构

```
src/pages/WorkoutDetailPage/
├── WorkoutDetailPage.tsx              # 主页面组件
├── WorkoutDetailPage.scss             # 页面样式
├── index.ts                           # 导出文件
├── components/                        # 页面专用组件
│   ├── SocialSection/                 # 社交信息区域
│   │   ├── SocialSection.tsx
│   │   ├── SocialSection.scss
│   │   └── index.ts
│   ├── TrainingExerciseCard/          # 训练动作卡片
│   │   ├── TrainingExerciseCard.tsx
│   │   ├── TrainingExerciseCard.scss
│   │   └── index.ts
│   ├── WorkoutDetailSkeleton/         # 加载骨架屏
│   │   ├── WorkoutDetailSkeleton.tsx
│   │   ├── WorkoutDetailSkeleton.scss
│   │   └── index.ts
│   └── ErrorStates/                   # 错误状态组件
│       ├── ErrorStateComponent.tsx
│       ├── NotFoundState.tsx
│       ├── ErrorStates.scss
│       └── index.ts
├── hooks/                             # 页面专用Hook
│   ├── useWorkoutDetail.ts            # 核心数据Hook
│   └── index.ts
└── types/                             # 页面专用类型
    ├── WorkoutDetail.types.ts
    └── index.ts

src/models/
├── ui/workout/                        # UI数据模型
│   ├── WorkoutDetail.ts               # 主要UI模型
│   ├── TrainingExercise.ts            # 训练动作模型
│   └── index.ts
└── transformers/workout/              # 数据转换器
    ├── WorkoutDetailTransformer.ts    # 主要转换器
    ├── ExerciseTransformer.ts         # 动作转换器
    └── index.ts

# 需要修改的现有文件
src/App.tsx                            # 添加新路由
src/pages/feed/FeedPage.tsx            # 添加跳转逻辑
src/pages/user-profile/ProfilePage.tsx # 添加跳转逻辑
src/components/fitness/WorkoutStatsCard/WorkoutStatsCard.tsx # 添加点击支持
```

### 路由配置

```typescript
// src/App.tsx 新增路由
<Routes>
  {/* 现有路由... */}
  
  {/* WorkoutDetail 路由 */}
  <Route path="/workout/:postId" element={<WorkoutDetailPage />} />
  <Route path="/workout/detail/:workoutId" element={<WorkoutDetailPage />} />
</Routes>
```

---

## 🎨 样式设计规范

### 1. 主页面样式结构

```scss
// src/pages/WorkoutDetailPage/WorkoutDetailPage.scss

.workout-detail-page {
  // iOS Safe Area 适配
  min-height: 100vh;
  padding-top: calc(var(--safe-area-inset-top) + var(--header-height));
  padding-bottom: calc(var(--safe-area-inset-bottom) + var(--nav-height));
  padding-left: var(--safe-area-inset-left);
  padding-right: var(--safe-area-inset-right);
  
  // iOS滚动优化
  -webkit-overflow-scrolling: touch;
  overscroll-behavior: none;
  
  // 页面进入动画
  animation: slideInFromRight 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  
  .page-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--space-4) var(--space-4) var(--space-3);
    background: var(--bg-surface);
    border-bottom: 1px solid var(--border-color);
    
    .back-button {
      @include ios-touch-target;
      background: none;
      border: none;
      color: var(--accent-500);
      
      &:active {
        transform: scale(0.95);
      }
    }
    
    .page-title {
      font-size: var(--text-lg);
      font-weight: var(--font-bold);
      color: var(--text-primary);
      margin: 0;
    }
    
    .header-actions {
      display: flex;
      gap: var(--space-2);
      
      button {
        @include ios-touch-target;
        background: none;
        border: none;
        color: var(--text-secondary);
        
        &:active {
          transform: scale(0.95);
        }
      }
    }
  }
  
  .page-content {
    padding: var(--space-4);
    
    .section-title {
      font-size: var(--text-lg);
      font-weight: var(--font-bold);
      color: var(--text-primary);
      margin: var(--space-6) 0 var(--space-3);
      
      &:first-child {
        margin-top: 0;
      }
    }
  }
}

// iOS专用样式混入
@mixin ios-touch-target {
  min-width: var(--ios-touch-target); // 44px
  min-height: var(--ios-touch-target);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 22px;
  cursor: pointer;
  transition: transform 0.1s cubic-bezier(0.4, 0, 0.2, 1);
  
  &:active {
    transform: scale(0.98);
  }
}

// 页面转场动画
@keyframes slideInFromRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}
```

### 2. TrainingExerciseCard 样式

```scss
// src/pages/WorkoutDetailPage/components/TrainingExerciseCard/TrainingExerciseCard.scss

.training-exercise-card {
  background: var(--bg-surface);
  border-radius: 12px;
  border: 1px solid var(--border-color);
  margin-bottom: var(--space-4);
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  
  &.expanded {
    border-color: var(--accent-200);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
  
  .exercise-header {
    @include ios-touch-target;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--space-4);
    cursor: pointer;
    
    &:active {
      background: var(--bg-hover);
    }
    
    .exercise-main-info {
      flex: 1;
      
      .exercise-name {
        font-size: var(--text-base);
        font-weight: var(--font-semibold);
        color: var(--text-primary);
        margin: 0 0 var(--space-1);
      }
      
      .exercise-summary {
        display: flex;
        gap: var(--space-3);
        
        .sets-reps {
          font-size: var(--text-sm);
          color: var(--text-secondary);
        }
        
        .volume {
          font-size: var(--text-sm);
          color: var(--accent-500);
          font-weight: var(--font-medium);
        }
      }
    }
    
    .exercise-actions {
      display: flex;
      align-items: center;
      gap: var(--space-3);
      
      .completion-badge {
        .completed {
          color: var(--success-500);
        }
        
        .progress-ring {
          width: 32px;
          height: 32px;
          border-radius: 50%;
          border: 2px solid var(--accent-200);
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: var(--text-xs);
          font-weight: var(--font-medium);
          color: var(--accent-500);
        }
      }
      
      .expand-button {
        @include ios-touch-target;
        width: 32px;
        height: 32px;
        background: none;
        border: none;
        color: var(--text-secondary);
        
        .rotated {
          transform: rotate(180deg);
        }
      }
    }
  }
  
  .exercise-details {
    border-top: 1px solid var(--border-color);
    overflow: hidden;
    transition: max-height 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    
    &.collapsed {
      max-height: 0;
    }
    
    &.expanded {
      max-height: 500px; // 根据内容调整
    }
    
    .set-records-container {
      padding: var(--space-4);
      
      .set-records-header {
        display: grid;
        grid-template-columns: 1fr 1fr 1fr 1fr;
        gap: var(--space-2);
        padding-bottom: var(--space-2);
        border-bottom: 1px solid var(--border-color);
        margin-bottom: var(--space-3);
        
        span {
          font-size: var(--text-sm);
          font-weight: var(--font-medium);
          color: var(--text-secondary);
          text-align: center;
        }
      }
      
      .set-record {
        display: grid;
        grid-template-columns: 1fr 1fr 1fr 1fr;
        gap: var(--space-2);
        padding: var(--space-2) 0;
        border-bottom: 1px solid var(--border-light);
        
        &:last-child {
          border-bottom: none;
        }
        
        &.completed {
          background: var(--success-50);
          border-radius: 6px;
          padding: var(--space-2);
        }
        
        span {
          font-size: var(--text-sm);
          text-align: center;
          
          &.weight, &.reps {
            font-weight: var(--font-medium);
            color: var(--text-primary);
          }
          
          &.set-number {
            color: var(--text-secondary);
          }
        }
        
        .check-icon {
          color: var(--success-500);
        }
        
        .pending-icon {
          color: var(--text-muted);
        }
      }
    }
    
    .exercise-stats {
      display: flex;
      justify-content: space-around;
      padding: var(--space-4);
      background: var(--bg-secondary);
      
      .stat-item {
        text-align: center;
        
        .label {
          display: block;
          font-size: var(--text-xs);
          color: var(--text-secondary);
          margin-bottom: var(--space-1);
        }
        
        .value {
          display: block;
          font-size: var(--text-base);
          font-weight: var(--font-semibold);
          color: var(--text-primary);
        }
      }
    }
    
    .exercise-notes {
      padding: var(--space-4);
      border-top: 1px solid var(--border-color);
      
      h4 {
        font-size: var(--text-sm);
        font-weight: var(--font-semibold);
        color: var(--text-primary);
        margin: 0 0 var(--space-2);
      }
      
      p {
        font-size: var(--text-sm);
        color: var(--text-secondary);
        line-height: 1.5;
        margin: 0;
      }
    }
  }
}
```

---

## 🚀 实施计划

### 阶段1：核心基础（1-2天）
1. ✅ **创建基础文件结构**
   - WorkoutDetailPage目录和基础文件
   - UIWorkoutDetail数据模型
   - WorkoutDetailTransformer基础结构

2. ✅ **实现useWorkoutDetail Hook**
   - 数据获取逻辑
   - 状态管理
   - 错误处理

3. ✅ **路由配置**
   - App.tsx中添加新路由
   - 路由参数解析逻辑

### 阶段2：核心组件（2-3天）
1. ✅ **TrainingExerciseCard组件**
   - 基础结构和样式
   - 展开/折叠动画
   - iOS触摸反馈

2. ✅ **WorkoutDetailPage主页面**
   - 页面结构和布局
   - 条件渲染逻辑
   - 基础交互功能

3. ✅ **SocialSection组件**
   - 社交信息展示
   - 点赞、关注功能
   - 乐观更新

### 阶段3：页面跳转（1天）
1. ✅ **Feed页面跳转**
   - WorkoutStatsCard点击支持
   - 跳转参数传递
   - 初始数据传递

2. ✅ **Profile页面跳转**
   - "查看详情"按钮功能
   - 路由参数配置

### 阶段4：优化完善（1-2天）
1. ✅ **iOS体验优化**
   - Safe Area适配
   - 触摸反馈完善
   - 动画性能优化

2. ✅ **错误处理完善**
   - 骨架屏组件
   - 错误状态组件
   - 重试机制

3. ✅ **测试验证**
   - 功能测试
   - iOS设备测试
   - 性能测试

### 阶段5：高级功能（可选）
1. ✅ **分享功能**
2. ✅ **编辑功能**（Profile场景）
3. ✅ **评论功能**（Feed场景）
4. ✅ **无障碍支持**

---

## ⚠️ 关键风险点和解决方案

### 1. iOS CapacitorHttp 类型转换
**风险**: API请求参数类型错误导致iOS端崩溃
**解决方案**: 
- 使用现有的`safeProcessRequestParams`函数
- 所有数字和布尔值转换为字符串
- 添加参数验证和错误捕获

### 2. 数据模型不一致
**风险**: ApiPostDetail和ApiWorkoutResponse结构差异导致转换错误
**解决方案**:
- 设计统一的UIWorkoutDetail模型
- 创建专门的转换器处理不同数据源
- 添加数据验证和降级处理

### 3. 组件命名冲突
**风险**: TrainingExerciseCard与现有ExerciseCard混淆
**解决方案**:
- 明确的命名约定（Training前缀）
- 放在页面特定目录下
- 完善的TypeScript类型区分

### 4. 性能问题
**风险**: 大量动作数据导致渲染性能问题
**解决方案**:
- 使用React.memo优化重渲染
- 虚拟化长列表（如果需要）
- 懒加载动作详情
- GPU加速动画

### 5. 肌肉数据聚合准确性
**风险**: 从动作名称推断肌肉群不准确
**解决方案**:
- 复用现有的`smartMuscleMappingForExercise`逻辑
- 添加手动校正机制
- 提供降级展示方案

---

## 📋 测试策略

### 1. 单元测试
```typescript
// 数据转换器测试
describe('WorkoutDetailTransformer', () => {
  test('应该正确转换Feed帖子数据', () => {
    const apiPost = mockApiPostDetail;
    const result = WorkoutDetailTransformer.transformPostDetailToUIModel(apiPost);
    
    expect(result.source).toBe('feed');
    expect(result.socialInfo).toBeDefined();
    expect(result.exercises).toHaveLength(apiPost.related_workout_detail.workout_exercises.length);
  });
  
  test('应该正确聚合肌肉数据', () => {
    const exercises = mockWorkoutExercises;
    const result = WorkoutDetailTransformer.aggregateMuscleData(exercises);
    
    expect(result.targetMuscleIds).toBeInstanceOf(Array);
    expect(result.synergistMuscleIds).toBeInstanceOf(Array);
  });
});

// Hook测试
describe('useWorkoutDetail', () => {
  test('应该正确获取Feed数据', async () => {
    const { result } = renderHook(() => 
      useWorkoutDetail({ type: 'feed', id: '123' })
    );
    
    await waitFor(() => {
      expect(result.current.workoutDetail).toBeDefined();
      expect(result.current.loading).toBe(false);
    });
  });
});
```

### 2. 集成测试
```typescript
// 页面集成测试
describe('WorkoutDetailPage', () => {
  test('应该正确渲染Feed场景', async () => {
    render(
      <MemoryRouter initialEntries={['/workout/123']}>
        <WorkoutDetailPage />
      </MemoryRouter>
    );
    
    expect(screen.getByText('训练动作')).toBeInTheDocument();
    expect(screen.getByText('目标肌群')).toBeInTheDocument();
  });
  
  test('应该支持动作展开/折叠', async () => {
    render(<WorkoutDetailPage />);
    
    const exerciseCard = screen.getByTestId('exercise-card-1');
    fireEvent.click(exerciseCard);
    
    expect(screen.getByTestId('exercise-details')).toHaveClass('expanded');
  });
});
```

### 3. E2E测试
```typescript
// Cypress测试
describe('WorkoutDetail E2E', () => {
  it('应该支持从Feed跳转到详情页', () => {
    cy.visit('/feed');
    cy.get('[data-testid="workout-stats-card"]').first().click();
    cy.url().should('include', '/workout/');
    cy.get('.workout-detail-page').should('be.visible');
  });
  
  it('应该支持动作详情展开', () => {
    cy.visit('/workout/123');
    cy.get('.training-exercise-card').first().click();
    cy.get('.exercise-details.expanded').should('be.visible');
  });
});
```

### 4. iOS设备测试
- **iOS模拟器测试**: iPhone 14、iPad Pro
- **真机测试**: 至少一台真实iOS设备
- **Safe Area测试**: 不同刘海屏设备
- **性能测试**: 内存使用、渲染性能
- **网络测试**: 弱网环境下的表现

---

## 📈 性能优化策略

### 1. 组件层面优化
```typescript
// 使用React.memo避免不必要的重渲染
export const TrainingExerciseCard = React.memo<TrainingExerciseCardProps>(
  ({ exercise, isExpanded, onToggleExpansion }) => {
    // 组件实现...
  },
  (prevProps, nextProps) => {
    // 自定义比较逻辑
    return (
      prevProps.exercise.id === nextProps.exercise.id &&
      prevProps.isExpanded === nextProps.isExpanded
    );
  }
);

// 使用useMemo缓存计算结果
const muscleData = useMemo(() => {
  return WorkoutDetailTransformer.aggregateMuscleData(exercises);
}, [exercises]);

// 使用useCallback缓存事件处理函数
const handleToggle = useCallback((exerciseId: string) => {
  toggleExerciseExpansion(exerciseId);
}, [toggleExerciseExpansion]);
```

### 2. 数据层面优化
```typescript
// 智能缓存策略
export const useWorkoutDetail = (dataSource: DataSource) => {
  // 利用现有缓存系统
  const cacheKey = `workout-detail-${dataSource.type}-${dataSource.id}`;
  
  const fetchData = useCallback(async () => {
    // 先检查缓存
    const cached = await CacheManager.get(cacheKey);
    if (cached && !forceRefresh) {
      return cached;
    }
    
    // 网络请求...
    const result = await fetchFromAPI();
    
    // 更新缓存
    await CacheManager.set(cacheKey, result, { ttl: 30 * 60 * 1000 });
    
    return result;
  }, [dataSource, forceRefresh]);
};

// 预加载策略
export const preloadWorkoutDetail = async (dataSource: DataSource) => {
  // 后台预加载下一个可能访问的训练详情
  const preloadKey = `preload-${dataSource.type}-${dataSource.id}`;
  
  if (!CacheManager.has(preloadKey)) {
    const data = await fetchWorkoutDetail(dataSource);
    CacheManager.set(preloadKey, data, { ttl: 10 * 60 * 1000 });
  }
};
```

### 3. 渲染层面优化
```scss
// GPU加速动画
.exercise-details {
  transform: translateZ(0); // 强制GPU层
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  
  &.collapsed {
    transform: translateY(-100%) translateZ(0);
  }
  
  &.expanded {
    transform: translateY(0) translateZ(0);
  }
}

// 优化滚动性能
.exercise-list {
  contain: layout style paint; // CSS Containment
  -webkit-overflow-scrolling: touch;
  transform: translateZ(0);
}

// 减少重绘
.training-exercise-card {
  will-change: transform; // 提示浏览器优化
  
  &:hover {
    transform: translateY(-1px) translateZ(0);
  }
}
```

---

## 🔄 后续扩展计划

### 短期扩展（1-2周）
1. **评论系统集成**
   - 评论列表展示
   - 添加/回复评论
   - 评论点赞功能

2. **分享功能完善**
   - 系统分享API集成
   - 自定义分享卡片
   - 分享到社交媒体

3. **编辑功能**（Profile场景）
   - 训练记录编辑
   - 动作顺序调整
   - 组数据修改

### 中期扩展（1个月）
1. **高级可视化**
   - 训练进度图表
   - 肌肉强度热力图
   - 历史对比分析

2. **离线支持**
   - 离线数据查看
   - 离线编辑功能
   - 同步冲突解决

3. **个性化定制**
   - 显示选项配置
   - 主题定制
   - 布局调整

### 长期扩展（3个月+）
1. **AI功能集成**
   - 智能训练分析
   - 改进建议
   - 自动肌肉识别

2. **社交功能增强**
   - 训练挑战
   - 好友对比
   - 群组训练

3. **数据分析**
   - 详细统计报告
   - 趋势分析
   - 预测模型

---

**文档版本**: 1.0.0  
**创建日期**: 2025-01-27  
**维护者**: Claude AI Assistant

**准备状态**: ✅ 技术方案设计完成，等待用户确认后开始实施