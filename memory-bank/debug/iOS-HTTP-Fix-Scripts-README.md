# iOS HTTP修复脚本使用说明

## 📋 概述

本目录包含用于修复iOS HTTP连接问题的自动化脚本，主要解决以下问题：

1. **ATS策略问题** - App Transport Security阻止HTTP连接
2. **类型转换问题** - iOS原生层无法处理JavaScript数字/布尔类型参数
3. **网络配置问题** - 服务器地址和端口配置不正确

## 🚀 快速修复命令

### 一键修复HTTP连接问题
```bash
./scripts/quick-fix-ios-http.sh
```

### 详细修复步骤
```bash
./scripts/fix-ios-http.sh
```

## 🔧 手动修复步骤

如果脚本无法运行，可以手动执行以下步骤：

### 1. 更新Info.plist
```bash
# 备份当前配置
cp ios/App/App/Info.plist ios/App/App/Info.plist.backup

# 运行修复脚本
./scripts/quick-fix-ios-http.sh
```

## 📱 手动验证步骤

### 1. 验证ATS配置
检查 `ios/App/App/Info.plist` 是否包含正确的ATS配置

### 2. 验证类型转换修复
确保所有API调用参数都已转换为字符串类型

### 3. 验证分页计算
检查分页相关的数学计算是否正确处理字符串到数字的转换

### 4. 构建和测试
```bash
npm run build
npx cap sync ios
npx cap open ios
# 在Xcode中清理并重新构建项目
```

## 🛡️ 安全注意事项

1. **生产环境配置**:
   - ATS宽松配置仅适用于开发环境
   - 生产环境应使用HTTPS并配置特定域名例外

2. **类型转换最佳实践**:
   - 新增API调用时始终将参数转换为字符串
   - 使用统一的参数处理函数确保一致性
   - 添加TypeScript类型检查避免错误

## 📚 相关文档

- [iOS HTTP服务器访问问题修复总结](./iOS-HTTP-Server-Fix-Summary.md)
- [iOS HTTP完整修复指南](./iOS-HTTP-Fix-Complete-Guide.md)
- [类型转换错误修复归档](./memory-bank/archive/ios-type-conversion-error-fix-report.md)

## ⚠️ 故障排除

如果修复后仍有问题：

1. **清理Xcode缓存**:
   - Xcode → Product → Clean Build Folder
   - 删除 `~/Library/Developer/Xcode/DerivedData` 中的相关目录

2. **重置模拟器**:
   - Device → Erase All Content and Settings

3. **重启设备**:
   - 物理设备可能需要重启以清除网络缓存

## 📱 真机测试

### 1. 连接设备
```bash
# 检查设备连接
xcrun devicectl list devices
```

### 2. 在Xcode中选择真机
1. 打开Xcode项目
2. 选择真实设备作为目标
3. 确保开发者证书正确

### 3. 构建并运行
```bash
# 在Xcode中构建
Product → Build (⌘+B)
Product → Run (⌘+R)
```

## 🎯 预期结果

修复成功后应该看到：
- ✅ 没有ATS错误日志
- ✅ 网络请求成功
- ✅ 运动数据正常加载
- ✅ 图片正常显示
- ✅ 应用功能完整可用

## 📞 技术支持

如果问题仍然存在：
1. 查看Xcode控制台完整日志
2. 访问 `/network-test` 页面截图
3. 提供设备型号和iOS版本
4. 检查网络连接状态

---

**脚本版本**: v1.0  
**最后更新**: 2024年  
**兼容性**: macOS 10.15+, Xcode 12+ 