# 🧪 营养模块集成测试方案

## 📋 测试概述

### 测试目标
1. **功能完整性验证**：确保所有营养服务功能正常工作
2. **iOS兼容性测试**：验证iOS原生功能集成效果
3. **性能基准测试**：确保响应时间和缓存效率达标
4. **错误处理验证**：测试各种异常情况的处理能力
5. **数据一致性检查**：验证服务间数据传递的准确性

### 测试范围
- **FoodService**: 食物搜索、详情、识别、收藏、评分
- **FoodbaseService**: 分类、类型、热门食物、搜索建议
- **MealService**: 餐食记录、计划、摘要、目标管理
- **NutrientService**: 营养分析、评分、趋势、AI分析

## 🔧 测试环境配置

### 1. 测试环境要求
```typescript
// 测试环境配置
interface TestEnvironment {
  platform: 'ios' | 'web';
  apiBaseUrl: string;
  mockDataEnabled: boolean;
  cacheEnabled: boolean;
  debugMode: boolean;
}

const TEST_CONFIG: TestEnvironment = {
  platform: 'ios',
  apiBaseUrl: 'https://api-test.fitmaster.com',
  mockDataEnabled: true,
  cacheEnabled: true,
  debugMode: true
};
```

### 2. 测试数据准备
```typescript
// 测试用户数据
const TEST_USER = {
  id: 999,
  email: '<EMAIL>',
  weight: 70,
  height: 175,
  age: 25,
  gender: 'male',
  activity_level: 'moderate'
};

// 测试食物数据
const TEST_FOODS = [
  { id: 1, name: '苹果', category: '水果', calories: 52 },
  { id: 2, name: '鸡胸肉', category: '肉类', calories: 165 },
  { id: 3, name: '燕麦', category: '谷物', calories: 389 }
];

// 测试餐食数据
const TEST_MEALS = [
  {
    meal_type: 'breakfast',
    meal_date: '2024-08-01',
    items: [
      { food_id: 1, quantity: 150, unit: 'g' },
      { food_id: 3, quantity: 50, unit: 'g' }
    ]
  }
];
```

## 🧪 详细测试用例

### 1. FoodService 测试用例

#### 1.1 食物搜索功能测试
```typescript
describe('FoodService - 食物搜索', () => {
  test('基础搜索功能', async () => {
    const result = await foodService.searchFoods({ name: '苹果' });
    
    expect(result.success).toBe(true);
    expect(result.data?.foods).toBeDefined();
    expect(result.data?.foods.length).toBeGreaterThan(0);
    expect(result.data?.hasMore).toBeDefined();
  });

  test('分页搜索功能', async () => {
    const page1 = await foodService.searchFoods({}, 0);
    const page2 = await foodService.searchFoods({}, 1);
    
    expect(page1.data?.currentPage).toBe(0);
    expect(page2.data?.currentPage).toBe(1);
    expect(page1.data?.foods).not.toEqual(page2.data?.foods);
  });

  test('筛选条件测试', async () => {
    const result = await foodService.searchFoods({
      category: '水果',
      max_calories: 100
    });
    
    expect(result.success).toBe(true);
    result.data?.foods.forEach(food => {
      expect(food.nutrition.calories).toBeLessThanOrEqual(100);
    });
  });

  test('缓存机制测试', async () => {
    const startTime = Date.now();
    await foodService.searchFoods({ name: '苹果' });
    const firstCallTime = Date.now() - startTime;

    const cacheStartTime = Date.now();
    await foodService.searchFoods({ name: '苹果' });
    const cacheCallTime = Date.now() - cacheStartTime;

    expect(cacheCallTime).toBeLessThan(firstCallTime);
  });
});
```

#### 1.2 食物图片识别测试
```typescript
describe('FoodService - 图片识别', () => {
  test('图片识别功能', async () => {
    const mockImageBase64 = 'data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQ...';
    
    const result = await foodService.analyzeFoodImage(
      mockImageBase64,
      'lunch',
      '2024-08-01'
    );
    
    expect(result.success).toBe(true);
    expect(result.data?.data.recognized_foods).toBeDefined();
    expect(result.data?.data.confidence).toBeGreaterThan(0);
  });

  test('iOS触觉反馈测试', async () => {
    // 模拟iOS环境
    const mockHaptics = jest.fn();
    jest.mock('@capacitor/haptics', () => ({
      Haptics: { impact: mockHaptics }
    }));

    await foodService.analyzeFoodImage('mock_image', 'dinner');
    
    // 验证触觉反馈被调用
    expect(mockHaptics).toHaveBeenCalled();
  });
});
```

### 2. MealService 测试用例

#### 2.1 餐食记录管理测试
```typescript
describe('MealService - 餐食记录', () => {
  test('创建餐食记录', async () => {
    const mealRequest = {
      meal_type: 'breakfast' as const,
      meal_date: '2024-08-01',
      items: [
        { food_id: 1, quantity: 150, unit: 'g' }
      ]
    };

    const result = await mealService.createMealRecord(mealRequest);
    
    expect(result.success).toBe(true);
    expect(result.data?.meal_type).toBe('breakfast');
    expect(result.data?.items.length).toBe(1);
  });

  test('获取餐食记录列表', async () => {
    const result = await mealService.getMealRecords({
      start_date: '2024-08-01',
      end_date: '2024-08-07'
    });
    
    expect(result.success).toBe(true);
    expect(result.data?.meals).toBeDefined();
    expect(Array.isArray(result.data?.meals)).toBe(true);
  });

  test('每日营养摘要', async () => {
    const result = await mealService.getDailyNutritionSummary('2024-08-01');
    
    expect(result.success).toBe(true);
    expect(result.data?.total_calories).toBeGreaterThanOrEqual(0);
    expect(result.data?.total_protein).toBeGreaterThanOrEqual(0);
    expect(result.data?.completion_percentage).toBeLessThanOrEqual(100);
  });
});
```

### 3. NutrientService 测试用例

#### 3.1 营养分析测试
```typescript
describe('NutrientService - 营养分析', () => {
  test('生成营养分析报告', async () => {
    const params = {
      startDate: '2024-08-01',
      endDate: '2024-08-07',
      periodType: 'week' as const,
      includeDeficiencies: true,
      includeRecommendations: true
    };

    const result = await nutrientService.generateNutritionAnalysis(params);
    
    expect(result.success).toBe(true);
    expect(result.data?.nutrient_adequacy_score).toBeGreaterThanOrEqual(0);
    expect(result.data?.nutrient_adequacy_score).toBeLessThanOrEqual(100);
    expect(result.data?.recommendations).toBeDefined();
  });

  test('营养评分计算', async () => {
    const result = await nutrientService.getNutritionScore(
      '2024-08-01',
      '2024-08-07'
    );
    
    expect(result.success).toBe(true);
    expect(result.data?.overall_score).toBeGreaterThanOrEqual(0);
    expect(result.data?.overall_score).toBeLessThanOrEqual(100);
    expect(result.data?.category_scores).toBeDefined();
  });

  test('AI营养分析', async () => {
    const request = {
      user_id: TEST_USER.id,
      analysis_type: 'pattern_detection' as const,
      time_period: {
        start_date: '2024-08-01',
        end_date: '2024-08-07'
      },
      user_profile: {
        age: TEST_USER.age,
        gender: TEST_USER.gender,
        weight: TEST_USER.weight,
        height: TEST_USER.height,
        activity_level: TEST_USER.activity_level
      }
    };

    const result = await nutrientService.performAIAnalysis(request);
    
    expect(result.success).toBe(true);
    expect(result.data?.confidence_score).toBeGreaterThan(0);
    expect(result.data?.insights).toBeDefined();
    expect(Array.isArray(result.data?.insights)).toBe(true);
  });
});
```

## 📱 iOS专项测试

### 1. iOS原生功能测试
```typescript
describe('iOS原生功能集成测试', () => {
  test('触觉反馈功能', async () => {
    // 测试不同强度的触觉反馈
    const feedbackTests = ['light', 'medium', 'heavy'];
    
    for (const style of feedbackTests) {
      const mockHaptics = jest.fn();
      jest.mock('@capacitor/haptics', () => ({
        Haptics: { impact: mockHaptics }
      }));

      // 触发需要反馈的操作
      await foodService.updateFoodFavorite(1, true);
      
      expect(mockHaptics).toHaveBeenCalled();
    }
  });

  test('状态栏样式管理', async () => {
    const mockStatusBar = jest.fn();
    jest.mock('@capacitor/status-bar', () => ({
      StatusBar: { setStyle: mockStatusBar }
    }));

    // 触发状态栏变化的操作
    await nutrientService.generateNutritionAnalysis({
      startDate: '2024-08-01',
      endDate: '2024-08-07',
      periodType: 'week'
    });

    expect(mockStatusBar).toHaveBeenCalled();
  });

  test('本地通知功能', async () => {
    const mockNotifications = jest.fn();
    jest.mock('@capacitor/local-notifications', () => ({
      LocalNotifications: { schedule: mockNotifications }
    }));

    await mealService.createMealRecord({
      meal_type: 'breakfast',
      meal_date: '2024-08-01',
      items: [{ food_id: 1, quantity: 100, unit: 'g' }]
    });

    expect(mockNotifications).toHaveBeenCalled();
  });
});
```

### 2. iOS类型安全测试
```typescript
describe('iOS类型安全测试', () => {
  test('API参数字符串转换', () => {
    const testParams = {
      skip: 0,
      limit: 20,
      active: true,
      min_calories: 100
    };

    // 模拟参数处理函数
    const processedParams = safeProcessRequestParams(testParams);

    expect(typeof processedParams.skip).toBe('string');
    expect(typeof processedParams.limit).toBe('string');
    expect(typeof processedParams.active).toBe('string');
    expect(typeof processedParams.min_calories).toBe('string');
  });

  test('数学计算类型转换', () => {
    const stringParams = {
      skip: '20',
      limit: '10'
    };

    const currentPage = Math.floor(Number(stringParams.skip) / Number(stringParams.limit));
    expect(currentPage).toBe(2);
    expect(typeof currentPage).toBe('number');
  });
});
```

## ⚡ 性能测试

### 1. 响应时间基准测试
```typescript
describe('性能基准测试', () => {
  test('API响应时间测试', async () => {
    const operations = [
      () => foodService.searchFoods({ name: '苹果' }),
      () => mealService.getDailyNutritionSummary('2024-08-01'),
      () => nutrientService.getNutritionScore('2024-08-01', '2024-08-07')
    ];

    for (const operation of operations) {
      const startTime = Date.now();
      await operation();
      const responseTime = Date.now() - startTime;

      expect(responseTime).toBeLessThan(3000); // 3秒内响应
    }
  });

  test('缓存性能测试', async () => {
    // 第一次调用（无缓存）
    const startTime1 = Date.now();
    await foodService.searchFoods({ name: '苹果' });
    const firstCallTime = Date.now() - startTime1;

    // 第二次调用（有缓存）
    const startTime2 = Date.now();
    await foodService.searchFoods({ name: '苹果' });
    const cachedCallTime = Date.now() - startTime2;

    // 缓存调用应该明显更快
    expect(cachedCallTime).toBeLessThan(firstCallTime * 0.5);
  });

  test('内存使用测试', async () => {
    const initialMemory = process.memoryUsage().heapUsed;

    // 执行大量操作
    for (let i = 0; i < 100; i++) {
      await foodService.searchFoods({ name: `test${i}` });
    }

    const finalMemory = process.memoryUsage().heapUsed;
    const memoryIncrease = finalMemory - initialMemory;

    // 内存增长应该在合理范围内（小于50MB）
    expect(memoryIncrease).toBeLessThan(50 * 1024 * 1024);
  });
});
```

## 🔄 错误处理测试

### 1. 网络错误测试
```typescript
describe('错误处理测试', () => {
  test('网络连接失败处理', async () => {
    // 模拟网络错误
    jest.mock('../core/ApiClient', () => ({
      ApiClient: jest.fn().mockImplementation(() => ({
        get: jest.fn().mockRejectedValue(new Error('Network Error')),
        post: jest.fn().mockRejectedValue(new Error('Network Error'))
      }))
    }));

    const result = await foodService.searchFoods({ name: '苹果' });

    expect(result.success).toBe(false);
    expect(result.error?.code).toBe('FOOD_SEARCH_FAILED');
    expect(result.error?.retryable).toBe(true);
  });

  test('API错误响应处理', async () => {
    // 模拟API错误响应
    jest.mock('../core/ApiClient', () => ({
      ApiClient: jest.fn().mockImplementation(() => ({
        get: jest.fn().mockRejectedValue({
          status: 400,
          message: 'Bad Request'
        })
      }))
    }));

    const result = await mealService.createMealRecord({
      meal_type: 'breakfast',
      meal_date: '2024-08-01',
      items: []
    });

    expect(result.success).toBe(false);
    expect(result.error?.message).toContain('Bad Request');
  });
});
```

## 📊 测试执行计划

### 阶段1：单元测试（1天）
- [ ] FoodService 功能测试
- [ ] FoodbaseService 功能测试
- [ ] MealService 功能测试
- [ ] NutrientService 功能测试

### 阶段2：集成测试（1天）
- [ ] 服务间数据流测试
- [ ] 缓存一致性测试
- [ ] 错误传播测试

### 阶段3：iOS专项测试（0.5天）
- [ ] 原生功能集成测试
- [ ] 类型安全测试
- [ ] 性能优化验证

### 阶段4：性能测试（0.5天）
- [ ] 响应时间基准测试
- [ ] 缓存效率测试
- [ ] 内存使用测试

## ✅ 验收标准

### 功能验收标准
- [ ] 所有核心功能正常工作
- [ ] 错误处理机制完善
- [ ] 数据一致性保证

### 性能验收标准
- [ ] API响应时间 < 3秒
- [ ] 缓存命中率 > 75%
- [ ] 内存使用增长 < 50MB

### iOS验收标准
- [ ] 触觉反馈正常工作
- [ ] 状态栏样式正确切换
- [ ] 本地通知正常发送
- [ ] 类型转换无错误

### 质量验收标准
- [ ] 测试覆盖率 > 80%
- [ ] 无内存泄漏
- [ ] 无TypeScript编译错误
