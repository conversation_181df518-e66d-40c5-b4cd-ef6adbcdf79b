/**
 * 环境配置
 * 统一管理环境变量，避免import.meta.env的TypeScript问题
 */

// 安全的环境变量获取函数
function getEnvVar(key: string, defaultValue: string = ''): string {
  try {
    // 在浏览器环境中使用import.meta.env
    if (typeof window !== 'undefined' && 'import' in window) {
      return (window as any).import?.meta?.env?.[key] || defaultValue;
    }
    
    // 在Node.js环境中使用process.env
    if (typeof process !== 'undefined' && process.env) {
      return process.env[key] || defaultValue;
    }
    
    return defaultValue;
  } catch (error) {
    console.warn(`获取环境变量 ${key} 失败:`, error);
    return defaultValue;
  }
}

function getEnvBoolean(key: string, defaultValue: boolean = false): boolean {
  try {
    const value = getEnvVar(key);
    if (value === 'true' || value === '1') return true;
    if (value === 'false' || value === '0') return false;
    return defaultValue;
  } catch (error) {
    console.warn(`获取布尔环境变量 ${key} 失败:`, error);
    return defaultValue;
  }
}

// 环境配置
export const ENV_CONFIG = {
  // API配置
  API_BASE_URL: getEnvVar('VITE_API_BASE_URL', 'http://localhost:8000'),
  API_IMAGE_BASE_URL: getEnvVar('VITE_API_IMAGE_BASE_URL', 'http://localhost:8000'),
  
  // 应用环境
  APP_ENV: getEnvVar('VITE_APP_ENV', 'development'),
  IS_DEV: getEnvBoolean('DEV', true),
  IS_PROD: getEnvBoolean('PROD', false),
  
  // 其他配置
  MODE: getEnvVar('MODE', 'development'),
  BASE_URL: getEnvVar('BASE_URL', '/'),
  SSR: getEnvBoolean('SSR', false)
};

// 开发环境检测
export const isDevelopment = ENV_CONFIG.IS_DEV || ENV_CONFIG.APP_ENV === 'development';
export const isProduction = ENV_CONFIG.IS_PROD || ENV_CONFIG.APP_ENV === 'production';

// 导出环境配置
export default ENV_CONFIG;
