/**
 * ErrorStateComponent 错误状态组件
 * 
 * @fileoverview 通用错误状态展示组件，支持重试操作
 * <AUTHOR> Team
 * @since 1.0.0
 */

import React, { useCallback } from 'react';
import './ErrorStates.scss';

/**
 * 错误类型定义
 */
export interface ErrorInfo {
  /** 错误代码 */
  code: string;
  /** 错误消息 */
  message: string;
  /** 是否可以重试 */
  retryable: boolean;
}

/**
 * ErrorStateComponent 组件属性
 */
export interface ErrorStateComponentProps {
  /** 错误信息 */
  error: ErrorInfo;
  /** 重试回调 */
  onRetry?: () => void;
  /** 返回回调 */
  onGoBack?: () => void;
  /** 额外的CSS类名 */
  className?: string;
}

/**
 * 错误状态组件
 * 根据错误类型显示不同的错误信息和操作建议
 */
export const ErrorStateComponent: React.FC<ErrorStateComponentProps> = ({
  error,
  onRetry,
  onGoBack,
  className = ''
}) => {
  // iOS触摸反馈
  const provideiOSTouchFeedback = useCallback(() => {
    try {
      if (window.navigator.vibrate) {
        window.navigator.vibrate(10);
      }
    } catch (error) {
      // 静默忽略错误
    }
  }, []);

  // 处理重试操作
  const handleRetry = useCallback(() => {
    if (onRetry) {
      provideiOSTouchFeedback();
      onRetry();
    }
  }, [onRetry, provideiOSTouchFeedback]);

  // 处理返回操作
  const handleGoBack = useCallback(() => {
    if (onGoBack) {
      provideiOSTouchFeedback();
      onGoBack();
    }
  }, [onGoBack, provideiOSTouchFeedback]);

  // 根据错误代码获取错误图标
  const getErrorIcon = (code: string) => {
    switch (code) {
      case 'NETWORK_ERROR':
        return '🌐';
      case 'NOT_FOUND':
        return '🔍';
      case 'UNAUTHORIZED':
        return '🔒';
      case 'SERVER_ERROR':
        return '⚠️';
      default:
        return '❌';
    }
  };

  // 根据错误代码获取错误标题
  const getErrorTitle = (code: string) => {
    switch (code) {
      case 'NETWORK_ERROR':
        return '网络连接异常';
      case 'NOT_FOUND':
        return '内容不存在';
      case 'UNAUTHORIZED':
        return '需要登录';
      case 'SERVER_ERROR':
        return '服务器异常';
      default:
        return '加载失败';
    }
  };

  // 根据错误代码获取操作建议
  const getErrorSuggestion = (code: string) => {
    switch (code) {
      case 'NETWORK_ERROR':
        return '请检查网络连接后重试';
      case 'NOT_FOUND':
        return '请求的内容可能已被删除或移动';
      case 'UNAUTHORIZED':
        return '请先登录后再访问此内容';
      case 'SERVER_ERROR':
        return '服务器暂时无法响应，请稍后重试';
      default:
        return '发生了未知错误，请重试或联系客服';
    }
  };

  return (
    <div className={`error-state-component ${className}`}>
      <div className="error-content">
        {/* 错误图标 */}
        <div className="error-icon">
          {getErrorIcon(error.code)}
        </div>
        
        {/* 错误标题 */}
        <h2 className="error-title">
          {getErrorTitle(error.code)}
        </h2>
        
        {/* 错误消息 */}
        <p className="error-message">
          {error.message}
        </p>
        
        {/* 操作建议 */}
        <p className="error-suggestion">
          {getErrorSuggestion(error.code)}
        </p>
        
        {/* 操作按钮 */}
        <div className="error-actions">
          {error.retryable && onRetry && (
            <button
              className="retry-button primary"
              onClick={handleRetry}
              type="button"
            >
              重试
            </button>
          )}
          
          {onGoBack && (
            <button
              className="back-button secondary"
              onClick={handleGoBack}
              type="button"
            >
              返回
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default ErrorStateComponent;