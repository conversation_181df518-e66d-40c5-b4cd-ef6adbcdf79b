/**
 * ErrorStates 样式
 * 
 * @fileoverview 错误状态组件的样式定义，符合iOS HIG标准
 * <AUTHOR> Team
 * @since 1.0.0
 */

// iOS触摸目标样式混入
@mixin ios-touch-target {
  min-width: var(--ios-touch-target); // 44px
  min-height: var(--ios-touch-target);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 22px;
  cursor: pointer;
  transition: transform 0.1s cubic-bezier(0.4, 0, 0.2, 1);
  
  &:active {
    transform: scale(0.98);
  }
}

// 按钮基础样式
@mixin button-base {
  @include ios-touch-target;
  border: none;
  font-size: var(--text-base);
  font-weight: var(--font-semibold);
  padding: var(--space-3) var(--space-6);
  border-radius: 24px;
  transition: all 0.2s ease;
  
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
  }
}

// 错误状态通用样式
%error-state-base {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  padding: var(--space-6);
  text-align: center;
  
  .error-content,
  .not-found-content {
    max-width: 400px;
    width: 100%;
    
    .error-icon,
    .not-found-icon {
      font-size: 64px;
      margin-bottom: var(--space-4);
      filter: grayscale(20%);
    }
    
    .error-title,
    .not-found-title {
      font-size: var(--text-xl);
      font-weight: var(--font-bold);
      color: var(--text-primary);
      margin: 0 0 var(--space-3);
    }
    
    .error-message,
    .not-found-description {
      font-size: var(--text-base);
      color: var(--text-secondary);
      line-height: 1.6;
      margin: 0 0 var(--space-2);
    }
    
    .error-suggestion,
    .not-found-suggestion {
      font-size: var(--text-sm);
      color: var(--text-muted);
      line-height: 1.5;
      margin: 0 0 var(--space-6);
    }
    
    .error-actions,
    .not-found-actions {
      display: flex;
      flex-direction: column;
      gap: var(--space-3);
      align-items: center;
      
      button {
        @include button-base;
        
        &.primary {
          background: var(--accent-500);
          color: white;
          
          &:hover {
            background: var(--accent-600);
          }
          
          &:active {
            background: var(--accent-700);
          }
        }
        
        &.secondary {
          background: var(--bg-secondary);
          color: var(--text-primary);
          border: 1px solid var(--border-color);
          
          &:hover {
            background: var(--bg-tertiary);
            border-color: var(--border-hover);
          }
          
          &:active {
            background: var(--bg-quaternary);
          }
        }
      }
    }
  }
}

// ErrorStateComponent 样式
.error-state-component {
  @extend %error-state-base;
  background: var(--bg-primary);
  
  .error-content {
    .error-icon {
      // 根据错误类型调整图标颜色
      &:has-text("🌐") {
        filter: hue-rotate(210deg);
      }
      
      &:has-text("🔍") {
        filter: hue-rotate(30deg);
      }
      
      &:has-text("🔒") {
        filter: hue-rotate(60deg);
      }
      
      &:has-text("⚠️") {
        filter: hue-rotate(45deg);
      }
    }
    
    .error-actions {
      // 重试按钮优先级更高
      .retry-button {
        order: 1;
      }
      
      .back-button {
        order: 2;
      }
    }
  }
}

// NotFoundState 样式
.not-found-state {
  @extend %error-state-base;
  background: var(--bg-primary);
  
  .not-found-content {
    .not-found-icon {
      color: var(--text-muted);
    }
    
    .not-found-actions {
      .back-button {
        min-width: 160px;
      }
    }
  }
}

// iOS专用媒体查询优化
@media only screen and (max-width: 768px) {
  %error-state-base {
    min-height: 50vh;
    padding: var(--space-4);
    
    .error-content,
    .not-found-content {
      .error-icon,
      .not-found-icon {
        font-size: 48px;
        margin-bottom: var(--space-3);
      }
      
      .error-title,
      .not-found-title {
        font-size: var(--text-lg);
        margin-bottom: var(--space-2);
      }
      
      .error-message,
      .not-found-description {
        font-size: var(--text-sm);
        margin-bottom: var(--space-2);
      }
      
      .error-suggestion,
      .not-found-suggestion {
        font-size: var(--text-xs);
        margin-bottom: var(--space-4);
      }
      
      .error-actions,
      .not-found-actions {
        width: 100%;
        
        button {
          width: 100%;
          max-width: 280px;
          padding: var(--space-3) var(--space-4);
        }
      }
    }
  }
}

// 深色主题适配
@media (prefers-color-scheme: dark) {
  .error-state-component,
  .not-found-state {
    .error-content,
    .not-found-content {
      .error-actions,
      .not-found-actions {
        button {
          &.secondary {
            background: var(--bg-secondary-dark);
            border-color: var(--border-color-dark);
            color: var(--text-primary-dark);
            
            &:hover {
              background: var(--bg-tertiary-dark);
              border-color: var(--border-hover-dark);
            }
          }
        }
      }
    }
  }
}

// 高对比度模式适配
@media (prefers-contrast: high) {
  %error-state-base {
    .error-content,
    .not-found-content {
      .error-title,
      .not-found-title {
        font-weight: var(--font-black);
      }
      
      .error-actions,
      .not-found-actions {
        button {
          border-width: 2px;
          font-weight: var(--font-bold);
          
          &.secondary {
            border-color: var(--text-primary);
          }
        }
      }
    }
  }
}

// 减弱动画模式适配
@media (prefers-reduced-motion: reduce) {
  %error-state-base {
    .error-content,
    .not-found-content {
      .error-actions,
      .not-found-actions {
        button {
          transition: none;
          
          &:active {
            transform: none;
          }
        }
      }
    }
  }
}