/**
 * NotFoundState 未找到状态组件
 * 
 * @fileoverview 数据不存在或未找到时的状态展示组件
 * <AUTHOR> Team
 * @since 1.0.0
 */

import React, { useCallback } from 'react';
import './ErrorStates.scss';

/**
 * NotFoundState 组件属性
 */
export interface NotFoundStateProps {
  /** 数据类型描述 */
  dataType?: string;
  /** 返回回调 */
  onGoBack?: () => void;
  /** 额外的CSS类名 */
  className?: string;
}

/**
 * 未找到状态组件
 * 用于显示数据不存在或已删除的情况
 */
export const NotFoundState: React.FC<NotFoundStateProps> = ({
  dataType = '训练记录',
  onGoBack,
  className = ''
}) => {
  // iOS触摸反馈
  const provideiOSTouchFeedback = useCallback(() => {
    try {
      if (window.navigator.vibrate) {
        window.navigator.vibrate(10);
      }
    } catch (error) {
      // 静默忽略错误
    }
  }, []);

  // 处理返回操作
  const handleGoBack = useCallback(() => {
    if (onGoBack) {
      provideiOSTouchFeedback();
      onGoBack();
    }
  }, [onGoBack, provideiOSTouchFeedback]);

  return (
    <div className={`not-found-state ${className}`}>
      <div className="not-found-content">
        {/* 未找到图标 */}
        <div className="not-found-icon">
          🔍
        </div>
        
        {/* 标题 */}
        <h2 className="not-found-title">
          {dataType}不存在
        </h2>
        
        {/* 描述信息 */}
        <p className="not-found-description">
          请求的{dataType}可能已被删除或移动到其他位置
        </p>
        
        {/* 建议信息 */}
        <p className="not-found-suggestion">
          您可以返回上一页或查看其他内容
        </p>
        
        {/* 操作按钮 */}
        <div className="not-found-actions">
          {onGoBack && (
            <button
              className="back-button primary"
              onClick={handleGoBack}
              type="button"
            >
              返回上一页
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default NotFoundState;