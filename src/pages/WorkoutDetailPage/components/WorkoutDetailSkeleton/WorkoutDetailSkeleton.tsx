/**
 * WorkoutDetailSkeleton 骨架屏组件
 * 
 * @fileoverview 训练详情页面的加载骨架屏，提供优雅的加载体验
 * <AUTHOR> Team
 * @since 1.0.0
 */

import React from 'react';
import './WorkoutDetailSkeleton.scss';

/**
 * WorkoutDetailSkeleton 组件属性
 */
export interface WorkoutDetailSkeletonProps {
  /** 是否显示社交区域骨架 */
  showSocialSection?: boolean;
  /** 额外的CSS类名 */
  className?: string;
}

/**
 * 训练详情页面骨架屏组件
 * 根据场景动态显示不同的骨架结构
 */
export const WorkoutDetailSkeleton: React.FC<WorkoutDetailSkeletonProps> = ({
  showSocialSection = false,
  className = ''
}) => {
  return (
    <div className={`workout-detail-skeleton ${className}`}>
      {/* 页面头部骨架 */}
      <div className="skeleton-header">
        <div className="skeleton-back-button skeleton-element"></div>
        <div className="skeleton-title skeleton-element"></div>
        <div className="skeleton-action-button skeleton-element"></div>
      </div>

      {/* 页面内容骨架 */}
      <div className="skeleton-content">
        {/* 社交信息骨架（仅Feed场景） */}
        {showSocialSection && (
          <div className="skeleton-social-section">
            <div className="skeleton-user-info">
              <div className="skeleton-avatar skeleton-element"></div>
              <div className="skeleton-user-details">
                <div className="skeleton-username skeleton-element"></div>
                <div className="skeleton-timestamp skeleton-element"></div>
              </div>
              <div className="skeleton-follow-button skeleton-element"></div>
            </div>
            <div className="skeleton-post-content">
              <div className="skeleton-text-line skeleton-element"></div>
              <div className="skeleton-text-line skeleton-element"></div>
            </div>
            <div className="skeleton-social-actions">
              <div className="skeleton-action-button skeleton-element"></div>
              <div className="skeleton-action-button skeleton-element"></div>
              <div className="skeleton-action-button skeleton-element"></div>
            </div>
          </div>
        )}

        {/* 训练统计骨架 */}
        <div className="skeleton-stats-section">
          <div className="skeleton-section-title skeleton-element"></div>
          <div className="skeleton-stats-grid">
            <div className="skeleton-stat-item skeleton-element"></div>
            <div className="skeleton-stat-item skeleton-element"></div>
            <div className="skeleton-stat-item skeleton-element"></div>
            <div className="skeleton-stat-item skeleton-element"></div>
          </div>
        </div>

        {/* 训练动作骨架 */}
        <div className="skeleton-exercises-section">
          <div className="skeleton-section-title skeleton-element"></div>
          <div className="skeleton-exercises-list">
            {Array.from({ length: 3 }).map((_, index) => (
              <div key={index} className="skeleton-exercise-card">
                <div className="skeleton-exercise-header">
                  <div className="skeleton-exercise-info">
                    <div className="skeleton-exercise-name skeleton-element"></div>
                    <div className="skeleton-exercise-summary skeleton-element"></div>
                  </div>
                  <div className="skeleton-completion-badge skeleton-element"></div>
                  <div className="skeleton-expand-button skeleton-element"></div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* 肌肉详情骨架 */}
        <div className="skeleton-muscle-section">
          <div className="skeleton-section-title skeleton-element"></div>
          <div className="skeleton-muscle-info">
            <div className="skeleton-muscle-item skeleton-element"></div>
            <div className="skeleton-muscle-item skeleton-element"></div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default WorkoutDetailSkeleton;