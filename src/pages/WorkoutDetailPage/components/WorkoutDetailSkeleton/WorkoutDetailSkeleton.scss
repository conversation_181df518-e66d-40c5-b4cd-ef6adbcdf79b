/**
 * WorkoutDetailSkeleton 样式
 * 
 * @fileoverview 训练详情页面骨架屏的样式定义，符合iOS HIG标准
 * <AUTHOR> Team
 * @since 1.0.0
 */

// 骨架元素基础样式
@mixin skeleton-base {
  background: linear-gradient(
    90deg,
    var(--skeleton-color-base) 25%,
    var(--skeleton-color-highlight) 50%,
    var(--skeleton-color-base) 75%
  );
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 6px;
}

// 骨架动画
@keyframes skeleton-loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

.workout-detail-skeleton {
  min-height: 100vh;
  background: var(--bg-primary);
  
  // iOS Safe Area 适配
  padding-top: calc(var(--safe-area-inset-top) + var(--header-height));
  padding-bottom: calc(var(--safe-area-inset-bottom) + var(--nav-height));
  padding-left: var(--safe-area-inset-left);
  padding-right: var(--safe-area-inset-right);
  
  // 定义骨架屏颜色变量
  --skeleton-color-base: var(--bg-secondary);
  --skeleton-color-highlight: var(--bg-tertiary);
  
  .skeleton-element {
    @include skeleton-base;
  }
  
  .skeleton-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--space-4);
    background: var(--bg-surface);
    border-bottom: 1px solid var(--border-color);
    
    .skeleton-back-button {
      width: 44px;
      height: 44px;
      border-radius: 22px;
    }
    
    .skeleton-title {
      height: 24px;
      width: 150px;
    }
    
    .skeleton-action-button {
      width: 60px;
      height: 32px;
      border-radius: 16px;
    }
  }
  
  .skeleton-content {
    padding: var(--space-4);
    
    // 社交信息骨架
    .skeleton-social-section {
      background: var(--bg-surface);
      border-radius: 12px;
      border: 1px solid var(--border-color);
      padding: var(--space-4);
      margin-bottom: var(--space-4);
      
      .skeleton-user-info {
        display: flex;
        align-items: flex-start;
        gap: var(--space-3);
        margin-bottom: var(--space-4);
        
        .skeleton-avatar {
          width: 48px;
          height: 48px;
          border-radius: 50%;
          flex-shrink: 0;
        }
        
        .skeleton-user-details {
          flex: 1;
          
          .skeleton-username {
            height: 18px;
            width: 120px;
            margin-bottom: var(--space-2);
          }
          
          .skeleton-timestamp {
            height: 14px;
            width: 80px;
          }
        }
        
        .skeleton-follow-button {
          width: 60px;
          height: 32px;
          border-radius: 16px;
        }
      }
      
      .skeleton-post-content {
        margin-bottom: var(--space-4);
        
        .skeleton-text-line {
          height: 16px;
          margin-bottom: var(--space-2);
          
          &:first-child {
            width: 100%;
          }
          
          &:last-child {
            width: 70%;
          }
        }
      }
      
      .skeleton-social-actions {
        display: flex;
        gap: var(--space-2);
        border-top: 1px solid var(--border-color);
        padding-top: var(--space-3);
        
        .skeleton-action-button {
          width: 80px;
          height: 36px;
          border-radius: 18px;
        }
      }
    }
    
    // 训练统计骨架
    .skeleton-stats-section {
      margin-bottom: var(--space-6);
      
      .skeleton-section-title {
        height: 24px;
        width: 120px;
        margin-bottom: var(--space-3);
      }
      
      .skeleton-stats-grid {
        background: var(--bg-surface);
        border-radius: 12px;
        border: 1px solid var(--border-color);
        padding: var(--space-4);
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: var(--space-4);
        
        .skeleton-stat-item {
          height: 60px;
          border-radius: 8px;
        }
      }
    }
    
    // 训练动作骨架
    .skeleton-exercises-section {
      margin-bottom: var(--space-6);
      
      .skeleton-section-title {
        height: 24px;
        width: 160px;
        margin-bottom: var(--space-3);
      }
      
      .skeleton-exercises-list {
        .skeleton-exercise-card {
          background: var(--bg-surface);
          border-radius: 12px;
          border: 1px solid var(--border-color);
          margin-bottom: var(--space-4);
          padding: var(--space-4);
          
          .skeleton-exercise-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            
            .skeleton-exercise-info {
              flex: 1;
              margin-right: var(--space-3);
              
              .skeleton-exercise-name {
                height: 18px;
                width: 180px;
                margin-bottom: var(--space-2);
              }
              
              .skeleton-exercise-summary {
                height: 14px;
                width: 140px;
              }
            }
            
            .skeleton-completion-badge {
              width: 32px;
              height: 32px;
              border-radius: 50%;
              margin-right: var(--space-3);
            }
            
            .skeleton-expand-button {
              width: 32px;
              height: 32px;
              border-radius: 50%;
            }
          }
        }
      }
    }
    
    // 肌肉详情骨架
    .skeleton-muscle-section {
      .skeleton-section-title {
        height: 24px;
        width: 100px;
        margin-bottom: var(--space-3);
      }
      
      .skeleton-muscle-info {
        background: var(--bg-surface);
        border-radius: 12px;
        border: 1px solid var(--border-color);
        padding: var(--space-4);
        
        .skeleton-muscle-item {
          height: 16px;
          margin-bottom: var(--space-2);
          
          &:first-child {
            width: 80%;
          }
          
          &:last-child {
            width: 60%;
            margin-bottom: 0;
          }
        }
      }
    }
  }
}

// iOS专用媒体查询优化
@media only screen and (max-width: 768px) {
  .workout-detail-skeleton {
    .skeleton-header {
      padding: var(--space-3);
      
      .skeleton-title {
        width: 120px;
      }
      
      .skeleton-action-button {
        width: 50px;
        height: 28px;
      }
    }
    
    .skeleton-content {
      padding: var(--space-3);
      
      .skeleton-social-section {
        padding: var(--space-3);
        border-radius: 8px;
        
        .skeleton-user-info {
          .skeleton-avatar {
            width: 40px;
            height: 40px;
          }
        }
      }
      
      .skeleton-stats-section {
        .skeleton-stats-grid {
          padding: var(--space-3);
          border-radius: 8px;
          
          .skeleton-stat-item {
            height: 50px;
          }
        }
      }
      
      .skeleton-exercises-section {
        .skeleton-exercises-list {
          .skeleton-exercise-card {
            padding: var(--space-3);
            border-radius: 8px;
          }
        }
      }
    }
  }
}

// 深色主题适配
@media (prefers-color-scheme: dark) {
  .workout-detail-skeleton {
    --skeleton-color-base: rgba(255, 255, 255, 0.08);
    --skeleton-color-highlight: rgba(255, 255, 255, 0.12);
  }
}

// 减弱动画模式适配
@media (prefers-reduced-motion: reduce) {
  .workout-detail-skeleton {
    .skeleton-element {
      animation: none;
      background: var(--skeleton-color-base);
    }
  }
}