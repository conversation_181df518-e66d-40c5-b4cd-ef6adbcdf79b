/**
 * SocialSection 样式
 * 
 * @fileoverview 社交信息区域的样式定义，使用统一的PostUserSection和PostActions组件
 * <AUTHOR> Team
 * @since 1.0.0
 */

.social-section {
  background: var(--bg-surface);
  border-radius: 12px;
  border: 1px solid var(--border-color);
  padding: var(--space-4);
  margin-bottom: var(--space-6);
  
  // iOS风格阴影
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  
  &:hover {
    border-color: var(--border-hover);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  }
  
  // 用户信息区域（使用PostUserSection组件）
  .post-header {
    margin-bottom: var(--space-4);
    border-bottom: 1px solid var(--border-light);
    padding-bottom: var(--space-4);
    
    .social-user-section {
      // PostUserSection组件的样式已在通用组件中定义
      // 这里可以添加特定的样式覆盖（如需要）
    }
  }
  
  // 帖子内容样式
  .post-content {
    margin-bottom: var(--space-4);
    
    p {
      font-size: var(--text-base);
      line-height: 1.6;
      color: var(--text-primary);
      margin: 0;
      word-break: break-word;
      
      // iOS优化的文本选择
      -webkit-user-select: text;
      user-select: text;
    }
  }
  
  // 帖子图片样式
  .post-images {
    display: grid;
    gap: var(--space-2);
    margin-bottom: var(--space-4);
    
    // 根据图片数量调整布局
    &:has(img:only-child) {
      grid-template-columns: 1fr;
    }
    
    &:has(img:nth-child(2):last-child) {
      grid-template-columns: 1fr 1fr;
    }
    
    &:has(img:nth-child(3):last-child) {
      grid-template-columns: 1fr 1fr 1fr;
    }
    
    // 更多图片时使用默认网格
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    
    .post-image {
      width: 100%;
      height: 120px;
      object-fit: cover;
      border-radius: 8px;
      border: 1px solid var(--border-light);
      transition: transform 0.2s cubic-bezier(0.4, 0, 0.2, 1);
      
      &:hover {
        transform: scale(1.02);
      }
      
      // iOS优化加载
      loading: lazy;
    }
  }
  
  // 社交操作区域（使用PostActions组件）
  .social-actions {
    // PostActions组件的样式已在通用组件中定义
    // 这里可以添加特定的样式覆盖（如需要）
  }
}

// iOS专用优化
@media only screen and (max-width: 768px) {
  .social-section {
    padding: var(--space-3);
    margin-bottom: var(--space-4);
    border-radius: 8px;
    
    .post-header {
      margin-bottom: var(--space-3);
      padding-bottom: var(--space-3);
    }
    
    .post-content {
      margin-bottom: var(--space-3);
      
      p {
        font-size: var(--text-sm);
      }
    }
    
    .post-images {
      margin-bottom: var(--space-3);
      gap: var(--space-1-5);
      
      .post-image {
        height: 100px;
        border-radius: 6px;
      }
    }
  }
}

// 横屏模式优化
@media only screen and (max-height: 500px) and (orientation: landscape) {
  .social-section {
    .post-images {
      .post-image {
        height: 80px;
      }
    }
  }
}

// 深色主题支持
@media (prefers-color-scheme: dark) {
  .social-section {
    background: var(--bg-surface-dark);
    border-color: var(--border-color-dark);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
    
    &:hover {
      border-color: var(--border-hover-dark);
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.4);
    }
    
    .post-header {
      border-bottom-color: var(--border-light-dark);
    }
    
    .post-content p {
      color: var(--text-primary-dark);
    }
    
    .post-images .post-image {
      border-color: var(--border-light-dark);
    }
  }
}

// 高对比度支持
@media (prefers-contrast: high) {
  .social-section {
    border-width: 2px;
    
    .post-header {
      border-bottom-width: 2px;
    }
    
    .post-images .post-image {
      border-width: 2px;
    }
  }
}