import React, { useState, useCallback, useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import './ExercisesPage.scss';
import { ExerciseCard } from '../../components/ExerciseCard/ExerciseCard';
import { SkeletonCard } from '../../components/LoadingSpinner/LoadingSpinner';

import { useInfiniteScroll } from '../../hooks/useInfiniteScroll';
import { useExerciseFilters } from '../../hooks/useExerciseFilters';
import { useInfiniteExercises } from '../../hooks/useInfiniteExercises';
import { exerciseService } from '../../services/exerciseService';
import { getBodyPartIconByName, getDisplayOrderedBodyParts } from '../../constants/exerciseCategories';
import { Exercise } from '../../utils/exerciseDataMapper';

// 开发环境下导入验证工具
if (import.meta.env.DEV) {
  import('../../utils/bodyPartValidation').then(({ logValidationResult }) => {
    logValidationResult();
  });
}

// BodyPartIcon 组件 - 渲染身体部位的 SVG 图标
const BodyPartIcon: React.FC<{ bodyPartName: string; className?: string }> = React.memo(({ 
  bodyPartName, 
  className = '' 
}) => {
  const iconFileName = getBodyPartIconByName(bodyPartName);
  
  if (!iconFileName) {
    return <span className={`muscle-icon ${className}`}>💪</span>;
  }
  
  const iconPath = new URL(`../../assets/muscle/filter-icon/${iconFileName}`, import.meta.url).href;
  
  return (
    <img 
      src={iconPath} 
      alt={bodyPartName}
      className={`muscle-icon ${className}`}
    />
  );
});

// 难度显示组件
const getDifficultyDisplay = (difficulty: string) => {
  const difficultyLevel = difficulty === 'beginner' ? 1 : difficulty === 'intermediate' ? 2 : 3;
  const iconPath = new URL(`../../assets/plates/plate${difficultyLevel === 1 ? '2-5' : difficultyLevel === 2 ? '5' : '10'}.png`, import.meta.url).href;
  
  return (
    <img 
      src={iconPath} 
      alt={difficulty}
      className="difficulty-plate"
    />
  );
};

// 最近执行的运动
const recentPerformedExercises = [
  { id: 'ex_1', name: '杠铃卧推', muscleGroup: '胸部', lastPerformed: '2天前' },
  { id: 'ex_2', name: '深蹲', muscleGroup: '股四头肌', lastPerformed: '3天前' },
  { id: 'ex_3', name: '硬拉', muscleGroup: '背部', lastPerformed: '5天前' }
];

const ExercisesPage: React.FC = () => {
  const navigate = useNavigate();
  
  // 🔧 使用新的筛选Hook管理筛选状态
  const {
    searchTerm,
    selectedMuscleGroup,
    filters,
    compiledFilters,
    setSearchTerm,
    setSelectedMuscleGroup,

    resetFilters,
    hasActiveFilters,
    getActiveFilterCount
  } = useExerciseFilters();

  // 🔧 使用新的无限滚动数据Hook
  const {
    exercises,
    loading,
    loadingMore,
    error,
    hasMore,
    loadMore,
    refresh,
    toggleFavorite,
    preloadNextPage,
    isFromCache,
    cacheTimestamp
  } = useInfiniteExercises({
    filters: compiledFilters,
    enablePreload: true
  });

  // UI状态
  const [sortBy] = useState<'name' | 'popularity' | 'difficulty'>('popularity');
  const [showSearchModal, setShowSearchModal] = useState(false);

  // 无限滚动Hook
  const { sentinelRef, isIntersecting } = useInfiniteScroll({
    hasMore,
    loading: loadingMore,
    onLoadMore: loadMore
  });

  // 🍎 iOS触摸反馈工具函数
  const provideiOSTouchFeedback = useCallback(() => {
    try {
      // iOS轻微震动反馈
      if (window.navigator.vibrate) {
        window.navigator.vibrate(10);
      }
      
      // iOS触摸音效反馈（如果支持）
      if ('AudioContext' in window) {
        // 创建轻微的点击音效
        const audioContext = new AudioContext();
        const oscillator = audioContext.createOscillator();
        const gainNode = audioContext.createGain();
        
        oscillator.connect(gainNode);
        gainNode.connect(audioContext.destination);
        
        oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
        gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
        gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.1);
        
        oscillator.start(audioContext.currentTime);
        oscillator.stop(audioContext.currentTime + 0.1);
      }
    } catch (error) {
      console.warn('⚠️ iOS触摸反馈失败:', error);
    }
  }, []);

  // 🔧 肌肉群选择处理
  const handleMuscleGroupSelect = useCallback((muscleGroup: string) => {
    console.log('🎯 肌肉群选择开始:', {
      clickedMuscle: muscleGroup,
      currentSelection: selectedMuscleGroup,
      timestamp: new Date().toISOString()
    });

    // 🍎 iOS触摸反馈
    provideiOSTouchFeedback();

    // 确定新的选择状态
    const newSelection = selectedMuscleGroup === muscleGroup ? null : muscleGroup;

    console.log('🔄 肌肉群选择变更:', {
      from: selectedMuscleGroup,
      to: newSelection,
      action: newSelection ? '选择' : '取消选择'
    });

    // 使用新Hook更新筛选状态
    setSelectedMuscleGroup(newSelection);

  }, [selectedMuscleGroup, provideiOSTouchFeedback, setSelectedMuscleGroup]);

  // 收藏功能
  const handleToggleFavorite = useCallback(async (exerciseId: string) => {
    console.log('❤️ 切换收藏状态:', exerciseId);
    provideiOSTouchFeedback();
    
    try {
      await toggleFavorite(exerciseId);
      console.log('收藏状态切换成功');
    } catch (error) {
      console.error('收藏操作失败:', error);
    }
  }, [provideiOSTouchFeedback, toggleFavorite]);

  // 动作详情导航
  const handleExerciseClick = useCallback((exercise: Exercise) => {
    console.log('🎯 跳转到动作详情:', exercise.name);
    provideiOSTouchFeedback();
    
    // 🔧 预加载下一页数据，提升用户体验
    preloadNextPage();
    
    navigate(`/exercises/${exercise.id}`, {
      state: {
        name: exercise.name,
        equipment: exercise.equipment,
        category: exercise.category,
        difficulty: exercise.difficulty
      }
    });
  }, [navigate, provideiOSTouchFeedback, preloadNextPage]);

  // 重试功能
  const handleRetry = useCallback(async () => {
    console.log('🔄 重试数据加载');
    provideiOSTouchFeedback();
    await refresh();
  }, [refresh, provideiOSTouchFeedback]);

  // 调试功能
  const handleManualLoadMore = useCallback(async () => {
    console.log('🔄 手动触发增量加载...');
    provideiOSTouchFeedback();
    await loadMore();
  }, [loadMore, provideiOSTouchFeedback]);

  const handleResetData = useCallback(() => {
    console.log('🔄 重置筛选器...');
    
    // 🍎 iOS反馈
    provideiOSTouchFeedback();
    
    // 使用新Hook重置筛选器
    resetFilters();
  }, [provideiOSTouchFeedback, resetFilters]);

  // 搜索和筛选模态框处理
  const handleSearchModalOpen = useCallback(() => {
    provideiOSTouchFeedback();
    setShowSearchModal(true);
  }, [provideiOSTouchFeedback]);

  const handleAddExercise = useCallback(() => {
    provideiOSTouchFeedback();
    console.log('添加运动');
  }, [provideiOSTouchFeedback]);

  // 客户端排序 - 使用useMemo优化
  const sortedExercises = useMemo(() => {
    return [...exercises].sort((a, b) => {
      switch (sortBy) {
        case 'name':
          return a.name.localeCompare(b.name);
        case 'difficulty':
          return parseInt(a.difficulty) - parseInt(b.difficulty);
        case 'popularity':
        default:
          return b.popularity - a.popularity;
      }
    });
  }, [exercises, sortBy]);

  return (
    <div className="exercises-page">
      {/* Header设计 */}
      <div className="exercises-header">
        <div className="header-top">
          <div className="title-section">
            <h1>运动库</h1>
            <p>探索和学习各种健身动作</p>
          </div>
          
          <div className="header-actions">
            <button 
              className="action-icon-btn search-btn"
              onClick={handleSearchModalOpen}
              aria-label="搜索运动"
            >
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                <path d="M6 2h8v2H6V2zM4 6V4h2v2H4zm0 8H2V6h2v8zm2 2H4v-2h2v2zm8 0v2H6v-2h8zm2-2h-2v2h2v2h2v2h2v2h2v-2h-2v-2h-2v-2h-2v-2zm0-8h2v8h-2V6zm0 0V4h-2v2h2z" fill="currentColor"/>
              </svg>
            </button>
             
            <button 
              className="action-icon-btn filter-btn"
              onClick={() => {}}
              aria-label="筛选运动"
            >
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                <path d="M17 4h2v10h-2V4zm0 12h-2v2h2v2h2v-2h2v-2h-4zm-4-6h-2v10h2V10zm-8 2H3v2h2v6h2v-6h2v-2H5zm8-8h-2v2H9v2h6V6h-2V4zM5 4h2v6H5V4z" fill="currentColor"/>
              </svg>
            </button>
             
            <button 
              className="action-icon-btn add-btn"
              onClick={handleAddExercise}
              aria-label="添加运动"
            >
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                <path d="M3 3h18v18H3V3zm16 16V5H5v14h14zm-6-8h4v2h-4v4h-2v-4H7v-2h4V7h2v4z" fill="currentColor"/>
              </svg>
            </button>
          </div>
        </div>
        
        {/* 🔧 优化的肌肉群筛选栏 */}
        <div className="muscle-filter-bar">
          <div className="filter-icon-fixed">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
              <path d="M17 4h2v10h-2V4zm0 12h-2v2h2v2h2v-2h2v-2h-4zm-4-6h-2v10h2V10zm-8 2H3v2h2v6h2v-6h2v-2H5zm8-8h-2v2H9v2h6V6h-2V4zM5 4h2v6H5V4z" fill="currentColor"/>
            </svg>
          </div>

          <div className="muscle-filter-scroll">
            {getDisplayOrderedBodyParts().map(bodyPart => (
              <button
                key={bodyPart.id}
                className={`muscle-filter-btn ${selectedMuscleGroup === bodyPart.name ? 'active' : ''}`}
                onClick={() => handleMuscleGroupSelect(bodyPart.name)}
                title={`筛选${bodyPart.name}动作`}
                style={{
                  // 🍎 iOS触摸目标优化
                  minHeight: 'var(--ios-touch-target)',
                  minWidth: 'var(--ios-touch-target)'
                }}
              >
                <BodyPartIcon bodyPartName={bodyPart.name} />
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* 最近执行区域 */}
      <div className="recent-performed-section">
        <div className="section-header">
          <h2>最近执行</h2>
          <button className="view-all-btn">查看全部</button>
        </div>
        
        <div className="recent-exercises-scroll">
          {recentPerformedExercises.map(exercise => (
            <div key={exercise.id} className="recent-exercise-card">
              <div className="recent-exercise-image">
                <BodyPartIcon bodyPartName={exercise.muscleGroup} />
              </div>
              <div className="recent-exercise-info">
                <h4>{exercise.name}</h4>
                <p>{exercise.lastPerformed}</p>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Results Count */}
      <div className="results-info">
        <h2 className="section-title">
          {selectedMuscleGroup ? `${selectedMuscleGroup}动作` : '动作列表'}
          <span className="results-count">({sortedExercises.length})</span>
        </h2>
      </div>

      {/* Loading State */}
      {loading && (
        <div className="loading-state">
          <div className="loading-spinner">
            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <circle cx="12" cy="12" r="10"/>
              <path d="M12 2v4M12 18v4M4.93 4.93l2.83 2.83M16.24 16.24l2.83 2.83M2 12h4M18 12h4M4.93 19.07l2.83-2.83M16.24 7.76l2.83-2.83"/>
            </svg>
          </div>
          <p>正在加载运动数据...</p>
          {isFromCache && (
            <p className="cache-indicator">📦 来自缓存</p>
          )}
        </div>
      )}

      {/* Error State */}
      {error && (
        <div className="error-state">
          <div className="error-icon">
            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <circle cx="12" cy="12" r="10"/>
              <line x1="15" y1="9" x2="9" y2="15"/>
              <line x1="9" y1="9" x2="15" y2="15"/>
            </svg>
          </div>
          <h3>加载失败</h3>
          <p>{error || '加载失败，请稍后重试'}</p>
          <button 
            className="retry-btn"
            onClick={handleRetry}
          >
            重试
          </button>
        </div>
      )}

      {/* Empty State */}
      {!loading && !error && sortedExercises.length === 0 && (
        <div className="empty-state">
          <div className="empty-icon">
            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <circle cx="12" cy="12" r="10"/>
              <path d="M12 8v4M12 16h.01"/>
            </svg>
          </div>
          <h3>暂无运动数据</h3>
          <p>尝试调整筛选条件或搜索关键词</p>
          <button 
            className="reset-btn"
            onClick={handleResetData}
          >
            重置筛选
          </button>
        </div>
      )}

      {/* Exercises Grid */}
      {!loading && !error && sortedExercises.length > 0 && (
        <>
          <div className="exercises-grid">
            {sortedExercises.map(exercise => (
              <ExerciseCard
                key={exercise.id}
                exercise={exercise}
                onToggleFavorite={handleToggleFavorite}
                onExerciseClick={handleExerciseClick}
                getDifficultyDisplay={getDifficultyDisplay}
              />
            ))}
            
            {loadingMore && (
              <SkeletonCard count={4} />
            )}
          </div>
          
          <div ref={sentinelRef} className="scroll-sentinel" />
          
          {/* 🔧 增强的调试面板 */}
          {process.env.NODE_ENV === 'development' && (
            <div className="debug-panel">
              <h3>🔧 调试工具</h3>
              <div className="debug-info">
                <p>当前数据量: {exercises.length}</p>
                <p>排序后数据量: {sortedExercises.length}</p>
                <p>hasMore: {hasMore ? '是' : '否'}</p>
                <p>loadingMore: {loadingMore ? '是' : '否'}</p>
                <p>isIntersecting: {isIntersecting ? '是' : '否'}</p>
                <p>selectedMuscleGroup: {selectedMuscleGroup || '全部'}</p>
                <p>编译后筛选条件: {JSON.stringify(compiledFilters)}</p>
                <p>活跃筛选器数量: {getActiveFilterCount()}</p>
                <p>筛选器状态: {JSON.stringify(filters)}</p>
                <p>缓存状态: 来自缓存={isFromCache ? '是' : '否'}, 时间戳={cacheTimestamp}</p>
              </div>
              <div className="debug-buttons">
                <button onClick={handleManualLoadMore} disabled={loadingMore}>
                  手动加载更多
                </button>
                <button onClick={handleResetData}>
                  重置筛选
                </button>
                <button onClick={() => exerciseService.clearCache()}>
                  清除缓存
                </button>
                <button onClick={() => console.log('🎯 当前状态:', {
                  selectedMuscleGroup,
                  compiledFilters,
                  filters,
                  exercisesCount: exercises.length,
                  hasActiveFilters,
                  isFromCache,
                  cacheTimestamp
                })}>
                  打印状态
                </button>
              </div>
            </div>
          )}
        </>
      )}

      {/* 搜索模态框 */}
      {showSearchModal && (
        <div className="modal-overlay" onClick={() => setShowSearchModal(false)}>
          <div className="search-modal" onClick={e => e.stopPropagation()}>
            <div className="modal-header">
              <h3>搜索运动</h3>
              <button 
                className="close-btn"
                onClick={() => setShowSearchModal(false)}
              >
                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                  <line x1="18" y1="6" x2="6" y2="18"/>
                  <line x1="6" y1="6" x2="18" y2="18"/>
                </svg>
              </button>
            </div>
            
            <div className="search-content">
              <div className="search-bar">
                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                  <circle cx="11" cy="11" r="8"/>
                  <path d="21 21L16.65 16.65"/>
                </svg>
                <input
                  type="text"
                  placeholder="搜索运动名称..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  autoFocus
                />
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ExercisesPage;
