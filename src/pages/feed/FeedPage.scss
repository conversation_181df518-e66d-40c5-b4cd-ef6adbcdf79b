// Feed Page Styles
.feed-page {
  max-width: 100vw; // 改为全屏宽度
  margin: 0;
  padding: 0;
  min-height: 100vh;
}

// Feed Header
.feed-header {
  margin-bottom: var(--space-6);
  padding: 0 var(--space-4); // 添加内边距
  
  .feed-title {
    margin-bottom: var(--space-4);
    
    h1 {
      font-size: var(--text-2xl);
      font-weight: var(--font-bold);
      color: var(--text-primary);
      margin: 0 0 var(--space-2) 0;
    }
    
    p {
      color: var(--text-secondary);
      font-size: var(--text-base);
      margin: 0;
    }
  }
  
  .feed-filters {
    display: flex;
    gap: var(--space-2);
    padding: var(--space-1);
    background: var(--bg-surface);
    border: 1px solid var(--primary-500);
    border-radius: var(--radius-lg);
    
    .filter-btn {
      flex: 1;
      padding: var(--space-2) var(--space-4);
      border: none;
      border-radius: var(--radius-md);
      background: transparent;
      color: var(--text-secondary);
      font-size: var(--text-sm);
      font-weight: var(--font-medium);
      cursor: pointer;
      transition: all var(--transition-normal) var(--ease-in-out);
      
      &:hover {
        background: var(--bg-hover);
        color: var(--text-primary);
      }
      
      &.active {
        background: var(--accent-500);
        color: var(--text-on-accent);
      }
    }
  }
}



// Feed Posts - 移除边框，全宽度显示
.feed-posts {
  display: flex;
  flex-direction: column;
  gap: 0; // 移除间距
  width: 100%;
}

// Individual Feed Post - 移除边框和圆角，全宽度显示
.feed-post {
  background: var(--card-bg);
  border: none; // 移除边框
  border-bottom: 1px solid var(--border-light); // 仅保留底部分割线
  border-radius: 0; // 移除圆角
  padding: var(--space-4) var(--space-4); // 调整内边距
  transition: none; // 移除悬停效果
  width: 100%;
  
  &:hover {
    border-color: var(--border-light); // 移除悬停效果
    transform: none;
    box-shadow: none;
  }
}

// Post Header - 更新为HeroUI Avatar布局
.post-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-4);
  // 移除分割线
  border-bottom: none;
  padding-bottom: 0;

  // 左侧：用户头像 + 用户名称 + 发布时间
  .post-user-section {
    display: flex;
    align-items: center;
    gap: var(--space-3);
    flex: 1;

    // HeroUI Avatar样式调整
    .user-avatar {
      flex-shrink: 0;
      
      // 确保HeroUI Avatar正确显示
      &.heroui-avatar {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        overflow: hidden;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        
        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }
    }

    .user-info {
      flex: 1;
      min-width: 0;

      .user-name-row {
        margin-bottom: var(--space-1);

        .user-name {
          display: inline-flex;
          align-items: center;
          gap: var(--space-1);
          font-size: var(--text-base);
          font-weight: var(--font-semibold);
          color: var(--text-primary);

          .verified-icon {
            width: 1rem;
            height: 1rem;
            color: var(--accent-500);
            flex-shrink: 0;
          }
        }
      }

      .post-timestamp {
        font-size: var(--text-sm);
        color: var(--text-tertiary);
        line-height: 1.2;
      }
    }
  }

  // 右侧：HeroUI Button关注按钮
  .post-actions {
    flex-shrink: 0;

    .follow-btn {
      font-size: var(--text-xs); // 减小字体大小
      padding: var(--space-1-5) var(--space-3); // 调整内边距
      min-height: auto; // 覆盖HeroUI默认高度
      height: auto;
      
      // 确保HeroUI Button bordered样式正确显示
      &.heroui-button {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        border-radius: 6px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s ease;
        
        // 未关注状态 - 蓝色边框
        &.not-following {
          border: 2px solid var(--accent-500, #3b82f6) !important;
          background: transparent !important;
          color: var(--accent-500, #3b82f6) !important;
          
          &:hover {
            background: var(--bg-hover, rgba(59, 130, 246, 0.1)) !important;
            border-color: var(--accent-600, #2563eb) !important;
          }
        }
        
        // 已关注状态 - 灰色边框
        &.following {
          border: 2px solid var(--primary-400, #94a3b8) !important;
          background: transparent !important;
          color: var(--primary-600, #475569) !important;
          
          &:hover {
            background: var(--bg-hover, rgba(148, 163, 184, 0.1)) !important;
            border-color: var(--primary-500, #64748b) !important;
          }
        }
      }
    }
  }
}

// Post Content
.post-content {
  margin-bottom: var(--space-4);

  .post-text {
    font-size: var(--text-base);
    line-height: var(--leading-relaxed);
    color: var(--text-primary);
    margin: 0 0 var(--space-4) 0;
  }

  // 训练统计信息 - 使用独立的WorkoutStatsCard组件
  .feed-workout-stats {
    margin-bottom: var(--space-2); // 进一步优化间距，确保与下方区域协调
  }

  .post-image {
    margin-bottom: var(--space-4);
    border-radius: var(--radius-lg);
    overflow: hidden;

    img {
      width: 100%;
      height: auto;
      display: block;
    }
  }
}

// 训练记录区域 - 现在使用WorkoutCarousel组件  
.workout-carousel-wrapper {
  margin-top: var(--space-2); // 优化间距：减少到原来的50%（从space-4减少到space-2）
  border-radius: var(--radius-lg);
  overflow: visible; // 改为visible，防止内容被裁剪
}

// 移除原来的 workout-carousel-section 样式，使用新的 training-record-section

// Post Actions
.post-actions {
  display: flex;
  justify-content: space-around;
  padding-top: var(--space-4);
  // 移除顶部边框线
  border-top: none;
  
  .action-btn {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    padding: var(--space-2) var(--space-3);
    background: transparent;
    border: none;
    border-radius: var(--radius-md);
    color: var(--text-secondary);
    font-size: var(--text-sm);
    font-weight: var(--font-medium);
    cursor: pointer;
    transition: all var(--transition-normal) var(--ease-in-out);
    
    svg {
      width: 1.125rem;
      height: 1.125rem;
      stroke-width: 2;
    }
    
    &:hover {
      background: var(--primary-600);
      color: var(--text-primary);
      transform: translateY(-1px);
    }
    
    &.like-btn {
      &.liked {
        color: var(--error-500);
        
        svg {
          fill: currentColor;
        }
      }
      
      &:hover {
        color: var(--error-500);
      }
    }
    
    &.comment-btn:hover {
      color: var(--accent-500);
    }
    
    &.share-btn:hover {
      color: var(--success-500);
    }
  }
}

// Load More
.load-more {
  display: flex;
  justify-content: center;
  margin: var(--space-8) 0;
  
  .load-more-btn {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    padding: var(--space-3) var(--space-6);
    background: var(--bg-surface);
    border: 1px solid var(--primary-500);
    border-radius: var(--radius-lg);
    color: var(--text-secondary);
    font-size: var(--text-base);
    font-weight: var(--font-medium);
    cursor: pointer;
    transition: all var(--transition-normal) var(--ease-in-out);
    
    svg {
      width: 1.25rem;
      height: 1.25rem;
      stroke-width: 2;
    }
    
    &:hover {
      background: var(--primary-600);
      color: var(--text-primary);
      border-color: var(--accent-500);
      transform: translateY(-2px);
    }
  }
}

// Responsive Design - 更新为全宽度布局
@media (max-width: 768px) {
  .feed-page {
    padding: 0; // 移除内边距，保持全宽度
  }
  
  .feed-header {
    padding: 0 var(--space-3); // 减少内边距
    
    .feed-title h1 {
      font-size: var(--text-xl);
    }
    
    .feed-filters {
      .filter-btn {
        padding: var(--space-2);
        font-size: var(--text-xs);
      }
    }
  }
  

  
  .feed-post {
    padding: var(--space-3); // 减少内边距
  }
  
  .post-header {
    .post-user-section {
      gap: var(--space-2); // 减少间距
      
      .user-info .user-name {
        font-size: var(--text-sm);
      }
    }
    
    .post-actions .follow-btn {
      font-size: var(--text-xs);
      padding: var(--space-1) var(--space-2);
    }
  }
  
  .workout-stats-summary {
    padding: var(--space-2) 0; // 移动端适度减少内边距
    gap: var(--space-1-5); // 移动端紧凑间距
    margin-bottom: var(--space-4); // 移动端适度减少底部间距
    
    .workout-stat {
      // 移动端精确计算: stat-label (12px * 1.2) + margin (4px) + stat-value (16px * 1.2) 
      // = 14.4 + 4 + 19.2 = 37.6px, 约38px
      height: 38px; // 移动端固定高度
      
      .stat-value {
        font-size: var(--text-base); // 16px移动端
        line-height: 1.2; // 16px * 1.2 = 19.2px
      }
      
      .stat-label {
        font-size: var(--text-xs); // 12px保持不变
        margin-bottom: var(--space-1); // 4px保持不变
        line-height: 1.2; // 12px * 1.2 = 14.4px
      }
    }
    
    .stats-divider {
      height: 38px; // 与移动端workout-stat精确高度保持一致
      margin: 0 var(--space-1); // 移动端更紧凑的间距
      
      &.heroui-divider.vertical {
        height: 100% !important; // 确保填满移动端容器高度
      }
    }
  }
  
  .post-actions {
    .action-btn {
      padding: var(--space-2);
      font-size: var(--text-xs);
      
      svg {
        width: 1rem;
        height: 1rem;
      }
    }
  }
  

  
          // 训练记录区域移动端调整 - 现在使用WorkoutCarousel组件
  .workout-carousel-wrapper {
    margin-top: var(--space-1-5); // 移动端优化间距：减少到原来的50%
  }
}

// Loading States
.feed-post.loading {
  .post-content,
  .post-actions {
    opacity: 0.7;
    pointer-events: none;
  }
}

// Animation for new posts
@keyframes slideInFromTop {
  from {
    opacity: 0;
    transform: translateY(-1rem);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.feed-post.new-post {
  animation: slideInFromTop 0.3s ease-out;
}

// Focus states for accessibility
.filter-btn:focus,
.action-btn:focus,
.load-more-btn:focus {
  outline: 2px solid var(--accent-500);
  outline-offset: 2px;
}

// Reduced motion
@media (prefers-reduced-motion: reduce) {
  .feed-post,
  .action-btn,
  .load-more-btn {
    transition: none;
  }
  
  .feed-post:hover,
  .action-btn:hover,
  .load-more-btn:hover {
    transform: none;
  }
  
  .feed-post.new-post {
    animation: none;
  }
}

// High contrast mode
@media (prefers-contrast: high) {
  .feed-post {
    border-bottom-width: 2px;
  }

  .workout-stats-summary,
  .workout-carousel-wrapper {
    border-width: 2px;
  }
}

// 已移除旧的 workout-carousel-section 样式，使用新的 training-record-section

// === 新增：加载状态样式 ===

// Loading States
.loading-state,
.error-state,
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--space-12) var(--space-6);
  text-align: center;
  min-height: 300px;
}

.loading-state {
  .loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid var(--border-color);
    border-top: 3px solid var(--primary-500);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: var(--space-4);
  }

  p {
    color: var(--text-secondary);
    font-size: var(--text-base);
    margin: 0;
  }
}

.error-state {
  .error-icon {
    font-size: 48px;
    margin-bottom: var(--space-4);
  }

  p {
    color: var(--text-secondary);
    font-size: var(--text-base);
    margin: 0 0 var(--space-4) 0;
    max-width: 300px;
  }

  .retry-btn {
    padding: var(--space-2) var(--space-4);
    background: var(--primary-500);
    color: white;
    border: none;
    border-radius: var(--radius-md);
    font-size: var(--text-sm);
    font-weight: var(--font-medium);
    cursor: pointer;
    transition: background-color var(--transition-normal);

    &:hover {
      background: var(--primary-600);
    }
  }
}

.empty-state {
  .empty-icon {
    font-size: 48px;
    margin-bottom: var(--space-4);
    opacity: 0.6;
  }

  p {
    color: var(--text-secondary);
    font-size: var(--text-base);
    margin: 0 0 var(--space-4) 0;
  }

  .refresh-btn {
    padding: var(--space-2) var(--space-4);
    background: var(--bg-surface);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    font-size: var(--text-sm);
    font-weight: var(--font-medium);
    cursor: pointer;
    transition: all var(--transition-normal);

    &:hover {
      background: var(--bg-hover);
      border-color: var(--primary-300);
    }
  }
}

.loading-more {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: var(--space-6);

  .loading-spinner.small {
    width: 24px;
    height: 24px;
    border: 3px solid var(--border-color);
    border-top: 3px solid var(--primary-500);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: var(--space-2);
  }

  p {
    color: var(--text-secondary);
    font-size: var(--text-sm);
    margin: 0;
  }
}

// Animations
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// 响应式优化
@media (max-width: 768px) {
  .loading-state,
  .error-state,
  .empty-state {
    padding: var(--space-8) var(--space-4);
    min-height: 200px;
  }
}