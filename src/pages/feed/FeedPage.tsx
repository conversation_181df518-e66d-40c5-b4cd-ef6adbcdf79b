import React, { useState, useMemo, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { FeedFilterType } from '../../models/common/Filter';
import { WorkoutCarousel } from '../../components/fitness/WorkoutCarousel/WorkoutCarousel';
import { WorkoutStatsCard } from '../../components/fitness/WorkoutStatsCard/WorkoutStatsCard';
import { CommunityService } from '../../services/communityService';
import { ServiceCacheAdapter } from '../../services/cache/ServiceCacheAdapter';
import { useFilteredInfiniteFeed } from '../../hooks/useInfiniteFeed';
import { useAuthState } from '../../hooks/useAuthState';
import { PostUserSection, PostActions } from '../../components/common';
import { runIOSNetworkDiagnostics, generateDiagnosticsReport } from '../../utils/iosNetworkDiagnostics';
import './FeedPage.scss';



const FeedPage: React.FC = () => {
  const navigate = useNavigate();
  
  // 筛选状态
  const [filter, setFilter] = useState<FeedFilterType>('all');
  
  // 认证状态管理
  const { isAuthenticated, checkAuthStatus, showLoginModal } = useAuthState();
  
  // 使用新的无限分页Hook
  const {
    posts,
    loading,
    error,
    hasMore,
    loadMore,
    refresh,
    mutate
  } = useFilteredInfiniteFeed(filter);

  // 获取ServiceCacheAdapter实例
  const cacheAdapter = useMemo(() => ServiceCacheAdapter.getInstance(), []);



  // 处理点赞（乐观更新 + 缓存失效 + 认证检查）
  const handleLike = useCallback(async (postId: string) => {
    const targetPost = posts.find(post => post.id === postId);
    if (!targetPost) return;

    // 认证检查
    const authOk = await checkAuthStatus();
    if (!authOk) {
      console.warn('【FeedPage】用户未认证，无法点赞');
      showLoginModal();
      return;
    }

    const wasLiked = targetPost.isLiked;
    
    try {
      console.log('【FeedPage】点赞帖子:', { postId, wasLiked, isAuthenticated });

      // 1. 乐观更新本地状态
      const optimisticPosts = posts.map(post => {
        if (post.id === postId) {
          return {
            ...post,
            isLiked: !post.isLiked,
            stats: {
              ...post.stats,
              likes: post.isLiked ? post.stats.likes - 1 : post.stats.likes + 1
            }
          };
        }
        return post;
      });

      // 立即更新UI
      await mutate(optimisticPosts);

      // 2. 调用API
      await CommunityService.togglePostLike(postId);

      // 3. 通知缓存适配器处理失效
      await cacheAdapter.handleUserLike(postId, !wasLiked);

      console.log('【FeedPage】点赞成功:', { postId, newState: !wasLiked });

    } catch (error) {
      console.error('【FeedPage】点赞失败:', error);
      
      // 4. 错误时回滚乐观更新
      await mutate(posts);
      
      // 可以添加错误提示
      // showErrorToast('点赞失败，请稍后重试');
    }
  }, [posts, mutate, cacheAdapter, checkAuthStatus, showLoginModal, isAuthenticated]);

  // 处理训练详情跳转
  const handleWorkoutDetail = useCallback((postId: string, feedPost: any) => {
    try {
      // 跳转到WorkoutDetailPage，传递postId和完整的feedPost数据
      navigate(`/workout/${postId}`, {
        state: {
          initialData: feedPost // 传递完整的FeedPost对象
        }
      });
    } catch (error) {
      console.error('【FeedPage】跳转到训练详情失败:', error);
    }
  }, [navigate]);

  // 处理关注/取消关注（乐观更新 + 认证检查）
  const handleFollow = useCallback(async (userId: string, isCurrentlyFollowing: boolean) => {
    // 认证检查
    const authOk = await checkAuthStatus();
    if (!authOk) {
      console.warn('【FeedPage】用户未认证，无法进行关注操作');
      showLoginModal();
      return;
    }

    try {
      console.log('【FeedPage】关注状态切换:', { userId, isCurrentlyFollowing, isAuthenticated });

      // 1. 乐观更新本地状态
      const optimisticPosts = posts.map(post => {
        if (post.user.id === userId) {
          return {
            ...post,
            user: {
              ...post.user,
              isFollowing: !isCurrentlyFollowing
            }
          };
        }
        return post;
      });

      // 立即更新UI
      await mutate(optimisticPosts);

      // 2. 调用API（待实现）
      // await CommunityService.toggleFollow(userId);

      // 3. 通知缓存适配器处理失效
      await cacheAdapter.handleUserFollow(userId, !isCurrentlyFollowing);
      
      console.log(`【FeedPage】${isCurrentlyFollowing ? '取消关注' : '关注'}成功`);

    } catch (error) {
      console.error('【FeedPage】关注操作失败:', error);
      
      // 错误时回滚
      await mutate(posts);
    }
  }, [posts, mutate, cacheAdapter, checkAuthStatus, showLoginModal, isAuthenticated]);

  // 处理评论操作
  const handleComment = useCallback(async (postId: string) => {
    // 认证检查
    const authOk = await checkAuthStatus();
    if (!authOk) {
      console.warn('【FeedPage】用户未认证，无法评论');
      showLoginModal();
      return;
    }

    try {
      console.log('【FeedPage】评论帖子:', { postId });
      // TODO: 实现评论功能，可能跳转到评论页面或打开评论模态框
      // navigate(`/post/${postId}/comments`);
    } catch (error) {
      console.error('【FeedPage】评论操作失败:', error);
    }
  }, [checkAuthStatus, showLoginModal]);

  // 处理分享操作
  const handleShare = useCallback(async (postId: string) => {
    try {
      console.log('【FeedPage】分享帖子:', { postId });
      
      const targetPost = posts.find(post => post.id === postId);
      if (!targetPost) return;

      if (navigator.share) {
        await navigator.share({
          title: `${targetPost.user.name}的训练记录`,
          text: targetPost.content.text || '来看看这个精彩的训练记录！',
          url: `${window.location.origin}/workout/${postId}`
        });
      } else {
        // 降级到复制链接
        await navigator.clipboard.writeText(`${window.location.origin}/workout/${postId}`);
        console.log('分享链接已复制到剪贴板');
        // TODO: 显示成功提示
      }
    } catch (error) {
      console.error('【FeedPage】分享操作失败:', error);
    }
  }, [posts]);

  // 处理训练记录轮播切换
  const handleTrainingCarouselChange = useCallback((postId: string, index: number) => {
    console.log('【FeedPage】训练记录轮播切换:', { postId, index });
    // 这里可以追踪用户浏览行为
  }, []);

  // 处理筛选器变化
  const handleFilterChange = useCallback((newFilter: FeedFilterType) => {
    console.log('【FeedPage】筛选器变化:', { from: filter, to: newFilter });
    setFilter(newFilter);
  }, [filter]);

  // 刷新数据
  const handleRefresh = useCallback(async () => {
    console.log('【FeedPage】手动刷新数据');

    try {
      await refresh();
    } catch (error) {
      console.error('【FeedPage】刷新失败，开始网络诊断:', error);

      // 如果刷新失败，运行网络诊断
      try {
        const diagnostics = await runIOSNetworkDiagnostics();
        const report = generateDiagnosticsReport(diagnostics);
        console.log('【FeedPage】网络诊断报告:\n', report);

        // 可以在这里显示用户友好的错误信息
        if (!diagnostics.connectivityTest.canReachServer) {
          console.error('【FeedPage】服务器连接失败，请检查网络设置');
        } else if (!diagnostics.httpTest.canMakeHttpRequest) {
          console.error('【FeedPage】HTTP请求失败，可能是认证或权限问题');
        } else if (!diagnostics.authTest.isAuthenticated) {
          console.error('【FeedPage】认证失败，请重新登录');
        }
      } catch (diagError) {
        console.error('【FeedPage】网络诊断失败:', diagError);
      }
    }
  }, [refresh]);

  // 加载更多
  const handleLoadMore = useCallback(async () => {
    if (!loading && hasMore) {
      console.log('【FeedPage】加载更多数据');
      await loadMore();
    }
  }, [loading, hasMore, loadMore]);

  // 过滤逻辑现在在useFilteredInfiniteFeed中处理
  const filteredPosts = posts;

  return (
    <div className="feed-page">

      
      {/* Feed Header */}
      <div className="feed-header">
        <div className="feed-title">
          <h1>动态</h1>
          <p>发现健身伙伴的最新动态</p>
        </div>

        {/* Filter Tabs */}
        <div className="feed-filters">
          <button
            className={`filter-btn ${filter === 'all' ? 'active' : ''}`}
            onClick={() => handleFilterChange('all')}
          >
            全部动态
          </button>

          <button
            className={`filter-btn ${filter === 'following' ? 'active' : ''}`}
            onClick={() => handleFilterChange('following')}
          >
            关注的人
          </button>
        </div>
      </div>

      {/* Loading State */}
      {loading && posts.length === 0 && (
        <div className="loading-state">
          <div className="loading-spinner"></div>
          <p>正在加载动态...</p>
        </div>
      )}

      {/* Error State */}
      {error && (
        <div className="error-state">
          <div className="error-icon">⚠️</div>
          <p>{error.message || '加载失败，请稍后重试'}</p>
          <button className="retry-btn" onClick={handleRefresh}>
            重试
          </button>
        </div>
      )}

      {/* Empty State */}
      {!loading && !error && posts.length === 0 && (
        <div className="empty-state">
          <div className="empty-icon">📝</div>
          <p>暂无动态内容</p>
          <button className="refresh-btn" onClick={handleRefresh}>
            刷新
          </button>
        </div>
      )}



      {/* Feed Posts */}
      <div className="feed-posts">
        {filteredPosts.map(post => (
          <article key={post.id} className="feed-post">
            {/* Post Header - 使用统一的PostUserSection组件 */}
            <div className="post-header">
              <PostUserSection
                user={{
                  id: post.user.id,
                  name: post.user.name,
                  avatar: post.user.avatar,
                  isVerified: post.user.isVerified || false,
                  isFollowing: post.user.isFollowing
                }}
                timestamp={post.timestamp.toISOString()}
                showFollowButton={!post.user.isFollowing}
                onFollow={handleFollow}
                className="feed-user-section"
              />
            </div>

            {/* Post Content */}
            <div className="post-content">
              {post.content.text && (
                <p className="post-text">{post.content.text}</p>
              )}

              {/* 训练统计信息 - 使用独立的WorkoutStatsCard组件 */}
              {post.content.workout && (
                <WorkoutStatsCard
                  workout={post.content.workout}
                  className="feed-workout-stats"
                  showDividers={true}
                  stats={['duration', 'weight', 'calories']}
                  onClick={() => handleWorkoutDetail(post.id, post)}
                  clickable={true}
                />
              )}

                              {/* 训练记录轮播图 - 使用WorkoutCarousel组件 */}
                {post.content.workout && post.content.carousel_items && post.content.carousel_items.length > 0 && (
                  <WorkoutCarousel
                    items={[
                      ...post.content.carousel_items,
                      ...(post.images || []).map((imageUrl, index) => ({
                        id: `user-image-${index}`,
                        type: 'user_image' as const,
                        content: {
                          image_data: {
                            url: imageUrl,
                            alt: `用户上传图片 ${index + 1}`,
                            caption: '用户分享'
                          }
                        }
                      }))
                    ]}
                    autoPlay={false}
                    showIndicators={true}
                    className="workout-carousel-wrapper"
                    onItemChange={(index: number) => handleTrainingCarouselChange(post.id, index)}
                  />
                )}
            </div>

            {/* Post Actions - 使用统一的PostActions组件 */}
            <PostActions
              stats={{
                likes: post.stats.likes,
                comments: post.stats.comments,
                views: post.stats.shares, // 使用shares作为views
                isLiked: post.isLiked
              }}
              onLike={() => handleLike(post.id)}
              onComment={() => handleComment(post.id)}
              onShare={() => handleShare(post.id)}
              className="feed-post-actions"
            />
          </article>
        ))}
      </div>

      {/* Load More */}
      {hasMore && !loading && posts.length > 0 && (
        <div className="load-more">
          <button className="load-more-btn" onClick={handleLoadMore}>
            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <path d="M1 12S5 4 12 4S23 12 23 12S19 20 12 20S1 12 1 12Z"/>
              <circle cx="12" cy="12" r="3"/>
            </svg>
            查看更多动态
          </button>
        </div>
      )}

      {/* Loading More */}
      {loading && posts.length > 0 && (
        <div className="loading-more">
          <div className="loading-spinner small"></div>
          <p>加载更多...</p>
        </div>
      )}
    </div>
  );
};

export default FeedPage; 