/**
 * 底部导航栏可见性监控工具
 * 专门用于诊断和修复iOS应用中底部导航栏初始不可见问题
 */

// import { Capacitor } from '@capacitor/core'; // 暂时不使用

interface VisibilityState {
  // DOM存在性
  elementExists: boolean;
  parentExists: boolean;
  
  // CSS可见性属性
  display: string;
  visibility: string;
  opacity: string;
  zIndex: string;
  position: string;
  bottom: string;
  transform: string;
  
  // 计算属性
  computedHeight: number;
  computedWidth: number;
  boundingRect: DOMRect | null;
  
  // 可见性判断
  isVisible: boolean;
  isInViewport: boolean;
  hasValidDimensions: boolean;
  
  // React状态
  reactMounted: boolean;
  hasChildren: boolean;
  childrenCount: number;
}

interface VisibilityCheckResult {
  timestamp: number;
  checkCount: number;
  state: VisibilityState;
  issues: string[];
  recommendations: string[];
}

/**
 * 底部导航可见性监控器
 */
export class BottomNavVisibilityMonitor {
  private startTime: number = 0;
  private checkCount: number = 0;
  private monitorInterval?: NodeJS.Timeout;
  private results: VisibilityCheckResult[] = [];
  private visualIndicator?: HTMLElement;
  private config: {
    maxMonitorTime: number;
    checkInterval: number;
    enableConsoleLog: boolean;
    enableVisualIndicator: boolean;
    enableAutoFix: boolean;
  };

  constructor(config: Partial<typeof BottomNavVisibilityMonitor.prototype.config> = {}) {
    this.config = {
      maxMonitorTime: 5000,
      checkInterval: 100,
      enableConsoleLog: true,
      enableVisualIndicator: process.env.NODE_ENV === 'development',
      enableAutoFix: true,
      ...config
    };
  }

  /**
   * 开始监控底部导航可见性
   */
  public start(): Promise<VisibilityCheckResult[]> {
    return new Promise((resolve) => {
      this.startTime = Date.now();
      this.checkCount = 0;
      this.results = [];

      if (this.config.enableConsoleLog) {
        console.log('🔍 开始底部导航可见性监控...');
      }

      if (this.config.enableVisualIndicator) {
        this.createVisualIndicator();
      }

      this.monitorInterval = setInterval(() => {
        const result = this.performVisibilityCheck();
        this.results.push(result);

        const elapsed = Date.now() - this.startTime;
        const isVisible = result.state.isVisible;

        // 如果启用自动修复且检测到问题
        if (this.config.enableAutoFix && !isVisible && result.issues.length > 0) {
          this.attemptAutoFix(result);
        }

        if (isVisible || elapsed >= this.config.maxMonitorTime) {
          this.stop();
          
          if (this.config.enableConsoleLog) {
            if (isVisible) {
              console.log(`✅ 底部导航可见性正常，用时${elapsed}ms，检查${this.checkCount}次`);
            } else {
              console.warn(`⚠️ 底部导航可见性异常，监控超时(${elapsed}ms)，检查${this.checkCount}次`);
              this.logDetailedAnalysis();
            }
          }

          resolve(this.results);
        }
      }, this.config.checkInterval);
    });
  }

  /**
   * 执行可见性检查
   */
  private performVisibilityCheck(): VisibilityCheckResult {
    this.checkCount++;
    const timestamp = Date.now() - this.startTime;

    // 查找底部导航元素
    const element = document.querySelector('.bottom-navigation') as HTMLElement;
    const parent = element?.parentElement;

    let state: VisibilityState;
    let issues: string[] = [];
    let recommendations: string[] = [];

    if (!element) {
      state = this.createEmptyState();
      issues.push('底部导航元素不存在于DOM中');
      recommendations.push('检查React组件是否正确渲染');
      recommendations.push('检查路由配置是否正确');
    } else {
      state = this.analyzeElementState(element, parent);
      const analysis = this.analyzeVisibilityIssues(state);
      issues = analysis.issues;
      recommendations = analysis.recommendations;
    }

    const result: VisibilityCheckResult = {
      timestamp,
      checkCount: this.checkCount,
      state,
      issues,
      recommendations
    };

    if (this.config.enableConsoleLog) {
      this.logCheckResult(result);
    }

    if (this.visualIndicator) {
      this.updateVisualIndicator(result);
    }

    return result;
  }

  /**
   * 分析元素状态
   */
  private analyzeElementState(element: HTMLElement, parent: HTMLElement | null): VisibilityState {
    const computedStyle = getComputedStyle(element);
    const rect = element.getBoundingClientRect();

    return {
      // DOM存在性
      elementExists: true,
      parentExists: !!parent,
      
      // CSS可见性属性
      display: computedStyle.display,
      visibility: computedStyle.visibility,
      opacity: computedStyle.opacity,
      zIndex: computedStyle.zIndex,
      position: computedStyle.position,
      bottom: computedStyle.bottom,
      transform: computedStyle.transform,
      
      // 计算属性
      computedHeight: parseFloat(computedStyle.height) || 0,
      computedWidth: parseFloat(computedStyle.width) || 0,
      boundingRect: rect,
      
      // 可见性判断
      isVisible: this.isElementVisible(element, computedStyle, rect),
      isInViewport: this.isInViewport(rect),
      hasValidDimensions: rect.width > 0 && rect.height > 0,
      
      // React状态
      reactMounted: element.hasAttribute('data-react-mounted') || element.children.length > 0,
      hasChildren: element.children.length > 0,
      childrenCount: element.children.length
    };
  }

  /**
   * 创建空状态
   */
  private createEmptyState(): VisibilityState {
    return {
      elementExists: false,
      parentExists: false,
      display: '',
      visibility: '',
      opacity: '',
      zIndex: '',
      position: '',
      bottom: '',
      transform: '',
      computedHeight: 0,
      computedWidth: 0,
      boundingRect: null,
      isVisible: false,
      isInViewport: false,
      hasValidDimensions: false,
      reactMounted: false,
      hasChildren: false,
      childrenCount: 0
    };
  }

  /**
   * 判断元素是否可见
   */
  private isElementVisible(_element: HTMLElement, style: CSSStyleDeclaration, rect: DOMRect): boolean {
    // 基础可见性检查
    if (style.display === 'none') return false;
    if (style.visibility === 'hidden') return false;
    if (parseFloat(style.opacity) === 0) return false;
    
    // 尺寸检查
    if (rect.width === 0 || rect.height === 0) return false;
    
    // 位置检查（是否在视口外）
    if (!this.isInViewport(rect)) return false;
    
    // z-index检查（简单的遮挡检测）
    const zIndex = parseInt(style.zIndex) || 0;
    if (zIndex < 0) return false;
    
    return true;
  }

  /**
   * 检查元素是否在视口内
   */
  private isInViewport(rect: DOMRect): boolean {
    const viewport = {
      width: window.innerWidth,
      height: window.innerHeight
    };
    
    return (
      rect.top < viewport.height &&
      rect.bottom > 0 &&
      rect.left < viewport.width &&
      rect.right > 0
    );
  }

  /**
   * 分析可见性问题
   */
  private analyzeVisibilityIssues(state: VisibilityState): { issues: string[], recommendations: string[] } {
    const issues: string[] = [];
    const recommendations: string[] = [];

    if (!state.elementExists) {
      issues.push('元素不存在');
      recommendations.push('检查React组件渲染');
      return { issues, recommendations };
    }

    if (state.display === 'none') {
      issues.push('display: none');
      recommendations.push('设置display: block或flex');
    }

    if (state.visibility === 'hidden') {
      issues.push('visibility: hidden');
      recommendations.push('设置visibility: visible');
    }

    if (parseFloat(state.opacity) === 0) {
      issues.push('opacity: 0');
      recommendations.push('设置opacity: 1');
    }

    if (!state.hasValidDimensions) {
      issues.push('元素尺寸为0');
      recommendations.push('检查CSS高度和宽度设置');
    }

    if (!state.isInViewport) {
      issues.push('元素不在视口内');
      recommendations.push('检查position和bottom属性');
    }

    if (!state.hasChildren) {
      issues.push('元素无子内容');
      recommendations.push('检查React组件内容渲染');
    }

    const zIndex = parseInt(state.zIndex) || 0;
    if (zIndex < 1000) {
      issues.push(`z-index过低(${zIndex})`);
      recommendations.push('提高z-index值到9999以上');
    }

    return { issues, recommendations };
  }

  /**
   * 尝试自动修复
   */
  private attemptAutoFix(_result: VisibilityCheckResult): void {
    const element = document.querySelector('.bottom-navigation') as HTMLElement;
    if (!element) return;

    console.log('🔧 尝试自动修复底部导航可见性问题...');

    // 强制设置可见性样式
    element.style.setProperty('display', 'block', 'important');
    element.style.setProperty('visibility', 'visible', 'important');
    element.style.setProperty('opacity', '1', 'important');
    element.style.setProperty('z-index', '9999', 'important');
    element.style.setProperty('position', 'fixed', 'important');
    element.style.setProperty('bottom', '0', 'important');
    element.style.setProperty('left', '0', 'important');
    element.style.setProperty('right', '0', 'important');

    console.log('✅ 自动修复样式已应用');
  }

  /**
   * 记录检查结果
   */
  private logCheckResult(result: VisibilityCheckResult): void {
    const { timestamp, checkCount, state, issues } = result;
    
    console.log(`📊 底部导航可见性检查 #${checkCount} (${timestamp}ms):`);
    console.log('  状态:', {
      存在: state.elementExists,
      可见: state.isVisible,
      尺寸: `${state.computedWidth}x${state.computedHeight}`,
      位置: state.boundingRect ? `(${state.boundingRect.left}, ${state.boundingRect.top})` : 'N/A'
    });
    
    if (issues.length > 0) {
      console.warn('  问题:', issues);
      console.log('  建议:', result.recommendations);
    }
  }

  /**
   * 创建可视化指示器
   */
  private createVisualIndicator(): void {
    this.visualIndicator = document.createElement('div');
    this.visualIndicator.style.cssText = `
      position: fixed;
      top: 50px;
      right: 10px;
      background: rgba(255, 0, 0, 0.9);
      color: white;
      padding: 8px 12px;
      border-radius: 6px;
      font-family: monospace;
      font-size: 11px;
      z-index: 99999;
      pointer-events: none;
      min-width: 200px;
      max-width: 300px;
    `;
    this.visualIndicator.textContent = '底部导航监控中...';
    document.body.appendChild(this.visualIndicator);
  }

  /**
   * 更新可视化指示器
   */
  private updateVisualIndicator(result: VisibilityCheckResult): void {
    if (!this.visualIndicator) return;

    const { state, issues } = result;
    const status = state.isVisible ? '✅ 可见' : '❌ 不可见';
    const color = state.isVisible ? 'rgba(0, 128, 0, 0.9)' : 'rgba(255, 0, 0, 0.9)';

    this.visualIndicator.innerHTML = `
      <div>底部导航监控 #${result.checkCount}</div>
      <div>状态: ${status}</div>
      <div>尺寸: ${state.computedWidth}x${state.computedHeight}</div>
      <div>问题: ${issues.length}个</div>
      ${issues.length > 0 ? `<div style="font-size: 10px; margin-top: 4px;">${issues.join(', ')}</div>` : ''}
    `;

    this.visualIndicator.style.background = color;
  }

  /**
   * 输出详细分析
   */
  private logDetailedAnalysis(): void {
    console.log('📋 === 底部导航可见性详细分析 ===');
    
    const lastResult = this.results[this.results.length - 1];
    if (lastResult) {
      console.log('最终状态:', lastResult.state);
      console.log('发现问题:', lastResult.issues);
      console.log('修复建议:', lastResult.recommendations);
    }

    // 分析问题趋势
    const issueFrequency = new Map<string, number>();
    this.results.forEach(result => {
      result.issues.forEach(issue => {
        issueFrequency.set(issue, (issueFrequency.get(issue) || 0) + 1);
      });
    });

    console.log('问题频率统计:', Object.fromEntries(issueFrequency));
  }

  /**
   * 停止监控
   */
  public stop(): void {
    if (this.monitorInterval) {
      clearInterval(this.monitorInterval);
      this.monitorInterval = undefined;
    }

    if (this.visualIndicator) {
      this.visualIndicator.remove();
      this.visualIndicator = undefined;
    }
  }

  /**
   * 获取监控结果
   */
  public getResults(): VisibilityCheckResult[] {
    return [...this.results];
  }
}

/**
 * 便捷函数：启动底部导航可见性监控
 */
export const startBottomNavVisibilityMonitoring = (config?: Partial<BottomNavVisibilityMonitor['config']>): Promise<VisibilityCheckResult[]> => {
  const monitor = new BottomNavVisibilityMonitor(config);
  return monitor.start();
};

/**
 * 便捷函数：在应用启动时自动监控
 */
export const autoMonitorBottomNavVisibility = (): void => {
  if (process.env.NODE_ENV === 'development') {
    setTimeout(() => {
      startBottomNavVisibilityMonitoring({
        enableVisualIndicator: true,
        enableConsoleLog: true,
        enableAutoFix: true
      });
    }, 200);
  }
};
