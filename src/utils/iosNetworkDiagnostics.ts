/**
 * iOS网络诊断工具
 * 专门用于诊断iOS端的网络连接问题
 */

import { Capacitor } from '@capacitor/core';
import { Network } from '@capacitor/network';
import { Device } from '@capacitor/device';
import { apiClient } from '../services/core/ApiClient';
import { getApiConfig } from '../config/api.config';

export interface IOSNetworkDiagnostics {
  platform: string;
  isNative: boolean;
  deviceInfo: any;
  networkStatus: any;
  apiConfig: any;
  connectivityTest: {
    canReachServer: boolean;
    error?: string;
    responseTime?: number;
  };
  httpTest: {
    canMakeHttpRequest: boolean;
    error?: string;
    responseTime?: number;
  };
  authTest: {
    isAuthenticated: boolean;
    tokenExists: boolean;
    error?: string;
  };
}

/**
 * 执行完整的iOS网络诊断
 */
export async function runIOSNetworkDiagnostics(): Promise<IOSNetworkDiagnostics> {
  console.log('🔍 开始iOS网络诊断...');
  
  const startTime = Date.now();
  const config = getApiConfig();
  
  // 基础平台信息
  const platform = Capacitor.getPlatform();
  const isNative = Capacitor.isNativePlatform();
  
  let deviceInfo = null;
  let networkStatus = null;
  
  // 获取设备信息
  try {
    if (isNative) {
      deviceInfo = await Device.getInfo();
      networkStatus = await Network.getStatus();
    }
  } catch (error) {
    console.warn('获取设备信息失败:', error);
  }
  
  // 连接性测试
  const connectivityTest = await testServerConnectivity(config.baseURL);
  
  // HTTP请求测试
  const httpTest = await testHttpRequest(config.baseURL);
  
  // 认证测试
  const authTest = await testAuthentication();
  
  const diagnostics: IOSNetworkDiagnostics = {
    platform,
    isNative,
    deviceInfo,
    networkStatus,
    apiConfig: {
      baseURL: config.baseURL,
      timeout: config.timeout,
      retries: config.retries,
      protocol: config.baseURL.startsWith('https:') ? 'HTTPS' : 'HTTP'
    },
    connectivityTest,
    httpTest,
    authTest
  };
  
  const totalTime = Date.now() - startTime;
  
  console.log('🔍 iOS网络诊断完成:', {
    ...diagnostics,
    diagnosticTime: `${totalTime}ms`
  });
  
  return diagnostics;
}

/**
 * 测试服务器连接性
 */
async function testServerConnectivity(baseURL: string): Promise<{
  canReachServer: boolean;
  error?: string;
  responseTime?: number;
}> {
  const startTime = Date.now();
  
  try {
    console.log('🔍 测试服务器连接性:', baseURL);
    
    // 使用简单的fetch测试连接
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 5000);
    
    const response = await fetch(`${baseURL}/api/v1/ping`, {
      method: 'GET',
      signal: controller.signal,
      headers: {
        'Accept': 'application/json'
      }
    });
    
    clearTimeout(timeoutId);
    const responseTime = Date.now() - startTime;
    
    console.log('🔍 服务器连接测试结果:', {
      status: response.status,
      statusText: response.statusText,
      responseTime: `${responseTime}ms`
    });
    
    return {
      canReachServer: response.status < 500,
      responseTime
    };
    
  } catch (error) {
    const responseTime = Date.now() - startTime;
    const errorMessage = error instanceof Error ? error.message : String(error);
    
    console.error('🔍 服务器连接测试失败:', {
      error: errorMessage,
      responseTime: `${responseTime}ms`
    });
    
    return {
      canReachServer: false,
      error: errorMessage,
      responseTime
    };
  }
}

/**
 * 测试HTTP请求
 */
async function testHttpRequest(baseURL: string): Promise<{
  canMakeHttpRequest: boolean;
  error?: string;
  responseTime?: number;
}> {
  const startTime = Date.now();
  
  try {
    console.log('🔍 测试HTTP请求:', `${baseURL}/api/v1/community/posts/`);
    
    // 使用ApiClient测试实际的API请求（iOS安全：数字转字符串）
    const response = await apiClient.get('/api/v1/community/posts/', {
      skip: String(0),
      limit: String(1)
    });
    
    const responseTime = Date.now() - startTime;
    
    console.log('🔍 HTTP请求测试成功:', {
      responseTime: `${responseTime}ms`,
      hasData: !!response
    });
    
    return {
      canMakeHttpRequest: true,
      responseTime
    };
    
  } catch (error) {
    const responseTime = Date.now() - startTime;
    const errorMessage = error instanceof Error ? error.message : String(error);
    
    console.error('🔍 HTTP请求测试失败:', {
      error: errorMessage,
      responseTime: `${responseTime}ms`
    });
    
    return {
      canMakeHttpRequest: false,
      error: errorMessage,
      responseTime
    };
  }
}

/**
 * 测试认证状态
 */
async function testAuthentication(): Promise<{
  isAuthenticated: boolean;
  tokenExists: boolean;
  error?: string;
}> {
  try {
    console.log('🔍 测试认证状态...');
    
    // 检查token是否存在
    const tokenExists = apiClient.isAuthenticated();
    
    // 如果没有token，尝试登录
    if (!tokenExists) {
      console.log('🔍 未找到token，尝试测试用户登录...');
      const { authService } = await import('../services/authService');
      await authService.loginWithTestUser();
    }
    
    const isAuthenticated = apiClient.isAuthenticated();
    
    console.log('🔍 认证测试结果:', {
      tokenExists,
      isAuthenticated
    });
    
    return {
      isAuthenticated,
      tokenExists
    };
    
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    
    console.error('🔍 认证测试失败:', errorMessage);
    
    return {
      isAuthenticated: false,
      tokenExists: false,
      error: errorMessage
    };
  }
}

/**
 * 生成诊断报告
 */
export function generateDiagnosticsReport(diagnostics: IOSNetworkDiagnostics): string {
  const lines = [
    '📱 FitMaster iOS网络诊断报告',
    '========================================',
    '',
    `🔧 平台信息:`,
    `  - 平台: ${diagnostics.platform}`,
    `  - 原生应用: ${diagnostics.isNative ? '是' : '否'}`,
    `  - 设备型号: ${diagnostics.deviceInfo?.model || '未知'}`,
    `  - 系统版本: ${diagnostics.deviceInfo?.osVersion || '未知'}`,
    '',
    `🌐 网络状态:`,
    `  - 连接状态: ${diagnostics.networkStatus?.connected ? '已连接' : '未连接'}`,
    `  - 连接类型: ${diagnostics.networkStatus?.connectionType || '未知'}`,
    '',
    `⚙️ API配置:`,
    `  - 服务器地址: ${diagnostics.apiConfig.baseURL}`,
    `  - 协议: ${diagnostics.apiConfig.protocol}`,
    `  - 超时时间: ${diagnostics.apiConfig.timeout}ms`,
    `  - 重试次数: ${diagnostics.apiConfig.retries}`,
    '',
    `🔍 连接测试:`,
    `  - 服务器可达: ${diagnostics.connectivityTest.canReachServer ? '✅' : '❌'}`,
    `  - 响应时间: ${diagnostics.connectivityTest.responseTime || 'N/A'}ms`,
    `  - 错误信息: ${diagnostics.connectivityTest.error || '无'}`,
    '',
    `📡 HTTP测试:`,
    `  - HTTP请求: ${diagnostics.httpTest.canMakeHttpRequest ? '✅' : '❌'}`,
    `  - 响应时间: ${diagnostics.httpTest.responseTime || 'N/A'}ms`,
    `  - 错误信息: ${diagnostics.httpTest.error || '无'}`,
    '',
    `🔐 认证测试:`,
    `  - 已认证: ${diagnostics.authTest.isAuthenticated ? '✅' : '❌'}`,
    `  - Token存在: ${diagnostics.authTest.tokenExists ? '✅' : '❌'}`,
    `  - 错误信息: ${diagnostics.authTest.error || '无'}`,
    '',
    '========================================'
  ];
  
  return lines.join('\n');
}

/**
 * 快速网络检查
 */
export async function quickNetworkCheck(): Promise<boolean> {
  try {
    const config = getApiConfig();
    const response = await fetch(`${config.baseURL}/api/v1/ping`, {
      method: 'GET',
      timeout: 3000
    } as any);
    
    return response.ok;
  } catch (error) {
    console.warn('快速网络检查失败:', error);
    return false;
  }
}
