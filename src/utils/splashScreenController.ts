/**
 * SplashScreen控制器
 * 优化iOS启动体验，避免自动超时
 */

import { SplashScreen } from '@capacitor/splash-screen';
import { Capacitor } from '@capacitor/core';

class SplashScreenController {
  private static isHidden = false;
  private static hidePromise: Promise<void> | null = null;

  /**
   * 应用准备就绪时隐藏启动画面
   */
  public static async hideWhenReady(): Promise<void> {
    // 避免重复隐藏
    if (SplashScreenController.isHidden) {
      return;
    }

    // 如果已经在隐藏过程中，等待完成
    if (SplashScreenController.hidePromise) {
      return SplashScreenController.hidePromise;
    }

    // 只在原生平台执行
    if (!Capacitor.isNativePlatform()) {
      SplashScreenController.isHidden = true;
      return;
    }

    SplashScreenController.hidePromise = SplashScreenController.performHide();
    return SplashScreenController.hidePromise;
  }

  /**
   * 执行隐藏操作
   */
  private static async performHide(): Promise<void> {
    try {
      console.log('[SplashScreenController] 开始隐藏启动画面');
      
      await SplashScreen.hide({
        fadeOutDuration: 300 // 300ms淡出动画
      });
      
      SplashScreenController.isHidden = true;
      console.log('[SplashScreenController] ✅ 启动画面隐藏成功');
      
    } catch (error) {
      console.warn('[SplashScreenController] ⚠️ 隐藏启动画面失败:', error);
      SplashScreenController.isHidden = true; // 即使失败也标记为已隐藏
    } finally {
      SplashScreenController.hidePromise = null;
    }
  }

  /**
   * 检查是否已隐藏
   */
  public static isAlreadyHidden(): boolean {
    return SplashScreenController.isHidden;
  }

  /**
   * 重置状态（用于测试）
   */
  public static reset(): void {
    SplashScreenController.isHidden = false;
    SplashScreenController.hidePromise = null;
  }
}

export default SplashScreenController;
