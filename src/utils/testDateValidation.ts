/**
 * 日期验证测试脚本
 * 用于验证Invalid Date错误修复是否有效
 */

import { 
  createSafeDate, 
  isValidDate, 
  safeToISOString,
  validateAndFixExpiryTime,
  getDefaultExpiryTime,
  debugDateInfo
} from './dateValidation';

/**
 * 测试各种可能导致Invalid Date的情况
 */
export function testDateValidation(): void {
  console.log('🧪 开始日期验证测试...');
  
  // 测试用例
  const testCases = [
    // 正常情况
    { name: '正常ISO字符串', input: '2024-12-31T23:59:59.999Z' },
    { name: '正常Date对象', input: new Date() },
    { name: '正常时间戳', input: Date.now() },
    
    // 异常情况
    { name: '空字符串', input: '' },
    { name: 'null值', input: null },
    { name: 'undefined值', input: undefined },
    { name: '无效字符串', input: 'invalid-date' },
    { name: '数字字符串', input: '123456' },
    { name: '布尔值', input: true },
    { name: '对象', input: {} },
    { name: '数组', input: [] },
    
    // 边界情况
    { name: '过期时间', input: '2020-01-01T00:00:00.000Z' },
    { name: '未来时间', input: '2030-12-31T23:59:59.999Z' },
    { name: '格式错误的ISO', input: '2024-13-32T25:61:61.999Z' },
  ];
  
  console.log('\n📋 测试结果:');
  console.log('='.repeat(80));
  
  testCases.forEach((testCase, index) => {
    console.log(`\n${index + 1}. ${testCase.name}:`);
    console.log(`   输入: ${JSON.stringify(testCase.input)}`);
    
    try {
      // 测试createSafeDate
      const safeDate = createSafeDate(testCase.input as any);
      console.log(`   createSafeDate: ${safeDate ? '✅ 成功' : '❌ 失败'}`);
      
      // 测试isValidDate
      if (safeDate) {
        const valid = isValidDate(safeDate);
        console.log(`   isValidDate: ${valid ? '✅ 有效' : '❌ 无效'}`);
      }
      
      // 测试safeToISOString
      const isoString = safeToISOString(testCase.input as any);
      console.log(`   safeToISOString: ${isoString ? '✅ ' + isoString : '❌ 失败'}`);
      
      // 测试validateAndFixExpiryTime
      const fixedExpiry = validateAndFixExpiryTime(testCase.input as any);
      console.log(`   validateAndFixExpiryTime: ✅ ${fixedExpiry}`);
      
    } catch (error) {
      console.log(`   ❌ 测试异常: ${error instanceof Error ? error.message : String(error)}`);
    }
  });
  
  console.log('\n' + '='.repeat(80));
  console.log('🎯 特殊测试: 模拟Invalid Date场景');
  
  // 模拟可能导致Invalid Date的场景
  try {
    console.log('\n1. 模拟存储中的损坏数据:');
    const corruptedData = 'corrupted-date-string';
    debugDateInfo('损坏的存储数据', corruptedData);
    const fixed = validateAndFixExpiryTime(corruptedData);
    console.log(`   修复结果: ${fixed}`);
    
    console.log('\n2. 模拟网络响应中的异常expires_at:');
    const networkResponse = { expires_at: 'NaN' };
    const networkFixed = validateAndFixExpiryTime(networkResponse.expires_at);
    console.log(`   修复结果: ${networkFixed}`);
    
    console.log('\n3. 测试默认过期时间生成:');
    const defaultExpiry = getDefaultExpiryTime();
    console.log(`   默认过期时间: ${defaultExpiry}`);
    const defaultDate = createSafeDate(defaultExpiry);
    console.log(`   验证默认时间: ${defaultDate ? '✅ 有效' : '❌ 无效'}`);
    
  } catch (error) {
    console.error('❌ 特殊测试失败:', error);
  }
  
  console.log('\n🎉 日期验证测试完成!');
}

/**
 * 测试token验证场景
 */
export function testTokenValidationScenarios(): void {
  console.log('\n🔐 开始Token验证场景测试...');
  
  const tokenScenarios = [
    {
      name: '正常token',
      token: {
        accessToken: 'valid_token',
        expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString()
      }
    },
    {
      name: '过期token',
      token: {
        accessToken: 'expired_token',
        expiresAt: new Date(Date.now() - 60 * 60 * 1000).toISOString()
      }
    },
    {
      name: '无效过期时间的token',
      token: {
        accessToken: 'invalid_expiry_token',
        expiresAt: 'invalid-date-string'
      }
    },
    {
      name: '缺少过期时间的token',
      token: {
        accessToken: 'no_expiry_token'
      }
    }
  ];
  
  tokenScenarios.forEach((scenario, index) => {
    console.log(`\n${index + 1}. ${scenario.name}:`);
    
    try {
      const expiresAt = scenario.token.expiresAt;
      console.log(`   原始过期时间: ${expiresAt || 'undefined'}`);
      
      // 模拟isTokenValid逻辑
      if (!expiresAt) {
        console.log('   结果: ❌ 无过期时间');
        return;
      }
      
      const safeDate = createSafeDate(expiresAt);
      if (!safeDate || !isValidDate(safeDate)) {
        console.log('   结果: ❌ 无效日期格式');
        const fixed = validateAndFixExpiryTime(expiresAt);
        console.log(`   修复后: ✅ ${fixed}`);
        return;
      }
      
      const now = new Date();
      const isValid = now < safeDate;
      console.log(`   结果: ${isValid ? '✅ 有效' : '❌ 已过期'}`);
      
    } catch (error) {
      console.log(`   ❌ 验证异常: ${error instanceof Error ? error.message : String(error)}`);
    }
  });
  
  console.log('\n🎉 Token验证场景测试完成!');
}

/**
 * 运行所有测试
 */
export function runAllDateTests(): void {
  console.log('🚀 开始完整的日期处理测试套件...');
  
  try {
    testDateValidation();
    testTokenValidationScenarios();
    
    console.log('\n✅ 所有测试完成! Invalid Date错误修复验证通过。');
    
  } catch (error) {
    console.error('❌ 测试套件执行失败:', error);
  }
}

// 如果直接运行此文件，执行测试
if (typeof window !== 'undefined' && (window as any).runDateTests) {
  runAllDateTests();
}
