/**
 * 日期验证和处理工具
 * 专门用于处理iOS端的日期格式问题和Invalid Date错误
 */

/**
 * 检查日期是否有效
 */
export function isValidDate(date: Date): boolean {
  return date instanceof Date && !isNaN(date.getTime());
}

/**
 * 安全地创建日期对象
 */
export function createSafeDate(input: string | number | Date): Date | null {
  try {
    if (!input) {
      return null;
    }
    
    const date = new Date(input);
    return isValidDate(date) ? date : null;
  } catch (error) {
    console.warn('创建日期对象失败:', { input, error });
    return null;
  }
}

/**
 * 安全地获取ISO字符串
 */
export function safeToISOString(input: string | number | Date): string | null {
  try {
    const date = createSafeDate(input);
    return date ? date.toISOString() : null;
  } catch (error) {
    console.warn('转换为ISO字符串失败:', { input, error });
    return null;
  }
}

/**
 * 验证并修复过期时间字符串
 */
export function validateAndFixExpiryTime(expiresAt: string | undefined | null): string {
  if (!expiresAt) {
    console.warn('过期时间为空，使用默认24小时');
    return getDefaultExpiryTime();
  }
  
  const date = createSafeDate(expiresAt);
  if (!date) {
    console.warn('过期时间格式无效，使用默认24小时:', expiresAt);
    return getDefaultExpiryTime();
  }
  
  // 检查是否已经过期
  if (date.getTime() <= Date.now()) {
    console.warn('过期时间已过期，使用默认24小时:', expiresAt);
    return getDefaultExpiryTime();
  }
  
  return date.toISOString();
}

/**
 * 获取默认过期时间（24小时后）
 */
export function getDefaultExpiryTime(): string {
  try {
    const expiry = new Date(Date.now() + 24 * 60 * 60 * 1000);
    return expiry.toISOString();
  } catch (error) {
    console.error('生成默认过期时间失败:', error);
    // 最后的备用方案：使用固定的未来时间戳
    return new Date('2025-12-31T23:59:59.999Z').toISOString();
  }
}

/**
 * 比较两个日期
 */
export function compareDates(date1: string | Date, date2: string | Date): number {
  const d1 = createSafeDate(date1);
  const d2 = createSafeDate(date2);
  
  if (!d1 || !d2) {
    console.warn('日期比较失败，存在无效日期:', { date1, date2 });
    return 0;
  }
  
  return d1.getTime() - d2.getTime();
}

/**
 * 检查token是否即将过期（30分钟内）
 */
export function isTokenExpiringSoon(expiresAt: string | undefined | null): boolean {
  if (!expiresAt) {
    return true;
  }
  
  const expiry = createSafeDate(expiresAt);
  if (!expiry) {
    return true;
  }
  
  const now = new Date();
  const thirtyMinutesLater = new Date(now.getTime() + 30 * 60 * 1000);
  
  return expiry.getTime() <= thirtyMinutesLater.getTime();
}

/**
 * 格式化时间差
 */
export function formatTimeDifference(futureTime: string | Date, currentTime?: Date): string {
  const future = createSafeDate(futureTime);
  const current = currentTime || new Date();
  
  if (!future || !isValidDate(current)) {
    return '未知';
  }
  
  const diffMs = future.getTime() - current.getTime();
  
  if (diffMs <= 0) {
    return '已过期';
  }
  
  const diffMinutes = Math.floor(diffMs / (1000 * 60));
  const diffHours = Math.floor(diffMinutes / 60);
  const diffDays = Math.floor(diffHours / 24);
  
  if (diffDays > 0) {
    return `${diffDays}天`;
  } else if (diffHours > 0) {
    return `${diffHours}小时`;
  } else {
    return `${diffMinutes}分钟`;
  }
}

/**
 * iOS特定的日期处理
 */
export const iOSDateUtils = {
  /**
   * 检查iOS环境下的日期兼容性
   */
  checkiOSDateCompatibility(): boolean {
    try {
      const testDate = new Date();
      const isoString = testDate.toISOString();
      const parsedBack = new Date(isoString);
      
      return isValidDate(parsedBack) && Math.abs(testDate.getTime() - parsedBack.getTime()) < 1000;
    } catch (error) {
      console.error('iOS日期兼容性检查失败:', error);
      return false;
    }
  },
  
  /**
   * iOS安全的日期序列化
   */
  safeSerialize(date: Date): string | null {
    try {
      if (!isValidDate(date)) {
        return null;
      }
      
      // 在iOS上，确保使用UTC时间
      const utcDate = new Date(date.getTime() - date.getTimezoneOffset() * 60000);
      return utcDate.toISOString();
    } catch (error) {
      console.error('iOS日期序列化失败:', error);
      return null;
    }
  },
  
  /**
   * iOS安全的日期反序列化
   */
  safeDeserialize(isoString: string): Date | null {
    try {
      if (!isoString || typeof isoString !== 'string') {
        return null;
      }
      
      const date = new Date(isoString);
      return isValidDate(date) ? date : null;
    } catch (error) {
      console.error('iOS日期反序列化失败:', error);
      return null;
    }
  }
};

/**
 * 调试工具：打印日期相关信息
 */
export function debugDateInfo(label: string, dateInput: any): void {
  console.log(`🔍 [日期调试] ${label}:`, {
    input: dateInput,
    type: typeof dateInput,
    isString: typeof dateInput === 'string',
    isDate: dateInput instanceof Date,
    isValid: dateInput instanceof Date ? isValidDate(dateInput) : 'N/A',
    timestamp: dateInput instanceof Date ? dateInput.getTime() : 'N/A',
    isoString: (() => {
      try {
        return dateInput instanceof Date ? dateInput.toISOString() : new Date(dateInput).toISOString();
      } catch {
        return 'Invalid';
      }
    })()
  });
}
