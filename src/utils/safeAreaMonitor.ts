/**
 * Safe Area初始化监控工具
 * 用于调试和分析iOS Capacitor应用中的Safe Area初始化问题
 */

import { Capacitor } from '@capacitor/core';

interface SafeAreaMonitorConfig {
  maxMonitorTime: number; // 最大监控时间（毫秒）
  checkInterval: number;  // 检查间隔（毫秒）
  enableConsoleLog: boolean; // 是否启用控制台日志
  enableVisualIndicator: boolean; // 是否启用视觉指示器
}

interface SafeAreaCheckResult {
  timestamp: number;
  checkCount: number;
  methods: {
    envTop: string;
    envBottom: string;
    envLeft: string;
    envRight: string;
    testElementTop: string;
    testElementBottom: string;
    cssVarTop: string;
    cssVarBottom: string;
  };
  bottomNavigation?: {
    height: string;
    paddingBottom: string;
    computedHeight: string;
  };
}

/**
 * Safe Area初始化监控器
 */
export class SafeAreaMonitor {
  private config: SafeAreaMonitorConfig;
  private startTime: number = 0;
  private checkCount: number = 0;
  private monitorInterval?: NodeJS.Timeout;
  private results: SafeAreaCheckResult[] = [];
  private visualIndicator?: HTMLElement;

  constructor(config: Partial<SafeAreaMonitorConfig> = {}) {
    this.config = {
      maxMonitorTime: 3000,
      checkInterval: 100,
      enableConsoleLog: true,
      enableVisualIndicator: process.env.NODE_ENV === 'development',
      ...config
    };
  }

  /**
   * 开始监控Safe Area初始化
   */
  public start(): Promise<SafeAreaCheckResult[]> {
    return new Promise((resolve) => {
      if (!Capacitor.isNativePlatform() || Capacitor.getPlatform() !== 'ios') {
        console.log('🔍 非iOS原生环境，跳过Safe Area监控');
        resolve([]);
        return;
      }

      this.startTime = Date.now();
      this.checkCount = 0;
      this.results = [];

      if (this.config.enableConsoleLog) {
        console.log('🔍 开始Safe Area初始化监控...', {
          maxMonitorTime: this.config.maxMonitorTime,
          checkInterval: this.config.checkInterval
        });
      }

      if (this.config.enableVisualIndicator) {
        this.createVisualIndicator();
      }

      this.monitorInterval = setInterval(() => {
        const result = this.performCheck();
        this.results.push(result);

        const elapsed = Date.now() - this.startTime;
        const hasValidSafeArea = this.hasValidSafeAreaValues(result);

        if (hasValidSafeArea || elapsed >= this.config.maxMonitorTime) {
          this.stop();
          
          if (this.config.enableConsoleLog) {
            if (hasValidSafeArea) {
              console.log(`✅ Safe Area初始化完成，用时${elapsed}ms，检查${this.checkCount}次`);
              console.log('📐 最终Safe Area值:', {
                top: result.methods.testElementTop,
                bottom: result.methods.testElementBottom
              });
            } else {
              console.warn(`⚠️ Safe Area初始化超时(${elapsed}ms)，检查${this.checkCount}次`);
            }
            
            this.logSummary();
          }

          resolve(this.results);
        }
      }, this.config.checkInterval);
    });
  }

  /**
   * 停止监控
   */
  public stop(): void {
    if (this.monitorInterval) {
      clearInterval(this.monitorInterval);
      this.monitorInterval = undefined;
    }

    if (this.visualIndicator) {
      this.visualIndicator.remove();
      this.visualIndicator = undefined;
    }
  }

  /**
   * 执行单次检查
   */
  private performCheck(): SafeAreaCheckResult {
    this.checkCount++;
    const timestamp = Date.now() - this.startTime;

    // 检查env()函数
    const computedStyle = getComputedStyle(document.documentElement);
    const envTop = computedStyle.getPropertyValue('env(safe-area-inset-top)') || '未获取';
    const envBottom = computedStyle.getPropertyValue('env(safe-area-inset-bottom)') || '未获取';
    const envLeft = computedStyle.getPropertyValue('env(safe-area-inset-left)') || '未获取';
    const envRight = computedStyle.getPropertyValue('env(safe-area-inset-right)') || '未获取';

    // 检查CSS变量
    const cssVarTop = document.documentElement.style.getPropertyValue('--safe-area-inset-top') || '未设置';
    const cssVarBottom = document.documentElement.style.getPropertyValue('--safe-area-inset-bottom') || '未设置';

    // 使用测试元素检查
    const testElementTop = this.getTestElementValue('top');
    const testElementBottom = this.getTestElementValue('bottom');

    // 检查底部导航状态
    let bottomNavigation;
    const bottomNav = document.querySelector('.bottom-navigation');
    if (bottomNav) {
      const style = getComputedStyle(bottomNav);
      bottomNavigation = {
        height: style.height,
        paddingBottom: style.paddingBottom,
        computedHeight: `${bottomNav.getBoundingClientRect().height}px`
      };
    }

    const result: SafeAreaCheckResult = {
      timestamp,
      checkCount: this.checkCount,
      methods: {
        envTop,
        envBottom,
        envLeft,
        envRight,
        testElementTop,
        testElementBottom,
        cssVarTop,
        cssVarBottom
      },
      bottomNavigation
    };

    if (this.config.enableConsoleLog) {
      console.log(`📊 Safe Area检查 #${this.checkCount} (${timestamp}ms):`, result);
    }

    if (this.visualIndicator) {
      this.updateVisualIndicator(result);
    }

    return result;
  }

  /**
   * 使用测试元素获取Safe Area值
   */
  private getTestElementValue(position: 'top' | 'bottom' | 'left' | 'right'): string {
    const testEl = document.createElement('div');
    testEl.style.cssText = `position:fixed;${position}:env(safe-area-inset-${position});visibility:hidden;pointer-events:none;`;
    
    document.body.appendChild(testEl);
    const value = getComputedStyle(testEl)[position];
    document.body.removeChild(testEl);
    
    return value || '未获取';
  }

  /**
   * 检查是否有有效的Safe Area值
   */
  private hasValidSafeAreaValues(result: SafeAreaCheckResult): boolean {
    const { testElementTop, testElementBottom } = result.methods;
    
    const isValidValue = (value: string): boolean => {
      return value !== '未获取' && value !== '0px' && value !== 'auto' && value !== '';
    };

    return isValidValue(testElementTop) || isValidValue(testElementBottom);
  }

  /**
   * 创建视觉指示器
   */
  private createVisualIndicator(): void {
    this.visualIndicator = document.createElement('div');
    this.visualIndicator.style.cssText = `
      position: fixed;
      top: 10px;
      right: 10px;
      background: rgba(0, 0, 0, 0.8);
      color: white;
      padding: 8px 12px;
      border-radius: 6px;
      font-family: monospace;
      font-size: 12px;
      z-index: 99999;
      pointer-events: none;
      min-width: 200px;
    `;
    this.visualIndicator.textContent = 'Safe Area监控中...';
    document.body.appendChild(this.visualIndicator);
  }

  /**
   * 更新视觉指示器
   */
  private updateVisualIndicator(result: SafeAreaCheckResult): void {
    if (!this.visualIndicator) return;

    const { testElementTop, testElementBottom } = result.methods;
    const hasValid = this.hasValidSafeAreaValues(result);

    this.visualIndicator.innerHTML = `
      <div>Safe Area监控 #${result.checkCount}</div>
      <div>时间: ${result.timestamp}ms</div>
      <div>Top: ${testElementTop}</div>
      <div>Bottom: ${testElementBottom}</div>
      <div>状态: ${hasValid ? '✅ 已就绪' : '⏳ 等待中'}</div>
    `;

    this.visualIndicator.style.background = hasValid 
      ? 'rgba(0, 128, 0, 0.8)' 
      : 'rgba(255, 165, 0, 0.8)';
  }

  /**
   * 输出监控总结
   */
  private logSummary(): void {
    console.log('📋 === Safe Area监控总结 ===');
    console.log(`总检查次数: ${this.checkCount}`);
    console.log(`总耗时: ${Date.now() - this.startTime}ms`);
    
    const firstValidResult = this.results.find(r => this.hasValidSafeAreaValues(r));
    if (firstValidResult) {
      console.log(`首次检测到有效值: ${firstValidResult.timestamp}ms`);
      console.log('有效Safe Area值:', {
        top: firstValidResult.methods.testElementTop,
        bottom: firstValidResult.methods.testElementBottom
      });
    }

    // 分析底部导航高度变化
    const bottomNavResults = this.results
      .filter(r => r.bottomNavigation)
      .map(r => ({
        timestamp: r.timestamp,
        height: r.bottomNavigation!.height,
        computedHeight: r.bottomNavigation!.computedHeight
      }));

    if (bottomNavResults.length > 0) {
      console.log('📐 底部导航高度变化:', bottomNavResults);
    }
  }

  /**
   * 获取监控结果
   */
  public getResults(): SafeAreaCheckResult[] {
    return [...this.results];
  }
}

/**
 * 便捷函数：启动Safe Area监控
 */
export const startSafeAreaMonitoring = (config?: Partial<SafeAreaMonitorConfig>): Promise<SafeAreaCheckResult[]> => {
  const monitor = new SafeAreaMonitor(config);
  return monitor.start();
};

/**
 * 便捷函数：在应用启动时自动监控
 */
export const autoMonitorSafeArea = (): void => {
  if (process.env.NODE_ENV === 'development') {
    // 延迟启动，确保DOM已准备好
    setTimeout(() => {
      startSafeAreaMonitoring({
        enableVisualIndicator: true,
        enableConsoleLog: true
      });
    }, 100);
  }
};
