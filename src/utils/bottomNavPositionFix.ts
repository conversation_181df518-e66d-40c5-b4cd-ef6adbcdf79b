/**
 * 底部导航栏位置修复工具
 * 专门解决iOS WebView中Safe Area初始化延迟导致的位置异常问题
 */

import { Capacitor } from '@capacitor/core';

/**
 * 强制修复底部导航栏位置
 */
export const forceFixBottomNavPosition = (): void => {
  const isIOSDevice = Capacitor.getPlatform() === 'ios';
  const isNative = Capacitor.isNativePlatform();
  
  if (!isIOSDevice || !isNative) {
    return;
  }

  console.log('🔧 === 强制修复底部导航栏位置 ===');

  const element = document.querySelector('.bottom-navigation') as HTMLElement;
  
  if (!element) {
    console.warn('⚠️ 底部导航元素未找到，无法修复位置');
    return;
  }

  // 记录修复前的状态
  const beforeRect = element.getBoundingClientRect();
  const beforeStyle = getComputedStyle(element);
  
  console.log('📍 修复前状态:', {
    top: beforeRect.top,
    bottom: beforeRect.bottom,
    height: beforeRect.height,
    screenHeight: window.innerHeight,
    computedTop: beforeStyle.top,
    computedBottom: beforeStyle.bottom
  });

  // 🔧 强制清除所有定位相关属性
  const propertiesToClear = [
    'top', 'transform', 'translate', 'margin-top', 'margin-bottom',
    'padding-top', 'padding-bottom', 'border-top-width', 'border-bottom-width'
  ];
  
  propertiesToClear.forEach(prop => {
    element.style.removeProperty(prop);
  });

  // 🔧 设置正确的定位样式
  const fixStyles = {
    'position': 'fixed',
    'bottom': '0',
    'top': 'auto',
    'left': '0',
    'right': '0',
    'display': 'block',
    'visibility': 'visible',
    'opacity': '1',
    'z-index': '99999',
    'width': '100vw',
    'height': 'calc(60px + env(safe-area-inset-bottom, 34px))',
    'margin': '0',
    'padding': '0',
    'transform': 'none'
  };

  // 应用样式
  Object.entries(fixStyles).forEach(([property, value]) => {
    element.style.setProperty(property, value, 'important');
  });

  // 🔧 强制重新布局
  element.style.setProperty('display', 'none');
  element.offsetHeight; // 强制重排
  element.style.setProperty('display', 'block', 'important');

  // 验证修复效果
  setTimeout(() => {
    const afterRect = element.getBoundingClientRect();
    const afterStyle = getComputedStyle(element);
    const screenHeight = window.innerHeight;
    
    console.log('📍 修复后状态:', {
      top: afterRect.top,
      bottom: afterRect.bottom,
      height: afterRect.height,
      screenHeight,
      computedTop: afterStyle.top,
      computedBottom: afterStyle.bottom,
      isAtBottom: Math.abs(afterRect.bottom - screenHeight) < 5,
      positionCorrect: Math.abs(afterRect.top - (screenHeight - afterRect.height)) < 5
    });

    // 如果仍然不正确，再次尝试修复
    if (Math.abs(afterRect.bottom - screenHeight) > 5) {
      console.warn('⚠️ 位置仍然不正确，尝试再次修复...');
      
      // 更激进的修复方法
      element.style.cssText = `
        position: fixed !important;
        bottom: 0px !important;
        top: auto !important;
        left: 0px !important;
        right: 0px !important;
        width: 100vw !important;
        height: calc(60px + env(safe-area-inset-bottom, 34px)) !important;
        display: block !important;
        visibility: visible !important;
        opacity: 1 !important;
        z-index: 99999 !important;
        margin: 0px !important;
        padding: 0px !important;
        transform: none !important;
      `;
      
      // 再次验证
      setTimeout(() => {
        const finalRect = element.getBoundingClientRect();
        console.log('🎯 最终修复结果:', {
          top: finalRect.top,
          bottom: finalRect.bottom,
          isCorrect: Math.abs(finalRect.bottom - screenHeight) < 5
        });
      }, 100);
    } else {
      console.log('✅ 底部导航位置修复成功');
    }
  }, 100);
};

/**
 * 监控并自动修复底部导航位置
 */
export const startBottomNavPositionMonitor = (): void => {
  const isIOSDevice = Capacitor.getPlatform() === 'ios';
  const isNative = Capacitor.isNativePlatform();
  
  if (!isIOSDevice || !isNative) {
    return;
  }

  console.log('🔍 启动底部导航位置监控...');

  // 立即修复一次
  setTimeout(() => {
    forceFixBottomNavPosition();
  }, 100);

  // 定期检查和修复
  const monitorInterval = setInterval(() => {
    const element = document.querySelector('.bottom-navigation') as HTMLElement;
    
    if (element) {
      const rect = element.getBoundingClientRect();
      const screenHeight = window.innerHeight;
      
      // 如果位置不正确，自动修复
      if (Math.abs(rect.bottom - screenHeight) > 5) {
        console.log('🔧 检测到位置异常，自动修复...');
        forceFixBottomNavPosition();
      }
    }
  }, 1000); // 每秒检查一次

  // 5秒后停止监控（通常问题在前几秒内就会出现）
  setTimeout(() => {
    clearInterval(monitorInterval);
    console.log('🔍 底部导航位置监控结束');
  }, 5000);
};

/**
 * 在应用启动时自动启动位置修复
 */
export const autoFixBottomNavPosition = (): void => {
  if (process.env.NODE_ENV === 'development') {
    console.log('🔧 自动启动底部导航位置修复...');
  }
  
  // 等待DOM准备好
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
      setTimeout(startBottomNavPositionMonitor, 200);
    });
  } else {
    setTimeout(startBottomNavPositionMonitor, 200);
  }
};
