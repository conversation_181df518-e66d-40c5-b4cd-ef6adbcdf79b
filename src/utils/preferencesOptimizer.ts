/**
 * Preferences操作优化器
 * 减少频繁的原生调用，提升iOS性能
 */

import { Preferences } from '@capacitor/preferences';

interface PreferencesCache {
  [key: string]: {
    value: string | null;
    timestamp: number;
    ttl: number;
  };
}

class PreferencesOptimizer {
  private static cache: PreferencesCache = {};
  private static readonly DEFAULT_TTL = 30 * 1000; // 30秒缓存
  private static batchQueue: Array<{
    operation: 'get' | 'set' | 'remove';
    key: string;
    value?: string;
    ttl?: number;
    resolve: (value: any) => void;
    reject: (error: any) => void;
  }> = [];
  private static batchTimer: NodeJS.Timeout | null = null;
  private static readonly BATCH_DELAY = 50; // 50ms批量延迟

  /**
   * 优化的get操作
   */
  public static async get(key: string, ttl: number = PreferencesOptimizer.DEFAULT_TTL): Promise<string | null> {
    // 检查缓存
    const cached = PreferencesOptimizer.cache[key];
    if (cached && Date.now() - cached.timestamp < cached.ttl) {
      return cached.value;
    }

    // 添加到批量队列
    return new Promise((resolve, reject) => {
      PreferencesOptimizer.batchQueue.push({
        operation: 'get',
        key,
        ttl,
        resolve,
        reject
      });

      PreferencesOptimizer.scheduleBatch();
    });
  }

  /**
   * 优化的set操作
   */
  public static async set(key: string, value: string): Promise<void> {
    // 立即更新缓存
    PreferencesOptimizer.cache[key] = {
      value,
      timestamp: Date.now(),
      ttl: PreferencesOptimizer.DEFAULT_TTL
    };

    // 添加到批量队列
    return new Promise((resolve, reject) => {
      PreferencesOptimizer.batchQueue.push({
        operation: 'set',
        key,
        value,
        resolve,
        reject
      });

      PreferencesOptimizer.scheduleBatch();
    });
  }

  /**
   * 优化的remove操作
   */
  public static async remove(key: string): Promise<void> {
    // 立即从缓存中移除
    delete PreferencesOptimizer.cache[key];

    // 添加到批量队列
    return new Promise((resolve, reject) => {
      PreferencesOptimizer.batchQueue.push({
        operation: 'remove',
        key,
        resolve,
        reject
      });

      PreferencesOptimizer.scheduleBatch();
    });
  }

  /**
   * 批量获取多个键值
   */
  public static async getMultiple(keys: string[]): Promise<{ [key: string]: string | null }> {
    const results: { [key: string]: string | null } = {};
    const uncachedKeys: string[] = [];

    // 检查缓存
    for (const key of keys) {
      const cached = PreferencesOptimizer.cache[key];
      if (cached && Date.now() - cached.timestamp < cached.ttl) {
        results[key] = cached.value;
      } else {
        uncachedKeys.push(key);
      }
    }

    // 批量获取未缓存的键值
    if (uncachedKeys.length > 0) {
      const promises = uncachedKeys.map(key => 
        Preferences.get({ key }).then(result => ({ key, value: result.value }))
      );

      try {
        const responses = await Promise.all(promises);
        for (const { key, value } of responses) {
          results[key] = value;
          // 更新缓存
          PreferencesOptimizer.cache[key] = {
            value,
            timestamp: Date.now(),
            ttl: PreferencesOptimizer.DEFAULT_TTL
          };
        }
      } catch (error) {
        console.error('[PreferencesOptimizer] 批量获取失败:', error);
        throw error;
      }
    }

    return results;
  }

  /**
   * 调度批量操作
   */
  private static scheduleBatch(): void {
    if (PreferencesOptimizer.batchTimer) {
      return;
    }

    PreferencesOptimizer.batchTimer = setTimeout(() => {
      PreferencesOptimizer.processBatch();
    }, PreferencesOptimizer.BATCH_DELAY);
  }

  /**
   * 处理批量操作
   */
  private static async processBatch(): Promise<void> {
    const queue = [...PreferencesOptimizer.batchQueue];
    PreferencesOptimizer.batchQueue = [];
    PreferencesOptimizer.batchTimer = null;

    if (queue.length === 0) {
      return;
    }

    // 按操作类型分组
    const getOps = queue.filter(op => op.operation === 'get');
    const setOps = queue.filter(op => op.operation === 'set');
    const removeOps = queue.filter(op => op.operation === 'remove');

    try {
      // 处理get操作
      if (getOps.length > 0) {
        const getPromises = getOps.map(async op => {
          try {
            const result = await Preferences.get({ key: op.key });
            // 更新缓存
            PreferencesOptimizer.cache[op.key] = {
              value: result.value,
              timestamp: Date.now(),
              ttl: op.ttl || PreferencesOptimizer.DEFAULT_TTL
            };
            op.resolve(result.value);
          } catch (error) {
            op.reject(error);
          }
        });
        await Promise.all(getPromises);
      }

      // 处理set操作
      if (setOps.length > 0) {
        const setPromises = setOps.map(async op => {
          try {
            await Preferences.set({ key: op.key, value: op.value! });
            op.resolve(undefined);
          } catch (error) {
            op.reject(error);
          }
        });
        await Promise.all(setPromises);
      }

      // 处理remove操作
      if (removeOps.length > 0) {
        const removePromises = removeOps.map(async op => {
          try {
            await Preferences.remove({ key: op.key });
            op.resolve(undefined);
          } catch (error) {
            op.reject(error);
          }
        });
        await Promise.all(removePromises);
      }

    } catch (error) {
      console.error('[PreferencesOptimizer] 批量操作失败:', error);
    }
  }

  /**
   * 清除缓存
   */
  public static clearCache(): void {
    PreferencesOptimizer.cache = {};
    console.log('[PreferencesOptimizer] 缓存已清除');
  }

  /**
   * 获取缓存统计
   */
  public static getCacheStats() {
    const keys = Object.keys(PreferencesOptimizer.cache);
    const validKeys = keys.filter(key => {
      const cached = PreferencesOptimizer.cache[key];
      return Date.now() - cached.timestamp < cached.ttl;
    });

    return {
      totalKeys: keys.length,
      validKeys: validKeys.length,
      expiredKeys: keys.length - validKeys.length,
      queueLength: PreferencesOptimizer.batchQueue.length
    };
  }
}

export default PreferencesOptimizer;
