/**
 * 运动数据映射工具
 * 用于将API返回的数据转换为UI组件所需的数据格式
 */

import {
  getBodyPartName,
  getBodyPartNames,
  getEquipmentNames,
  getDifficultyText,
  getMuscleNames,
  buildMuscleImageUrls,
  EXERCISE_TYPES,
  BODY_PART_CATEGORIES,
  EQUIPMENT_CATEGORIES
} from '../constants/exerciseCategories';

/**
 * 类型安全的转换工具函数
 */

// 安全地将值转换为字符串
function safeToString(value: any): string {
  if (value === null || value === undefined) {
    return '';
  }
  return String(value);
}

// 安全地将值转换为数字
function safeToNumber(value: any, defaultValue: number = 0): number {
  if (value === null || value === undefined) {
    return defaultValue;
  }
  const num = Number(value);
  return isNaN(num) ? defaultValue : num;
}

// 安全地将值转换为数组
function safeToArray<T>(value: any): T[] {
  if (Array.isArray(value)) {
    return value;
  }
  if (value === null || value === undefined) {
    return [];
  }
  return [value];
}

import { getApiConfig } from '../config/api.config';

// 构建图片URL
export const buildImageUrl = (imageName: string): string => {
  const config = getApiConfig();
  return `${config.imageBaseURL}/${imageName}`;
};

// API返回的运动数据接口
export interface ApiExerciseResponse {
  name: string;
  en_name: string;
  body_part_id: number[];
  equipment_id: number[];
  image_name: string;
  gif_url: string;
  description: string | null;
  level: number;
  sort_priority: number;
  user_id: string | null;
  exercise_type: string;
  hit_time: number;
  id: number;
  created_at: string;
  updated_at: string;
}

// UI组件使用的运动数据接口（来自ExercisesPage.tsx）
export interface Exercise {
  id: string;
  name: string;
  englishName: string;
  category: string;
  muscleGroups: {
    primary: string[];
    secondary: string[];
  };
  equipment: string[];
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  instructions: string[];
  tips: string[];
  images: string[];
  videos: string[];
  alternatives: string[];
  is_favorite: boolean;
  popularity: number;
  // 新增字段
  gif_url?: string; // GIF动图URL
  primary_muscles?: string[]; // 主要肌肉群
  target?: string; // 目标肌肉（降级字段）
}

// API动作详情响应接口
export interface ApiExerciseDetailResponse {
  id: number;
  name: string;
  en_name: string;
  body_part_id: number[];
  equipment_id: number[];
  image_name: string;
  gif_url: string;
  description: string | null;
  level: number;
  sort_priority: number;
  user_id: string | null;
  exercise_type: string;
  hit_time: number;
  created_at: string;
  updated_at: string;
  // 详情特有字段
  target_muscles_id?: (string | number)[];
  synergist_muscles_id?: (string | number)[];
  ex_instructions?: string;
  exercise_tips?: string;
  video_file?: string;
}

// UI动作详情接口
export interface ExerciseDetail extends Exercise {
  targetMuscles: string[];
  synergistMuscles: string[];
  instructions: string[];
  tips: string[];
  videoUrl?: string;
  muscleImages: {
    frontImages: string[];
    backImages: string[];
  };
}

// API搜索参数接口（扩展版）
export interface ExerciseSearchParams {
  body_part_id?: number[];
  equipment_id?: number[];
  muscle_id?: number[];
  level?: string;
  skip?: number;
  limit?: number;
  search?: string;
  keyword?: string;
}

// 动作创建数据接口
export interface ExerciseCreateData {
  name: string;
  body_part_id: number;
  equipment_id: number;
  level: number;
  image_name?: string;
  gif_url?: string;
  target_muscles_id?: (string | number)[];
  synergist_muscles_id?: (string | number)[];
  ex_instructions?: string;
  exercise_tips?: string;
  video_file?: string;
}

// 媒体上传响应接口
export interface MediaUploadResponse {
  success: boolean;
  fileName?: string;
  error?: string;
}

/**
 * 将API返回的运动数据转换为UI组件数据格式
 */
export function mapApiExerciseToUIExercise(apiExercise: ApiExerciseResponse): Exercise {
  // 安全地处理body_part_id数组
  const bodyPartIds = safeToArray<number>(apiExercise.body_part_id);
  const equipmentIds = safeToArray<number>(apiExercise.equipment_id);

  const allMuscleGroups = getBodyPartNames(bodyPartIds);

  // 构建图片URL
  const imageUrl = apiExercise.image_name
    ? buildImageUrl(safeToString(apiExercise.image_name))
    : '';

  return {
    id: safeToString(apiExercise.id),
    name: safeToString(apiExercise.name),
    englishName: safeToString(apiExercise.en_name),
    category: getBodyPartName(bodyPartIds[0]) || '其他',
    muscleGroups: {
      primary: allMuscleGroups.slice(0, Math.ceil(allMuscleGroups.length / 2)), // 前半部分作为主要肌群
      secondary: allMuscleGroups.slice(Math.ceil(allMuscleGroups.length / 2)) // 后半部分作为次要肌群
    },
    equipment: getEquipmentNames(equipmentIds),
    difficulty: getDifficultyText(safeToNumber(apiExercise.level, 1)) as 'beginner' | 'intermediate' | 'advanced',
    instructions: apiExercise.description ? [safeToString(apiExercise.description)] : [],
    tips: [], // API中没有tips字段，使用空数组
    images: imageUrl ? [imageUrl] : [],
    videos: apiExercise.gif_url ? [safeToString(apiExercise.gif_url)] : [],
    alternatives: [], // API中没有alternatives字段，使用空数组
    is_favorite: false, // 用户偏好功能暂时忽略
    popularity: safeToNumber(apiExercise.hit_time) || safeToNumber(apiExercise.sort_priority) || 0,
    // 新增字段映射
    gif_url: safeToString(apiExercise.gif_url), // 直接映射GIF URL
    primary_muscles: allMuscleGroups.slice(0, Math.ceil(allMuscleGroups.length / 2)), // 主要肌肉群
    target: allMuscleGroups.length > 0 ? allMuscleGroups[0] : undefined // 目标肌肉（第一个）
  };
}

/**
 * 批量转换API运动数据
 */
export function mapApiExercisesToUIExercises(apiExercises: ApiExerciseResponse[]): Exercise[] {
  return apiExercises.map(mapApiExerciseToUIExercise);
}

/**
 * 将API返回的动作详情数据转换为UI组件数据格式
 */
export function mapApiExerciseDetailToUIDetail(apiDetail: ApiExerciseDetailResponse): ExerciseDetail {
  // 先转换基础动作信息
  const baseExercise = mapApiExerciseToUIExercise(apiDetail);

  // 安全地处理肌肉ID数组
  const targetMuscleIds = safeToArray<string | number>(apiDetail.target_muscles_id || []);
  const synergistMuscleIds = safeToArray<string | number>(apiDetail.synergist_muscles_id || []);

  // 获取肌肉信息
  const targetMuscles = getMuscleNames(targetMuscleIds);
  const synergistMuscles = getMuscleNames(synergistMuscleIds);

  // 生成肌肉图片URL
  const targetMuscleImages = buildMuscleImageUrls(targetMuscleIds, 'main');
  const synergistMuscleImages = buildMuscleImageUrls(synergistMuscleIds, 'minor');

  // 过滤重复的肌肉图片
  const allFrontImages = [...targetMuscleImages.frontImages, ...synergistMuscleImages.frontImages];
  const allBackImages = [...targetMuscleImages.backImages, ...synergistMuscleImages.backImages];
  const uniqueFrontImages = Array.from(new Set(allFrontImages));
  const uniqueBackImages = Array.from(new Set(allBackImages));

  // 构建视频URL
  const config = getApiConfig();
  const videoUrl = apiDetail.video_file
    ? `${config.baseURL}/api/v1/exercise/video/${safeToString(apiDetail.video_file)}`
    : undefined;

  return {
    ...baseExercise,
    targetMuscles,
    synergistMuscles,
    instructions: apiDetail.ex_instructions ? [safeToString(apiDetail.ex_instructions)] : [],
    tips: apiDetail.exercise_tips ? [safeToString(apiDetail.exercise_tips)] : [],
    videoUrl,
    muscleImages: {
      frontImages: uniqueFrontImages,
      backImages: uniqueBackImages
    }
  };
}

/**
 * 构建API查询参数
 */
export function buildExerciseQueryParams(params: ExerciseSearchParams): URLSearchParams {
  const queryParams = new URLSearchParams();
  
  if (params.body_part_id && params.body_part_id.length > 0) {
    params.body_part_id.forEach(id => {
      queryParams.append('body_part_id', id.toString());
    });
  }
  
  if (params.equipment_id && params.equipment_id.length > 0) {
    params.equipment_id.forEach(id => {
      queryParams.append('equipment_id', id.toString());
    });
  }
  
  if (params.skip !== undefined) {
    queryParams.append('skip', params.skip.toString());
  }
  
  if (params.limit !== undefined) {
    queryParams.append('limit', params.limit.toString());
  }
  
  return queryParams;
}

/**
 * 根据分类名称获取对应的body_part_id
 */
export function getCategoryBodyPartIds(category: string): number[] {
  return BODY_PART_CATEGORIES
    .filter(bodyPart => bodyPart.name === category)
    .map(bodyPart => bodyPart.id);
}

/**
 * 根据器械名称获取对应的equipment_id
 */
export function getEquipmentIds(equipmentNames: string[]): number[] {
  const ids: number[] = [];
  equipmentNames.forEach(name => {
    const equipment = EQUIPMENT_CATEGORIES.find(eq => eq.name === name);
    if (equipment) {
      ids.push(equipment.id);
    }
  });
  
  return ids;
}

/**
 * 获取运动类型的中文名称
 */
export function getExerciseTypeName(exerciseType: string): string {
  return EXERCISE_TYPES[exerciseType as keyof typeof EXERCISE_TYPES] || exerciseType;
}
