// Global styles for FitMaster
/* 导入统一设计系统变量 - 作为唯一CSS变量源 */
@use './variables.scss';

// 导入新的统一样式系统
@use './_workout-system';
@use './_mixins';
@use './_typography';

@import './design-system.css';
@import '@hackernoon/pixel-icon-library/fonts/iconfont.css';

// Reset and base styles
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  scroll-behavior: smooth;
  height: 100%;
}

body {
  font-family: var(--font-primary);
  font-size: var(--text-base);
  line-height: var(--leading-normal);
  color: var(--text-primary);
  background-color: var(--bg-primary);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  height: 100%;
  overflow-x: hidden;
}

#root {
  height: 100%;
  min-height: 100vh;
}

.app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

// Typography - 使用design-system变量
h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-display);
  font-weight: var(--font-semibold);
  line-height: var(--leading-tight);
  color: var(--text-primary);
}

h1 { font-size: var(--text-4xl); }
h2 { font-size: var(--text-3xl); }
h3 { font-size: var(--text-2xl); }
h4 { font-size: var(--text-xl); }
h5 { font-size: var(--text-lg); }
h6 { font-size: var(--text-base); }

p {
  line-height: var(--leading-relaxed);
  color: var(--text-secondary);
}

a {
  color: var(--accent-500);
  text-decoration: none;
  transition: color var(--transition-normal) var(--ease-in-out);
  
  &:hover {
    color: var(--accent-400);
  }
  
  &:focus {
    outline: 2px solid var(--accent-500);
    outline-offset: 2px;
    border-radius: var(--radius-sm);
  }
}

// Utility classes
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.flex { display: flex; }
.flex-col { flex-direction: column; }
.items-center { align-items: center; }
.justify-center { justify-content: center; }

// Scrollbar styles - 使用design-system变量
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--primary-600);
}

::-webkit-scrollbar-thumb {
  background: var(--primary-500);
  border-radius: var(--radius-full);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--accent-500);
}

// Selection styles
::selection {
  background-color: var(--selection-bg);
  color: var(--selection-color);
}

::-moz-selection {
  background-color: var(--selection-bg);
  color: var(--selection-color);
}

// Focus styles
:focus {
  outline: 2px solid var(--accent-500);
  outline-offset: var(--focus-ring-offset);
}

:focus:not(:focus-visible) {
  outline: none;
}

// Accessibility - 符合FitMaster开发规则
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
  
  html {
    scroll-behavior: auto;
  }
}

@media (prefers-contrast: high) {
  .card {
    border-width: 2px;
  }
  
  .btn {
    border-width: 2px;
  }
}

// Print styles
@media print {
  * {
    background: transparent !important;
    color: black !important;
    box-shadow: none !important;
    text-shadow: none !important;
  }
  
  body {
    font-size: 12pt;
    line-height: 1.4;
  }
  
  .btn {
    display: none;
  }
}

/* Pixel Icon Library使用示例 */
.icon-examples {
  display: none; // 默认不显示，仅作为文档参考
}

/* 
示例用法:
<i class="hn hn-dumbbell"></i>
<i class="hn hn-home"></i>
<i class="hn hn-heart"></i>
*/ 