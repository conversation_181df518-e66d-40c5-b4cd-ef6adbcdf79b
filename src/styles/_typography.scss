// FitMaster 字体系统
// iOS优化的统一字体规范

@use 'sass:map';
@use 'workout-system' as ws;
@use 'mixins' as mx;

// ===== 基础字体设置 =====

// 全局字体设置
html {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

// ===== 文字样式类 =====

// 标题类
.text-title {
  @include mx.responsive-font('xl', 'bold');
  line-height: 1.2;
  color: var(--text-primary);
  margin: 0;
}

.text-subtitle {
  @include mx.responsive-font('lg', 'semibold');
  line-height: 1.3;
  color: var(--text-primary);
  margin: 0;
}

// 正文类
.text-body {
  @include mx.responsive-font('md', 'normal');
  line-height: 1.4;
  color: var(--text-primary);
  margin: 0;
}

.text-body-sm {
  @include mx.responsive-font('sm', 'normal');
  line-height: 1.4;
  color: var(--text-secondary);
  margin: 0;
}

// 辅助文字类
.text-caption {
  @include mx.responsive-font('xs', 'normal');
  line-height: 1.3;
  color: var(--text-tertiary);
  margin: 0;
}

// ===== Exercise组件专用字体类 =====

// Exercise名称字体 - 确保可读性
.text-exercise-name {
  @include mx.responsive-font('md', 'medium'); // 14px -> 13px -> 12px -> 11px
  line-height: 1.2;
  color: var(--text-primary);
  margin: 0;
  
  // 文字截断处理
  @include mx.text-clamp(2);
  
  // 单行模式
  &.single-line {
    @include mx.text-truncate;
  }
}

// Exercise详情信息字体
.text-exercise-details {
  @include mx.responsive-font('sm', 'normal'); // 12px -> 11px -> 10px -> 9px
  line-height: 1.3;
  color: var(--text-secondary, #6b7280);
  margin: 0;
}

// Sets信息字体 - 确保可见性
.text-sets-info {
  @include mx.responsive-font('sm', 'normal'); // 12px -> 11px -> 10px -> 9px
  line-height: 1.2;
  color: var(--text-secondary, #6b7280);
  font-weight: 400;
  margin: 0;
  
  // 确保在所有背景下可见
  text-shadow: 0 0 1px rgba(255, 255, 255, 0.5);
  
  @include mx.dark-theme {
    color: var(--text-secondary-dark, #cbd5e1);
    text-shadow: 0 0 1px rgba(0, 0, 0, 0.5);
  }
}

// 设备标签字体
.text-equipment-tag {
  @include mx.responsive-font('xs', 'medium');
  line-height: 1.2;
  color: var(--text-secondary);
  margin: 0;
  white-space: nowrap;
}

// 身体部位标签字体
.text-body-part-tag {
  @include mx.responsive-font('xs', 'medium');
  line-height: 1.2;
  color: var(--color-primary, #007AFF);
  margin: 0;
  white-space: nowrap;
}

// 难度文字
.text-difficulty {
  @include mx.responsive-font('xs', 'medium');
  line-height: 1.2;
  color: var(--text-tertiary);
  margin: 0;
  display: flex;
  align-items: center;
  gap: map.get(ws.$workout-spacing, 'xs');
}

// ===== 响应式字体修饰符 =====

// 权重修饰符
.font-light { font-weight: map.get(ws.$workout-font-weights, 'light'); }
.font-normal { font-weight: map.get(ws.$workout-font-weights, 'normal'); }
.font-medium { font-weight: map.get(ws.$workout-font-weights, 'medium'); }
.font-semibold { font-weight: map.get(ws.$workout-font-weights, 'semibold'); }
.font-bold { font-weight: map.get(ws.$workout-font-weights, 'bold'); }

// 颜色修饰符
.text-primary { color: var(--text-primary); }
.text-secondary { color: var(--text-secondary); }
.text-tertiary { color: var(--text-tertiary); }
.text-accent { color: var(--color-primary); }
.text-success { color: var(--color-success); }
.text-warning { color: var(--color-warning); }
.text-error { color: var(--color-error); }

// 对齐修饰符
.text-left { text-align: left; }
.text-center { text-align: center; }
.text-right { text-align: right; }

// 截断修饰符
.text-truncate { @include mx.text-truncate; }
.text-clamp-1 { @include mx.text-clamp(1); }
.text-clamp-2 { @include mx.text-clamp(2); }
.text-clamp-3 { @include mx.text-clamp(3); }

// ===== 暗色主题适配 =====
@include mx.dark-theme {
  .text-title,
  .text-subtitle,
  .text-body {
    color: var(--text-primary-dark);
  }
  
  .text-body-sm {
    color: var(--text-secondary-dark);
  }
  
  .text-caption {
    color: var(--text-tertiary-dark);
  }
  
  .text-exercise-name {
    color: var(--text-primary-dark);
  }
  
  .text-exercise-details {
    color: var(--text-secondary-dark);
  }
}

// ===== 性能优化 =====
.text-exercise-name,
.text-exercise-details,
.text-sets-info {
  @include mx.css-contain('style paint');
}