// FitMaster Workout组件专用样式系统
// 使用现代Sass模块化语法
@use 'sass:map';

// ===== 响应式断点系统 (兼容现有系统) =====
$workout-breakpoints: (
  'small': 360px,      // 极小屏幕 (iPhone SE等)
  'mobile': 480px,     // iPhone标准尺寸
  'tablet': 768px,     // iPad竖屏
  'desktop': 1024px    // 桌面端
);

// 断点获取函数
@function workout-breakpoint($name) {
  @return map-get($workout-breakpoints, $name);
}

// ===== 字体系统 - iOS优化 =====
$workout-font-sizes: (
  'xs': (
    desktop: 10px,
    tablet: 9px,
    mobile: 8px,
    small: 8px
  ),
  'sm': (
    desktop: 12px,
    tablet: 11px,
    mobile: 10px,
    small: 9px
  ),
  'md': (
    desktop: 14px,
    tablet: 13px,
    mobile: 12px,
    small: 11px
  ),
  'lg': (
    desktop: 16px,
    tablet: 15px,
    mobile: 14px,
    small: 13px
  ),
  'xl': (
    desktop: 18px,
    tablet: 17px,
    mobile: 16px,
    small: 15px
  )
);

// 字体权重系统
$workout-font-weights: (
  'light': 300,
  'normal': 400,
  'medium': 500,
  'semibold': 600,
  'bold': 700
);

// ===== 间距系统 =====
$workout-spacing: (
  'xs': 2px,
  'sm': 4px,
  'md': 8px,
  'lg': 12px,
  'xl': 16px,
  'xxl': 24px
);

// 间距获取函数
@function workout-spacing($name) {
  @return map-get($workout-spacing, $name);
}

// ===== iOS专用尺寸 =====
$workout-ios-sizes: (
  'touch-target': 44px,        // Apple HIG最小触摸目标
  'icon-sm': 14px,             // 小图标 (difficulty-plate等)
  'icon-md': 18px,             // 中等图标
  'icon-lg': 24px,             // 大图标
  'border-radius-sm': 6px,     // 小圆角
  'border-radius-md': 12px,    // 中等圆角
  'border-radius-lg': 16px     // 大圆角
);

// iOS尺寸获取函数
@function workout-ios-size($name) {
  @return map-get($workout-ios-sizes, $name);
}