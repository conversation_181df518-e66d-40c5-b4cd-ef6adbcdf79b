/**
 * iOS增强样式
 * 提供iOS原生体验的样式定义
 */

// iOS系统颜色
:root {
  // iOS系统蓝色
  --ios-system-blue: #007AFF;
  --ios-system-blue-light: #5AC8FA;
  --ios-system-blue-dark: #0051D5;
  
  // iOS系统绿色
  --ios-system-green: #34C759;
  --ios-system-green-light: #30D158;
  --ios-system-green-dark: #248A3D;
  
  // iOS系统红色
  --ios-system-red: #FF3B30;
  --ios-system-red-light: #FF453A;
  --ios-system-red-dark: #D70015;
  
  // iOS系统橙色
  --ios-system-orange: #FF9500;
  --ios-system-orange-light: #FF9F0A;
  --ios-system-orange-dark: #C93400;
  
  // iOS系统黄色
  --ios-system-yellow: #FFCC00;
  --ios-system-yellow-light: #FFD60A;
  --ios-system-yellow-dark: #AD8B00;
  
  // iOS系统紫色
  --ios-system-purple: #AF52DE;
  --ios-system-purple-light: #BF5AF2;
  --ios-system-purple-dark: #8E44AD;
  
  // iOS系统粉色
  --ios-system-pink: #FF2D92;
  --ios-system-pink-light: #FF375F;
  --ios-system-pink-dark: #D70A53;
  
  // iOS灰色系统
  --ios-system-gray: #8E8E93;
  --ios-system-gray2: #AEAEB2;
  --ios-system-gray3: #C7C7CC;
  --ios-system-gray4: #D1D1D6;
  --ios-system-gray5: #E5E5EA;
  --ios-system-gray6: #F2F2F7;
  
  // iOS背景色
  --ios-background-primary: #FFFFFF;
  --ios-background-secondary: #F2F2F7;
  --ios-background-tertiary: #FFFFFF;
  
  // iOS分组背景色
  --ios-grouped-background-primary: #F2F2F7;
  --ios-grouped-background-secondary: #FFFFFF;
  --ios-grouped-background-tertiary: #F2F2F7;
  
  // iOS标签颜色
  --ios-label-primary: #000000;
  --ios-label-secondary: #3C3C43;
  --ios-label-tertiary: #3C3C43;
  --ios-label-quaternary: #3C3C43;
  
  // iOS分隔符颜色
  --ios-separator: #C6C6C8;
  --ios-separator-opaque: #C6C6C8;
  
  // iOS链接颜色
  --ios-link: #007AFF;
  
  // iOS阴影
  --ios-shadow-light: 0 1px 3px rgba(0, 0, 0, 0.1);
  --ios-shadow-medium: 0 4px 12px rgba(0, 0, 0, 0.15);
  --ios-shadow-heavy: 0 8px 24px rgba(0, 0, 0, 0.2);
  
  // iOS圆角
  --ios-radius-small: 8px;
  --ios-radius-medium: 12px;
  --ios-radius-large: 16px;
  --ios-radius-xlarge: 20px;
  
  // iOS间距
  --ios-spacing-xs: 4px;
  --ios-spacing-sm: 8px;
  --ios-spacing-md: 16px;
  --ios-spacing-lg: 24px;
  --ios-spacing-xl: 32px;
  
  // iOS字体大小
  --ios-font-caption2: 11px;
  --ios-font-caption1: 12px;
  --ios-font-footnote: 13px;
  --ios-font-subheadline: 15px;
  --ios-font-callout: 16px;
  --ios-font-body: 17px;
  --ios-font-headline: 17px;
  --ios-font-title3: 20px;
  --ios-font-title2: 22px;
  --ios-font-title1: 28px;
  --ios-font-large-title: 34px;
}

// 暗色模式
@media (prefers-color-scheme: dark) {
  :root {
    // iOS暗色背景
    --ios-background-primary: #000000;
    --ios-background-secondary: #1C1C1E;
    --ios-background-tertiary: #2C2C2E;
    
    // iOS暗色分组背景
    --ios-grouped-background-primary: #000000;
    --ios-grouped-background-secondary: #1C1C1E;
    --ios-grouped-background-tertiary: #2C2C2E;
    
    // iOS暗色标签
    --ios-label-primary: #FFFFFF;
    --ios-label-secondary: #EBEBF5;
    --ios-label-tertiary: #EBEBF5;
    --ios-label-quaternary: #EBEBF5;
    
    // iOS暗色分隔符
    --ios-separator: #38383A;
    --ios-separator-opaque: #38383A;
    
    // iOS暗色系统颜色调整
    --ios-system-blue: #0A84FF;
    --ios-system-green: #30D158;
    --ios-system-red: #FF453A;
    --ios-system-orange: #FF9F0A;
    --ios-system-yellow: #FFD60A;
    --ios-system-purple: #BF5AF2;
    --ios-system-pink: #FF375F;
  }
}

// iOS通用样式类
.ios-card {
  background-color: var(--ios-background-secondary);
  border-radius: var(--ios-radius-medium);
  box-shadow: var(--ios-shadow-light);
  border: 1px solid var(--ios-separator);
  overflow: hidden;
}

.ios-button {
  background-color: var(--ios-system-blue);
  color: white;
  border: none;
  border-radius: var(--ios-radius-medium);
  padding: var(--ios-spacing-sm) var(--ios-spacing-md);
  font-size: var(--ios-font-body);
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:hover {
    background-color: var(--ios-system-blue-dark);
    transform: translateY(-1px);
  }
  
  &:active {
    transform: translateY(0) scale(0.98);
  }
  
  &.secondary {
    background-color: var(--ios-system-gray6);
    color: var(--ios-system-blue);
    
    &:hover {
      background-color: var(--ios-system-gray5);
    }
  }
  
  &.destructive {
    background-color: var(--ios-system-red);
    
    &:hover {
      background-color: var(--ios-system-red-dark);
    }
  }
}

.ios-input {
  background-color: var(--ios-background-tertiary);
  border: 1px solid var(--ios-separator);
  border-radius: var(--ios-radius-medium);
  padding: var(--ios-spacing-sm) var(--ios-spacing-md);
  font-size: var(--ios-font-body);
  color: var(--ios-label-primary);
  transition: all 0.2s ease;
  
  &:focus {
    outline: none;
    border-color: var(--ios-system-blue);
    box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
  }
  
  &::placeholder {
    color: var(--ios-label-tertiary);
  }
}

.ios-list {
  background-color: var(--ios-grouped-background-secondary);
  border-radius: var(--ios-radius-medium);
  overflow: hidden;
  
  .ios-list-item {
    padding: var(--ios-spacing-md);
    border-bottom: 1px solid var(--ios-separator);
    background-color: var(--ios-background-secondary);
    transition: background-color 0.2s ease;
    
    &:last-child {
      border-bottom: none;
    }
    
    &:hover {
      background-color: var(--ios-system-gray6);
    }
    
    &:active {
      background-color: var(--ios-system-gray5);
    }
  }
}

.ios-navigation-bar {
  background-color: var(--ios-background-primary);
  border-bottom: 1px solid var(--ios-separator);
  padding: var(--ios-spacing-md);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  
  .ios-nav-title {
    font-size: var(--ios-font-headline);
    font-weight: 600;
    color: var(--ios-label-primary);
    text-align: center;
  }
}

.ios-tab-bar {
  background-color: var(--ios-background-primary);
  border-top: 1px solid var(--ios-separator);
  padding: var(--ios-spacing-sm) 0;
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  
  .ios-tab-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: var(--ios-spacing-xs);
    color: var(--ios-label-secondary);
    transition: color 0.2s ease;
    
    &.active {
      color: var(--ios-system-blue);
    }
    
    .ios-tab-icon {
      font-size: 24px;
      margin-bottom: 2px;
    }
    
    .ios-tab-label {
      font-size: var(--ios-font-caption1);
      font-weight: 500;
    }
  }
}

// iOS Safe Area支持
.ios-safe-area {
  padding-top: env(safe-area-inset-top);
  padding-bottom: env(safe-area-inset-bottom);
  padding-left: env(safe-area-inset-left);
  padding-right: env(safe-area-inset-right);
}

// iOS触摸反馈
.ios-touch-feedback {
  transition: transform 0.1s ease;
  
  &:active {
    transform: scale(0.98);
  }
}

// iOS毛玻璃效果
.ios-blur {
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  background-color: rgba(255, 255, 255, 0.8);
  
  @media (prefers-color-scheme: dark) {
    background-color: rgba(28, 28, 30, 0.8);
  }
}

// iOS加载动画
@keyframes ios-spinner {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.ios-spinner {
  animation: ios-spinner 1s linear infinite;
}

// iOS弹簧动画
.ios-spring-animation {
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}
