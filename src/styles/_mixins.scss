// FitMaster Workout组件专用Mixin系统
// 使用现代Sass模块化语法

@use 'sass:map';
@use 'workout-system' as ws;

// ===== 响应式断点Mixins =====

// 响应式断点mixin
@mixin respond-to($breakpoint) {
  @if map.has-key(ws.$workout-breakpoints, $breakpoint) {
    @media (max-width: map.get(ws.$workout-breakpoints, $breakpoint)) {
      @content;
    }
  } @else {
    @warn "Breakpoint `#{$breakpoint}` not found in $workout-breakpoints.";
  }
}

// 最小宽度断点mixin
@mixin respond-above($breakpoint) {
  @if map.has-key(ws.$workout-breakpoints, $breakpoint) {
    @media (min-width: map.get(ws.$workout-breakpoints, $breakpoint) + 1px) {
      @content;
    }
  } @else {
    @warn "Breakpoint `#{$breakpoint}` not found in $workout-breakpoints.";
  }
}

// ===== 响应式字体系统 =====

// 统一响应式字体mixin
@mixin responsive-font($size-key, $weight: 'normal') {
  @if map.has-key(ws.$workout-font-sizes, $size-key) {
    $sizes: map.get(ws.$workout-font-sizes, $size-key);
    
    // 使用 & {} 包装基础声明以符合新的 Sass 规则
    & {
      // 桌面端默认尺寸
      font-size: map.get($sizes, 'desktop');
      font-weight: map.get(ws.$workout-font-weights, $weight);
    }
    
    // 平板端
    @include respond-to('tablet') {
      font-size: map.get($sizes, 'tablet');
    }
    
    // 移动端
    @include respond-to('mobile') {
      font-size: map.get($sizes, 'mobile');
    }
    
    // 极小屏幕
    @include respond-to('small') {
      font-size: map.get($sizes, 'small');
    }
  } @else {
    @warn "Font size `#{$size-key}` not found in $workout-font-sizes.";
  }
}

// 单行文字截断
@mixin text-truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

// 多行文字截断
@mixin text-clamp($lines: 2) {
  display: -webkit-box;
  -webkit-line-clamp: $lines;
  line-clamp: $lines;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

// ===== 响应式间距系统 =====

// 响应式padding mixin
@mixin responsive-padding($size-key) {
  @if map.has-key(ws.$workout-spacing, $size-key) {
    $base-size: map.get(ws.$workout-spacing, $size-key);
    
    // 使用 & {} 包装基础声明以符合新的 Sass 规则
    & {
      padding: $base-size;
    }
    
    @include respond-to('mobile') {
      padding: $base-size * 0.8;
    }
    
    @include respond-to('small') {
      padding: $base-size * 0.6;
    }
  }
}

// 响应式margin mixin
@mixin responsive-margin($size-key) {
  @if map.has-key(ws.$workout-spacing, $size-key) {
    $base-size: map.get(ws.$workout-spacing, $size-key);
    
    // 使用 & {} 包装基础声明以符合新的 Sass 规则
    & {
      margin: $base-size;
    }
    
    @include respond-to('mobile') {
      margin: $base-size * 0.8;
    }
    
    @include respond-to('small') {
      margin: $base-size * 0.6;
    }
  }
}

// 响应式gap mixin
@mixin responsive-gap($size-key) {
  @if map.has-key(ws.$workout-spacing, $size-key) {
    $base-size: map.get(ws.$workout-spacing, $size-key);
    
    // 使用 & {} 包装基础声明以符合新的 Sass 规则
    & {
      gap: $base-size;
    }
    
    @include respond-to('mobile') {
      gap: $base-size * 0.8;
    }
    
    @include respond-to('small') {
      gap: $base-size * 0.6;
    }
  }
}

// ===== iOS专用Mixins =====

// iOS触摸反馈效果
@mixin ios-touch-feedback {
  transition: transform 0.15s ease;
  cursor: pointer;
  
  &:hover {
    transform: scale(1.02);
  }
  
  &:active {
    transform: scale(0.98);
  }
  
  // 移动端禁用hover效果
  @media (hover: none) {
    &:hover {
      transform: none;
    }
  }
}

// iOS安全区域适配
@mixin ios-safe-area($property: 'padding') {
  #{$property}-top: env(safe-area-inset-top);
  #{$property}-right: env(safe-area-inset-right);
  #{$property}-bottom: env(safe-area-inset-bottom);
  #{$property}-left: env(safe-area-inset-left);
}

// iOS毛玻璃效果
@mixin ios-glass-effect($opacity: 0.8) {
  background: rgba(255, 255, 255, $opacity);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  
  @media (prefers-color-scheme: dark) {
    background: rgba(0, 0, 0, $opacity);
  }
}

// ===== 布局Mixins =====

// Flexbox居中
@mixin flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

// Flexbox列布局
@mixin flex-column {
  display: flex;
  flex-direction: column;
}

// Flexbox行布局
@mixin flex-row {
  display: flex;
  flex-direction: row;
  align-items: center;
}

// Grid自适应列
@mixin grid-auto-fit($min-size: 250px) {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax($min-size, 1fr));
}

// ===== 性能优化Mixins =====

// GPU加速
@mixin gpu-accelerate {
  transform: translateZ(0);
  will-change: transform;
}

// CSS包含
@mixin css-contain($type: 'layout style paint') {
  contain: #{$type};
}

// ===== 动画Mixins =====

// iOS原生感动画曲线
@mixin ios-animation($duration: 0.3s) {
  transition: all $duration cubic-bezier(0.4, 0, 0.2, 1);
}

// 弹性动画
@mixin spring-animation($duration: 0.4s) {
  transition: all $duration cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

// ===== 主题Mixins =====

// 暗色主题适配
@mixin dark-theme {
  @media (prefers-color-scheme: dark) {
    @content;
  }
}

// 动画偏好设置
@mixin reduce-motion {
  @media (prefers-reduced-motion: reduce) {
    @content;
  }
}