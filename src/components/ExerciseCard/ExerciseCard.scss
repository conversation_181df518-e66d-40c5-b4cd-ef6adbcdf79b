// Exercise Card Styles - iOS优化版本（样式隔离）
.exercise-card {
  background-color: var(--bg-surface, #fff); // 确保白色背景
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  display: flex;
  flex-direction: column;
  height: 100%;
  
  // iOS优化 - 3:4宽高比
  aspect-ratio: 3 / 4; // 0.75
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  }

  &:active {
    transform: translateY(0);
  }
  
  // iOS小屏幕优化
  @media (max-width: 480px) {
    border-radius: 10px;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
    aspect-ratio: 0.85;
    
    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.12);
    }
  }
  
  // 极小屏幕优化
  @media (max-width: 360px) {
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);
    aspect-ratio: 0.9;
    
    &:hover {
      transform: none;
      box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
    }
  }
}

.exercise-card__image {
  position: relative;
  width: 100%;
  flex: 3; // 75% 高度给图像 (3:4比例中的3)
  overflow: hidden;
  background-color: var(--bg-surface, #fff); // 确保白色背景
  min-height: 100px;
  // 确保矩形显示，不是椭圆形
  border-radius: 0;
  
  // iOS优化
  @media (max-width: 480px) {
    min-height: 80px;
  }
  
  // 极小屏幕优化
  @media (max-width: 360px) {
    min-height: 70px;
  }
}

.exercise-card__image-container {
  position: relative;
  width: 100%;
  height: 100%;
}

.exercise-card__image-placeholder {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

.exercise-card__img {
  width: 100%;
  height: 100%;
  object-fit: cover; // 保持矩形，填满容器
  opacity: 0;
  transition: opacity 0.3s ease;
  
  &.loaded {
    opacity: 1;
  }
}

.exercise-card__favorite-btn {
  position: absolute;
  top: 8px;
  right: 8px; // 保持右上角位置
  width: 32px;
  height: 32px;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 2;
  
  // 未收藏状态：毛玻璃背景效果（贴合icon尺寸）
  background: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  border-radius: 50%;
  transition: all 0.2s ease;

  svg {
    width: 18px;
    height: 18px;
    color: #ffffff; // 未收藏时使用白色图标
    stroke-width: 2;
    transition: color 0.2s ease;
  }
  
  .like-icon {
    width: 16px;
    height: 16px;
    object-fit: contain;
  }

  &:hover {
    background: rgba(0, 0, 0, 0.4);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    transform: scale(1.05);
    
    svg {
      color: #ffffff;
    }
  }

  &:active {
    transform: scale(0.95);
  }

  &:focus {
    outline: none;
  }

  // 已收藏状态：完全透明
  &.favorited {
    background: none !important;
    backdrop-filter: none !important;
    -webkit-backdrop-filter: none !important;
    border-radius: 0;

    &:hover {
      background: none !important;
      backdrop-filter: none !important;
      -webkit-backdrop-filter: none !important;
      transform: scale(1.05);
    }
    
    &:active {
      transform: scale(0.95);
    }
  }
}

.exercise-card__info {
  padding: 12px;
  display: flex;
  flex-direction: column;
  gap: 6px;
  flex: 1; // 25% 高度给内容 (3:4比例中的1)
  flex-shrink: 0;
  // 放大字体以更好利用空间
  font-size: calc(var(--text-base, 14px) * 1.05);
  
  // 小屏幕优化
  @media (max-width: 480px) {
    padding: 10px;
    gap: 5px;
  }
  
  // 极小屏幕优化
  @media (max-width: 360px) {
    padding: 8px;
    gap: 4px;
  }
}

// 动作名称和器械标签同行布局
.exercise-card__header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 8px;
  margin-bottom: 2px;
  
  // 小屏幕优化
  @media (max-width: 480px) {
    gap: 6px;
    margin-bottom: 1px;
  }
  
  // 极小屏幕优化
  @media (max-width: 360px) {
    gap: 4px;
    margin-bottom: 0px;
  }
}

.exercise-card__name {
  font-size: 14px;
  font-weight: 600;
  color: var(--text-primary, #1a1a1a);
  margin: 0;
  line-height: 1.2;
  flex: 1;
  min-width: 0; // 允许文字截断
  
  // 文字截断
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2; // 标准属性兼容
  -webkit-box-orient: vertical;
  overflow: hidden;
  
  // 小屏幕优化
  @media (max-width: 480px) {
    font-size: 13px;
    -webkit-line-clamp: 2;
    line-height: 1.1;
  }
  
  // 极小屏幕优化
  @media (max-width: 360px) {
    font-size: 12px;
    -webkit-line-clamp: 1;
    line-height: 1.1;
  }
}

// 器械标签样式（位于右侧）
.exercise-card__equipment-tags {
  flex-shrink: 0;
  display: flex;
  align-items: flex-start;
}

.exercise-card__equipment-tag {
  background-color: var(--bg-tertiary, #f0f0f0);
  color: var(--text-secondary, #666);
  font-size: 11px;
  font-weight: 500;
  padding: 2px 6px;
  border-radius: 4px;
  white-space: nowrap;
  
  // 小屏幕优化
  @media (max-width: 480px) {
    font-size: 10px;
    padding: 2px 4px;
  }
  
  // 极小屏幕优化
  @media (max-width: 360px) {
    font-size: 9px;
    padding: 1px 3px;
  }
}

.exercise-card__meta {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 6px;
  margin-top: -4px;
  
  .exercise-card__difficulty {
    display: flex;
    align-items: center;
    font-size: 11px;
    color: #666;
    font-weight: 500;
    margin-top: -2px; // 额外上移难度图标
    
    // 小屏幕优化
    @media (max-width: 480px) {
      font-size: 10px;
      margin-top: -1px;
    }
    
    // 极小屏幕优化
    @media (max-width: 360px) {
      font-size: 9px;
      margin-top: -1px;
    }
    
    // 修复正确的类名选择器
    .difficulty-plate {
      // 固定尺寸，与文字行高匹配 - 解决溢出问题
      width: 14px;
      height: 14px;
      object-fit: contain;
      vertical-align: middle;
      margin-right: 4px;
      flex-shrink: 0; // 防止被压缩
      
      // 小屏幕保持固定尺寸，确保可见性
      @media (max-width: 480px) {
        width: 12px;
        height: 12px;
        margin-right: 3px;
      }
      
      // 极小屏幕最小尺寸
      @media (max-width: 360px) {
        width: 10px;
        height: 10px;
        margin-right: 2px;
      }
    }
  }
  
  // 新增：身体部位显示
  .exercise-card__body-parts {
    display: flex;
    align-items: center;
    font-size: 11px;
    color: var(--text-tertiary, #888);
    margin-top: -2px;
    
    @media (max-width: 480px) {
      margin-top: -1px;
    }
    
    @media (max-width: 360px) {
      margin-top: -1px;
    }
    
    .exercise-card__body-part-tag {
      background-color: var(--bg-info-light, #f0f8ff);
      color: var(--color-primary, #007AFF);
      padding: 1px 4px;
      border-radius: 3px;
      font-weight: 500;
      white-space: nowrap;
      font-size: 11px;
      
      @media (max-width: 480px) {
        font-size: 10px; // 与器械标签保持一致
        padding: 1px 3px;
      }
      
      @media (max-width: 360px) {
        font-size: 9px; // 与器械标签保持一致
        padding: 0px 2px;
      }
    }
  }
}

// 移除不需要的样式
.category,
.exercise-details,
.muscle-groups,
.muscle-tag {
  display: none;
}

// Dark mode support
@media (prefers-color-scheme: dark) {
  .exercise-card {
    background-color: var(--bg-surface-dark, #1a1a1a);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
    
    &:hover {
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.4);
    }
  }

  .exercise-card__image {
    background-color: var(--bg-tertiary-dark, #2a2a2a);
  }

  .exercise-card__image-placeholder {
    background: linear-gradient(90deg, #2a2a2a 25%, #3a3a3a 50%, #2a2a2a 75%);
  }

  .exercise-card__favorite-btn {
    // 暗色模式下的毛玻璃效果
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    
    svg {
      color: #ffffff; // 暗色模式下也使用白色图标
    }

    &:hover {
      background: rgba(255, 255, 255, 0.25);
      backdrop-filter: blur(10px);
      -webkit-backdrop-filter: blur(10px);
      
      svg {
        color: #ffffff;
      }
    }
    
    // 已收藏状态在暗色模式下保持透明
    &.favorited {
      background: none !important;
      backdrop-filter: none !important;
      -webkit-backdrop-filter: none !important;
      
      &:hover {
        background: none !important;
        backdrop-filter: none !important;
        -webkit-backdrop-filter: none !important;
      }
    }
  }

  .exercise-card__name {
    color: var(--text-primary-dark, #ffffff);
  }

  .exercise-card__equipment-tag {
    &:not(.more) {
      color: var(--color-accent, #64D2FF);
      background-color: rgba(100, 210, 255, 0.1);
    }
    
    &.more {
      color: var(--text-tertiary-dark, #a0a0a0);
      background-color: var(--bg-tertiary-dark, #2a2a2a);
    }
  }

  .exercise-card__body-part-tag {
    color: var(--color-accent, #64D2FF);
    background-color: rgba(100, 210, 255, 0.1);
  }
}

// Performance optimizations
.exercise-card {
  contain: layout style paint;
  will-change: transform;
}

.exercise-img {
  will-change: opacity;
}

// Reduced motion support
@media (prefers-reduced-motion: reduce) {
  .image-placeholder {
    animation: none;
    background: #f0f0f0;
  }
}