// FitMaster WorkoutExerciseGrid - 重构版本
// 使用现代Sass语法和统一样式系统

@use '../../../styles/_workout-system' as ws;
@use '../../../styles/_mixins' as mx;

.workout-exercise-grid {
  height: 100%;
  display: flex;
  flex-direction: column;

  // ===== 统一的Exercise信息样式 =====
  // 统一的exercise-name样式 - 增加字体大小
  .exercise-name {
    color: var(--text-primary);
    line-height: 1.2;
    margin: 0;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    @include mx.responsive-font('xl', 'medium'); // 18px -> 17px -> 16px -> 15px (增大字体)
  }
  
  // 统一的sets-info样式 - 增加字体大小和可见性
  .sets-info {
    color: var(--text-secondary, #6b7280);
    font-weight: 400;
    margin: 0;
    // 确保在所有背景下可见
    text-shadow: 0 0 1px rgba(255, 255, 255, 0.3);
    @include mx.responsive-font('lg', 'normal'); // 16px -> 15px -> 14px -> 13px (增大字体)
    
    @include mx.dark-theme {
      color: var(--text-secondary-dark, #cbd5e1);
      text-shadow: 0 0 1px rgba(0, 0, 0, 0.3);
    }
  }
  
  // 统一的exercise-details容器 - 与exercise-name左对齐
  .exercise-details {
    display: flex;
    justify-content: flex-start; // 与exercise-name左对齐
    align-items: center;
    margin: 0;
    padding: 0;
  }

  // 网格容器 - 支持多种布局模式
  .exercise-grid-container {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    
    // 1个动作：左右分布，图像在左文字在右
    .single-horizontal-container {
      @include mx.flex-center;
      height: 100%;
      @include mx.responsive-padding('sm');
      
      .exercise-grid-item.horizontal-layout {
        width: 100%;
        max-width: 100%;
        justify-content: center; // 整体内容在容器中居中对齐
        @include mx.flex-row;
        @include mx.responsive-gap('md');
        
        .exercise-image-wrapper {
          height: calc(100% - 8px);
          aspect-ratio: 1;
          border-radius: 50%;
          overflow: hidden;
          background: var(--bg-surface, white);
          @include mx.flex-center;
          box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
          flex-shrink: 0;
          
          .exercise-image {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 50%;
          }
        }
        
        .exercise-info {
          min-width: 0;
          width: auto; // 根据内容自适应宽度，避免占据过多空间
          @include mx.flex-column;
          @include mx.responsive-gap('xs');
          
          // 使用统一样式类，不再重复定义字体
        }
      }
    }
    
    // 2个动作：左右分布，每个动作内部也是左右布局
    .dual-horizontal-container {
      justify-content: space-between;
      height: 100%;
      @include mx.flex-row;
      @include mx.responsive-gap('sm');
      @include mx.responsive-padding('sm');
      
      .exercise-grid-item.horizontal-layout {
        flex: 1;
        min-width: 0;
        justify-content: center; // 整体内容在容器中居中对齐
        @include mx.flex-row;
        @include mx.responsive-gap('sm');
        
        .exercise-image-wrapper {
          height: calc(100% - 6px);
          aspect-ratio: 1;
          border-radius: 50%;
          overflow: hidden;
          background: var(--bg-surface, white);
          @include mx.flex-center;
          box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
          flex-shrink: 0;
          
          .exercise-image {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 50%;
          }
        }
        
        .exercise-info {
          @include mx.flex-column;
          gap: 1px;
          min-width: 0;
          width: auto; // 根据内容自适应宽度，避免占据过多空间
          
          // 双列布局也使用大字体，但稍小一些
          .exercise-name {
            line-height: 1.1;
            @include mx.responsive-font('lg', 'medium'); // 16px -> 15px -> 14px -> 13px (增大)
          }
          
          .sets-info {
            @include mx.responsive-font('md', 'normal'); // 14px -> 13px -> 12px -> 11px (增大)
          }
        }
      }
    }
    
    // 3-4个动作：网格布局，每个动作内部上下分布
    .grid-vertical-container {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(0, 1fr));
      gap: 2px;
      height: 100%;
      align-items: center;
      @include mx.responsive-padding('sm');
      
      // 根据动作数量调整网格
      &:has(.exercise-grid-item:nth-child(3):not(:nth-child(4))) {
        grid-template-columns: repeat(3, 1fr); // 3个动作：3列
      }
      
      &:has(.exercise-grid-item:nth-child(4)) {
        grid-template-columns: repeat(2, 1fr); // 4个动作：2列2行
      }
      
      .exercise-grid-item.vertical-layout {
        gap: 1px;
        width: 100%;
        min-width: 0;
        text-align: center; // 内容居中对齐
        align-items: center; // 整体内容在容器中居中对齐
        @include mx.flex-column; // 改为上下分布布局
        
        .exercise-image-wrapper {
          width: 80%;
          aspect-ratio: 1;
          border-radius: 50%;
          overflow: hidden;
          background: var(--bg-surface, white);
          @include mx.flex-center;
          box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
          flex-shrink: 0;
          margin: 0 auto; // 图像居中
          
          .exercise-image {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 50%;
          }
        }
        
        .exercise-info {
          width: 100%;
          @include mx.flex-column;
          gap: 1px;
          min-width: 0;
          
          // 网格布局使用中等字体，确保可读性
          .exercise-name {
            line-height: 1.1;
            text-align: center; // 名称居中显示
            @include mx.responsive-font('md', 'medium'); // 14px -> 13px -> 12px -> 11px (提升可读性) 
          }
          
          .exercise-details {
            display: flex;
            justify-content: center; // 垂直布局中保持居中对齐，与exercise-name一致
            align-items: center;
            margin: 0;
            padding: 0;
          }
          
          .sets-info {
            text-align: center; // 组数信息居中显示
            @include mx.responsive-font('sm', 'normal'); // 12px -> 11px -> 10px -> 9px (增大)
          }
        }
      }
    }
    
    // 原有的单行容器（保留作为降级方案）
    .single-row-container {
      height: 100%;
      @include mx.flex-center;
      @include mx.responsive-gap('md');
      
      .exercise-grid-item {
        gap: 0;
        padding: 0;
        background: transparent;
        border: none;
        text-align: center;
        min-width: 0;
        align-items: center; // 整体内容在容器中居中对齐
        @include mx.flex-column;
        @include mx.flex-center;
        @include mx.ios-animation;
        
        .exercise-image-wrapper {
          width: 80px;
          height: 80px;
          border-radius: 50%;
          background: white;
          overflow: hidden;
          @include mx.flex-center;
          box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
          
          @include mx.respond-to('mobile') {
            width: 60px;
            height: 60px;
          }
          
          .exercise-image {
            width: 100%;
            height: 100%;
            border-radius: 50%;
            object-fit: cover;
          }
        }
        
        .exercise-info {
          width: 100%;
          @include mx.flex-column;
          gap: 0;
          padding: 0;
          margin-top: 2px;
          
          .exercise-name {
            text-align: center;
            max-width: 100%;
            @include mx.responsive-font('lg', 'medium'); // 单行时使用较大字体
            @include mx.text-truncate;
          }
          
          .exercise-details {
            display: flex;
            justify-content: center; // 与exercise-name的text-align: center保持一致
            align-items: center;
            margin: 0;
            padding: 0;
            
            .sets-info {
              @include mx.responsive-font('md', 'normal'); // 增大字体
            }
          }
        }
      }
    }
    
    // 行容器 - 简化版本
    .row-container {
      display: grid;
      grid-template-columns: repeat(4, minmax(0, 1fr));
      gap: 0;
      align-items: start;
      
      &.first-row {
        flex: 0.4;
      }
      
      &.second-row {
        flex: 0.6;
      }
      
      // 使用新的响应式系统
      @include mx.respond-to('tablet') {
        grid-template-columns: repeat(3, 1fr);
      }
      
      @include mx.respond-to('mobile') {
        grid-template-columns: repeat(2, 1fr);
      }
      
      .exercise-grid-item {
        gap: 0;
        padding: 0;
        background: transparent;
        border: none;
        text-align: center;
        min-width: 0;
        align-items: center; // 整体内容在容器中居中对齐
        @include mx.flex-column;
        @include mx.flex-center;
        @include mx.ios-animation;
        
        .exercise-image-wrapper {
          width: calc((100vw - 32px) / 4 - 4px);
          height: calc((100vw - 32px) / 4 - 4px);
          max-width: 60px;
          max-height: 60px;
          min-width: 35px;
          min-height: 35px;
          border-radius: 50%;
          background: white;
          overflow: hidden;
          @include mx.flex-center;
          box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
          
          @include mx.respond-to('tablet') {
            width: calc((100vw - 24px) / 3 - 3px);
            height: calc((100vw - 24px) / 3 - 3px);
          }
          
          @include mx.respond-to('mobile') {
            width: calc((100vw - 16px) / 2 - 2px);
            height: calc((100vw - 16px) / 2 - 2px);
          }
          
          .exercise-image {
            width: 100%;
            height: 100%;
            border-radius: 50%;
            object-fit: cover;
          }
        }
        
        .exercise-info {
          width: 100%;
          @include mx.flex-column;
          gap: 0;
          padding: 0;
          margin-top: 2px;
          
          .exercise-name {
            text-align: center;
            max-width: 100%;
            @include mx.responsive-font('md', 'medium'); // 提升字体大小以增强可读性
            @include mx.text-truncate;
          }
          
          .exercise-details {
            display: flex;
            justify-content: center; // 与exercise-name的text-align: center保持一致
            align-items: center;
            margin: 0;
            padding: 0;
            
            .sets-info {
              @include mx.responsive-font('sm', 'normal'); // 增大字体
            }
          }
        }
      }
    }
  }

  .overflow-indicator {
    text-align: center;
    padding: 8px;
    font-size: 10px;
    color: var(--text-secondary, #6b7280);
    font-weight: 500;
    background: var(--bg-secondary, #f8fafc);
    border-radius: 6px;
    margin-top: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    
    &:hover {
      background: var(--bg-hover, #f1f5f9);
      color: var(--accent-500);
    }
  }
}

// ===== 简化的主题适配 =====
// 暗色主题适配 - 使用统一的主题系统
@include mx.dark-theme {
  .workout-exercise-grid {
    .exercise-grid-container {
      .exercise-grid-item {
        .exercise-image-wrapper {
          background: var(--bg-primary-dark, #0f172a);
        }
      }
    }
    
    .overflow-indicator {
      background: var(--bg-secondary-dark, #1e293b);
      color: var(--text-secondary-dark, #cbd5e1);
      
      &:hover {
        background: var(--bg-hover-dark, #334155);
        color: var(--accent-400);
      }
    }
  }
} 