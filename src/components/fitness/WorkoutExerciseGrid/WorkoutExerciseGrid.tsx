import React, { useMemo } from 'react';
import './WorkoutExerciseGrid.scss';

// 健身动作类型定义
export interface WorkoutExercise {
  id: string;
  name: string;
  image_url?: string;
  weight?: number;
  sets?: number;
  reps?: number;
  muscle_groups?: string[];
}

// 带肌肉信息的健身动作类型
export interface WorkoutExerciseWithMuscles extends WorkoutExercise {
  muscle_groups: string[];
  primary_muscle?: string;
  secondary_muscles?: string[];
}

// 组件属性接口
interface WorkoutExerciseGridProps {
  exercises: WorkoutExercise[];
  maxItemsPerRow?: number;
  maxRows?: number;
  showImages?: boolean;
  showWeightInfo?: boolean;
  className?: string;
}

export const WorkoutExerciseGrid: React.FC<WorkoutExerciseGridProps> = ({
  exercises,
  maxItemsPerRow = 4,
  maxRows = 2, // 默认2行
  showImages = true,
  showWeightInfo = false,
  className = ''
}) => {
  // 计算要显示的动作数量
  const displayExercises = useMemo(() => {
    const maxItems = maxItemsPerRow * maxRows;
    return exercises.slice(0, maxItems);
  }, [exercises, maxItemsPerRow, maxRows]);

  // 根据动作数量计算自适应布局
  const layoutConfig = useMemo(() => {
    const totalItems = displayExercises.length;
    
    if (totalItems === 1) {
      // 1个动作：左图右文布局
      return {
        type: 'single-horizontal',
        layout: 'image-text',
        items: displayExercises
      };
    } else if (totalItems === 2) {
      // 2个动作：并排左图右文
      return {
        type: 'dual-horizontal', 
        layout: 'image-text',
        items: displayExercises
      };
    } else if (totalItems <= 4) {
      // 3-4个动作：图上文下网格布局
      return {
        type: 'grid-vertical',
        layout: 'image-over-text', 
        items: displayExercises
      };
    } else {
      // 超过4个：只显示前4个，网格布局
      return {
        type: 'grid-vertical',
        layout: 'image-over-text',
        items: displayExercises.slice(0, 4),
        hasMore: true
      };
    }
  }, [displayExercises]);

  // 渲染动作项的公共函数
  const renderExerciseItem = (exercise: WorkoutExercise, itemClass: string = '') => (
    <div key={exercise.id} className={`exercise-grid-item ${itemClass}`}>
      {showImages && exercise.image_url && (
        <div className="exercise-image-wrapper">
          <img
            src={exercise.image_url}
            alt={exercise.name}
            loading="lazy"
            className="exercise-image"
          />
        </div>
      )}
      <div className="exercise-info">
        <div className="exercise-name">{exercise.name}</div>
        {showWeightInfo && exercise.weight && exercise.sets && exercise.reps && (
          <div className="exercise-details">
            <span className="sets-info">{exercise.sets}组</span>
          </div>
        )}
      </div>
    </div>
  );

  return (
    <div className={`workout-exercise-grid ${className} layout-${layoutConfig.type}`}>
      <div className="exercise-grid-container">
        {layoutConfig.type === 'single-horizontal' && (
          // 1个动作：左图右文布局
          <div className="single-horizontal-container">
            {renderExerciseItem(layoutConfig.items[0], 'horizontal-layout')}
          </div>
        )}
        
        {layoutConfig.type === 'dual-horizontal' && (
          // 2个动作：并排左图右文
          <div className="dual-horizontal-container">
            {layoutConfig.items.map((exercise) => 
              renderExerciseItem(exercise, 'horizontal-layout')
            )}
          </div>
        )}
        
        {layoutConfig.type === 'grid-vertical' && (
          // 3-4个动作：图上文下网格布局
          <div className="grid-vertical-container">
            {layoutConfig.items.map((exercise) => 
              renderExerciseItem(exercise, 'vertical-layout')
            )}
          </div>
        )}
      </div>
      
      {layoutConfig.hasMore && (
        <div className="overflow-indicator">
          +{exercises.length - 4} 更多
        </div>
      )}
    </div>
  );
}; 