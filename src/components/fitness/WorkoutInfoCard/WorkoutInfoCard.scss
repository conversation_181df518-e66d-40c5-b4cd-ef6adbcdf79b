// WorkoutInfoCard 组件样式
// 整合肌肉信息和训练动作的卡片，宽高比3:4，上下82/18分配（减少底部空白）
// 新增：基于容器宽度的响应式布局系统，确保自适应宽度

.workout-info-card {
  width: 100%;
  height: auto; // 高度由内容自适应
  
  // 基础样式
  background: var(--bg-surface);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  padding: var(--space-2); // 减少基础padding
  
  // 垂直布局
  display: flex;
  flex-direction: column;
  gap: var(--space-4); // 在muscle-section和exercises-section之间添加间距
  
  // 阴影和交互效果
  box-shadow: var(--shadow-sm);
  transition: all var(--transition-normal) var(--ease-in-out);
  
  // CSS变量定义（由JavaScript动态设置）
  --muscle-view-scale: 1;
  --muscle-tag-font-size: 12px;
  --muscle-title-font-size: 14px;
  --sidebar-width: 20%; // 固定20%比例
  
  &:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
  }
  
  // 肌肉信息区域
  .muscle-section {
    flex: none; // 不再使用flex比例
    height: auto; // 高度自适应
    min-height: 0;
    overflow: hidden;
    
    .workout-muscle-card {
      height: 100%;
      border: none; // 移除内部边框，避免双重边框
      box-shadow: none; // 移除内部阴影
      padding: 0; // 移除内部padding，使用外部容器的padding
      background: transparent; // 背景透明，使用外部容器背景
      display: flex; // 确保flex布局
      gap: var(--space-2); // 减少内部间隙
      align-items: stretch; // 拉伸对齐，确保底部对齐
      overflow: visible; // 防止肌肉示意图被裁剪
      position: relative; // 为子元素层级控制做准备
      
      // 左侧肌肉列表边栏（严格20%宽度）
      .muscle-list-sidebar {
        // 使用固定20%宽度，确保2:8比例
        flex: 0 0 20%;
        display: flex;
        flex-direction: column;
        gap: var(--space-2);
        min-width: 0; // 允许收缩
        height: 100%; // 占满可用空间
        overflow-y: auto; // 整体滚动设置
        overflow-x: hidden;
        background: transparent; // 设置背景为透明，防止遮挡肌肉示意图
        position: relative; // 为层级控制做准备
        z-index: 1; // 确保标签区域在肌肉示意图下方
        margin-top: 1vw;
        margin-left: 1vw; // 设置左侧margin为页面宽度的5%

        // 无痕滚动样式 - iOS优化
        -webkit-overflow-scrolling: touch;
        scrollbar-width: none; // Firefox
        -ms-overflow-style: none; // IE/Edge

        // 隐藏滚动条但保持滚动功能
        &::-webkit-scrollbar {
          display: none; // Chrome/Safari/WebKit
        }

        // 添加渐变遮罩效果，提示可滚动内容
        &::after {
          content: '';
          position: sticky;
          bottom: 0;
          left: 0;
          right: 0;
          height: 20px;
          background: linear-gradient(transparent, var(--bg-surface));
          pointer-events: none;
          opacity: 0;
          transition: opacity var(--transition-normal) var(--ease-in-out);
        }

        // 当内容可滚动时显示渐变遮罩
        &.scrollable::after {
          opacity: 1;
        }

        // 肌肉分类样式 - 与ExerciseDetailPage保持一致
        .muscle-category {
          margin-bottom: var(--space-1); // 进一步减少主要和次要标签之间的间隔
          flex-shrink: 0; // 不让分类标题收缩

          // 进一步减少最后一个分类的底部间距
          &:last-child {
            margin-bottom: 0;
          }

          h3 {
            // 使用CSS变量动态字体大小
            font-size: var(--muscle-title-font-size);
            font-weight: var(--font-bold);
            color: var(--text-primary);
            margin: 0 0 var(--space-2) 0;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            position: relative;
            padding-bottom: var(--space-2);
            display: inline-block;
            width: auto;
            white-space: nowrap; // 防止标题换行

            // 使用CSS变量的下划线
            background-image: linear-gradient(transparent, transparent);
            background-repeat: no-repeat;
            background-size: 100% 3px;
            background-position: 0 calc(100% - 2px);

            // iOS小屏幕优化
            @media (max-width: 768px) {
              font-size: calc(var(--muscle-title-font-size) * 0.9);
              margin: 0 0 var(--space-1) 0;
              padding-bottom: var(--space-1);
            }
          }

          // 主要肌肉标题 - 使用设计系统颜色
          &:has(.muscle-tag.primary) h3 {
            background-image: linear-gradient(var(--accent-500), var(--accent-500));
          }

          // 次要肌肉标题 - 使用设计系统颜色
          &:has(.muscle-tag.secondary) h3 {
            background-image: linear-gradient(var(--accent-300), var(--accent-300));
          }

          .muscle-tags {
            display: flex;
            flex-direction: column;
            gap: var(--space-2);
            align-items: flex-start;
            width: 100%;
            // 移除独立滚动设置，使用父容器的整体滚动

            .muscle-tag {
              display: inline-block;
              padding: clamp(2px, 1vw, 4px) clamp(4px, 1.5vw, 8px); // 动态内边距
              border-radius: var(--radius-xl); // 使用大圆角设计
              // 使用CSS变量动态字体大小
              font-size: var(--muscle-tag-font-size);
              font-weight: var(--font-medium);
              text-align: center;
              white-space: nowrap;
              width: fit-content;
              max-width: 100%;
              transition: all var(--transition-normal) var(--ease-in-out);
              line-height: 1.2; // 紧凑的行高
              overflow: hidden; // 防止文本溢出
              text-overflow: ellipsis; // 文本溢出时显示省略号

              // 统一所有肌肉标签的外观，无论主要还是次要
              background: var(--bg-tertiary); // 统一背景色：#d1d5db (gray-300)
              color: var(--text-secondary); // 统一文字色：#4b5563 (gray-600)
              border: none; // 移除边框，保持简洁

              // iOS触摸反馈 - 统一的悬停效果
              &:hover {
                transform: translateY(-1px);
                box-shadow: var(--shadow-sm);
                background: var(--bg-hover); // 悬停时使用统一的悬停背景色
              }

              &:active {
                transform: scale(0.98);
              }

              // iOS小屏幕优化
              @media (max-width: 768px) {
                padding: clamp(2px, 0.8vw, 4px) clamp(3px, 1.2vw, 6px);
                font-size: calc(var(--muscle-tag-font-size) * 0.9);
              }
            }
          }
        }
      }
      
      // 右侧肌肉图容器（严格80%宽度）
      .muscle-illustration-container {
        // 严格80%宽度，确保2:8比例
        flex: 0 0 80%;
        min-width: 0; // 允许收缩
        display: flex;
        justify-content: center; // 居中对齐，减少空白
        align-items: flex-start; // 改为顶部对齐，避免在有过高容器时垂直居中
        min-height: 0; // 允许收缩
        height: auto; // 高度由内容决定，不再是100%
        overflow: visible; // 移除 hidden，防止图像被遮挡
        position: relative; // 为层级控制做准备
        z-index: 1100; // 设置高 z-index，确保在所有其他元素之上

        // 移除过度限制，让肌肉图自然显示
        > * {
          width: 100%;
          height: auto; // 高度自适应
          object-fit: contain;
        }

        // 优化肌肉示意图的显示，确保完全填充容器
        .static-muscle-illustration {
          width: 100% !important; // 强制100%宽度
          height: auto !important; // 高度自适应，不再是100%
          max-width: none !important; // 移除最大宽度限制
          max-height: none !important; // 移除最大高度限制
          margin: 0 !important; // 移除所有边距
          padding: 0 !important; // 移除所有内边距
          position: relative; // 为 iOS 层级优化做准备
          z-index: inherit; // 继承父容器的高 z-index
          display: flex !important; // 强制flex布局
          align-items: center !important; // 垂直居中
          justify-content: center !important; // 水平居中
          overflow: visible !important; // 确保内容不被裁剪

          svg {
            width: 100% !important;
            height: auto !important; // SVG高度自适应
            display: block !important; // 移除默认的 inline 间距
            max-width: none !important; // 移除SVG最大宽度限制
            max-height: none !important; // 移除SVG最大高度限制
            object-fit: contain !important; // 保持比例的情况下适应容器大小
            position: relative; // iOS Safari 优化
            z-index: inherit; // 继承父容器的 z-index

            // iOS 特定优化：确保 SVG 不被裁剪
            @supports (-webkit-touch-callout: none) {
              transform: translateZ(0); // 启用硬件加速
              will-change: transform; // 优化渲染性能
            }
          }
        }

        // 暂无数据状态
        .no-muscle-data {
          width: 100%;
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
          color: var(--text-secondary);
          font-size: clamp(10px, 2vw, 14px); // 动态字体大小
          background: var(--bg-secondary);
          border-radius: var(--radius-md);
          border: 2px dashed var(--border-color);
          
          p {
            margin: 0;
          }
        }
      }
      
      // iOS设备保持左右布局，不改为上下布局
      @media (max-width: 768px) {
        gap: var(--space-3);
      }

      // 只有极小屏幕（iPhone SE等）才考虑上下布局
      @media (max-width: 375px) {
        flex-direction: column;
        align-items: stretch;
        gap: var(--space-4);
        
        .muscle-list-sidebar {
          flex: none;
          width: 100%;
          height: auto;
          max-height: 120px; // 限制高度
        }
        
        .muscle-illustration-container {
          flex: none;
          height: 200px; // 固定高度
          
          .static-muscle-illustration {
            transform: scale(0.8); // 小屏幕固定缩放
          }
        }
      }
    }
  }
  
  // 动作列表区域
  .exercises-section {
    flex: none; // 不再使用flex比例
    height: auto; // 高度自适应
    min-height: 0; // 允许收缩
    padding-top: 0; // 增加顶部间距
    border-top: 1px solid var(--border-light); // 添加分割线
    
    .workout-exercise-grid {
      height: 100%;
    }
    
    // 空状态样式
    .empty-exercises {
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      color: var(--text-secondary);
      font-size: clamp(10px, 2vw, 14px); // 动态字体大小
      background: var(--bg-secondary);
      border-radius: var(--radius-md);
      border: 2px dashed var(--border-color);
      padding: var(--space-4); // 增加内边距
      
      p {
        margin: 0;
      }
    }
  }
  
  // ===== 布局模式样式 (移除所有固定比例) =====
  
  // 响应式设计 - 移除所有固定比例
  
  @media (min-width: 1440px) {
    max-width: 450px;
    margin: 0 auto;
    
    &:hover {
      box-shadow: var(--shadow-lg);
      transform: translateY(-4px);
    }
  }
  
  @media (min-width: 1024px) and (max-width: 1439px) {
    max-width: 400px;
    margin: 0 auto;
  }
  
  @media (min-width: 768px) and (max-width: 1023px) {
    max-width: 350px;
  }
  
  @media (max-width: 767px) {
    padding: var(--space-3);
    gap: var(--space-2);
  }
  
  @media (max-width: 480px) {
    padding: var(--space-2);
  }
  
  // ===== Web端特有优化 =====
  
  @media (min-width: 1024px) {
    &:focus-within {
      outline: 2px solid var(--accent-500);
      outline-offset: 2px;
    }
    
    &:active {
      transform: translateY(-1px);
      transition: transform 0.1s ease;
    }
  }
  
  @media (min-width: 1024px) {
    &.grid-item {
      width: 100%;
      height: fit-content;
      max-width: none;
    }
  }
  
  @media (max-width: 375px) {
    &.vertical-scroll {
      aspect-ratio: auto;
      height: auto;
      max-height: 80vh;
      overflow-y: auto;
      
      .muscle-section, .exercises-section {
        flex: none;
        height: auto;
      }
    }
  }
}

// ===== 网格布局容器样式 =====
// 用于Web端多个WorkoutInfoCard的网格布局

.workout-info-card-grid {
  display: grid;
  gap: var(--space-6);
  padding: var(--space-6);
  
  // 桌面端网格布局（1024px+）
  @media (min-width: 1024px) {
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    max-width: 1200px;
    margin: 0 auto;
  }
  
  // 平板端网格布局（768px-1023px）
  @media (min-width: 768px) and (max-width: 1023px) {
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: var(--space-4);
    padding: var(--space-4);
  }
  
  // 移动端网格布局（768px以下）
  @media (max-width: 767px) {
    grid-template-columns: 1fr;
    gap: var(--space-4);
    padding: var(--space-4);
  }
  
  // 大屏端网格布局（1440px+）
  @media (min-width: 1440px) {
    grid-template-columns: repeat(auto-fit, minmax(450px, 1fr));
    max-width: 1400px;
    gap: var(--space-8);
    padding: var(--space-8);
  }
}

// 特殊主题样式
.workout-info-card {
  // 暗色主题
  &.theme-dark {
    background: var(--bg-surface-dark, #374151);
    border-color: var(--border-color-dark, #4b5563);
    
    .exercises-section {
      border-top-color: var(--border-color-dark, #4b5563);
    }
    

    
    .empty-exercises {
      background: var(--bg-secondary-dark, #4b5563);
      border-color: var(--border-color-dark, #4b5563);
      color: var(--text-secondary-dark, #d1d5db);
    }
  }
  
  // 紧凑模式
  &.compact {
    aspect-ratio: 4 / 3; // 更宽的比例
    
    .muscle-section {
      flex: 0.6; // 减少肌肉区域
    }
    
    .exercises-section {
      flex: 0.4; // 增加动作区域
    }
  }
  
  // 展开模式
  &.expanded {
    aspect-ratio: 3 / 5; // 更高的比例
    
    .muscle-section {
      flex: 0.75; // 增加肌肉区域
    }
    
    .exercises-section {
      flex: 0.25; // 减少动作区域
    }
  }
} 