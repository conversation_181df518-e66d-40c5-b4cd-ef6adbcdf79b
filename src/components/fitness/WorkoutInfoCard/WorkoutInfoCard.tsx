import React, { useMemo } from 'react';
import { MuscleInfoCard } from '../MuscleVisualization/MuscleInfoCard';
import { WorkoutExerciseGrid } from '../WorkoutExerciseGrid/WorkoutExerciseGrid';
import { WorkoutData } from '../../../models/ui/exercise/Workout';
import './WorkoutInfoCard.scss';

// 组件属性接口
export interface WorkoutInfoCardProps {
  workout: WorkoutData; // 直接接收完整的workout数据
  theme?: 'light' | 'dark';
  className?: string;
  showExerciseImages?: boolean;
  showWeightInfo?: boolean;
  maxItemsPerRow?: number;
  maxRows?: number;
}

/**
 * 训练信息卡片组件
 * 整合肌肉信息展示和训练动作列表
 */
export const WorkoutInfoCard: React.FC<WorkoutInfoCardProps> = ({
  workout,
  theme = 'light',
  className = '',
  showExerciseImages = true,
  showWeightInfo = true,
  maxItemsPerRow = 4,
  maxRows = 2
}) => {
  // 使用真实的workout数据
  const muscleData = useMemo(() => {
    return {
      targetMuscleIds: workout.targetMuscleIds || [],
      synergistMuscleIds: workout.synergistMuscleIds || [],
      exercises: workout.exercises || [],
      muscleIntensities: workout.muscle_intensities || []
    };
  }, [workout]);

  return (
    <div 
      className={`workout-info-card ${className}`}
    >
      {/* 上方：肌肉信息区域 */}
      <div className="muscle-section">
        <MuscleInfoCard
          targetMuscleIds={muscleData.targetMuscleIds}
          synergistMuscleIds={muscleData.synergistMuscleIds}
          theme={theme}
          className="workout-muscle-card"
        />
      </div>

      {/* 下方：动作列表区域 */}
      <div className="exercises-section">
        {muscleData.exercises.length > 0 ? (
          <WorkoutExerciseGrid
            exercises={muscleData.exercises}
            maxItemsPerRow={maxItemsPerRow}
            maxRows={maxRows}
            showImages={showExerciseImages}
            showWeightInfo={showWeightInfo}
            className="workout-exercise-grid"
          />
        ) : (
          <div className="empty-exercises">
            <p>暂无训练动作</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default WorkoutInfoCard; 