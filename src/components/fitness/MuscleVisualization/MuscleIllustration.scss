/* 肌肉选择器样式 - 与参考实现保持一致 */
#muscle-illustration {
    width: 100% !important;
    height: 100% !important;
    max-width: none !important;
    user-select: none;
}

.muscle-group {
    cursor: pointer;
    transition: all 0.1s ease-out;
}

.muscle-group:hover .muscle-default {
    fill: #93c5fd; /* 悬停时的蓝色 */
}

.muscle-default {
    fill: #94a3b8; /* 默认的灰色 */
}

.muscle-selected {
    fill: #3b82f6; /* 选中时的蓝色 */
}

.fill-transparent {
    fill: transparent;
}

/* 加载状态 */
.loading {
  fill: #757575;
  animation: pulse 2s linear infinite;
}

@keyframes pulse {
  0% {
    opacity: 0.5;
  }
  50% {
    opacity: 0.8;
  }
  100% {
    opacity: 0.5;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
    #muscle-illustration {
        width: 100% !important;
        height: 100% !important;
        max-width: none !important;
    }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .svgContainer {
        width: 100% !important;
        height: 100% !important;
        max-width: none !important;
    }
} 