// MuscleInfoCard 组件样式
// 提取自 ExerciseDetailPage.scss，保持与原页面一致的布局和视觉效果

.muscle-info-card {
  background: var(--bg-surface);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  padding: var(--space-4);
  display: flex;
  gap: var(--space-4);
  align-items: flex-start;
  min-height: 360px; // 增加最小高度，确保肌肉示意图完整显示
  overflow: visible; // 改为 visible，防止肌肉示意图被裁剪
  position: relative; // 为子元素层级控制做准备

  // iOS设备保持左右布局，不改为上下布局
  @media (max-width: 768px) {
    gap: var(--space-3);
    min-height: 320px; // 增加移动端最小高度
    padding: var(--space-3);
  }

  // 只有极小屏幕（iPhone SE等）才考虑上下布局
  @media (max-width: 375px) {
    flex-direction: column;
    align-items: stretch;
    gap: var(--space-4);
    min-height: auto;
  }

  // 左侧肌肉列表边栏（严格20%宽度）
  .muscle-list-sidebar {
    flex: 0 0 20%;
    display: flex;
    flex-direction: column;
    gap: var(--space-2);
    min-width: 100px;
    height: 320px; // 与肌肉示意图高度保持一致
    overflow-y: auto; // 整体滚动设置
    overflow-x: hidden;
    background: transparent; // 设置背景为透明，防止遮挡肌肉示意图
    position: relative; // 为层级控制做准备
    z-index: 1; // 确保标签区域在肌肉示意图下方

    // 无痕滚动样式 - iOS优化
    -webkit-overflow-scrolling: touch;
    scrollbar-width: none; // Firefox
    -ms-overflow-style: none; // IE/Edge

    // 隐藏滚动条但保持滚动功能
    &::-webkit-scrollbar {
      display: none; // Chrome/Safari/WebKit
    }

    // 添加渐变遮罩效果，提示可滚动内容
    &::after {
      content: '';
      position: sticky;
      bottom: 0;
      left: 0;
      right: 0;
      height: 20px;
      background: linear-gradient(transparent, var(--bg-surface));
      pointer-events: none;
      opacity: 0;
      transition: opacity var(--transition-normal) var(--ease-in-out);
    }

    // 当内容可滚动时显示渐变遮罩
    &.scrollable::after {
      opacity: 1;
    }

    // iOS设备优化 - 保持20%比例
    @media (max-width: 768px) {
      flex: 0 0 20%;
      min-width: 80px;
      height: 240px; // 移动端稍微减少整体高度
    }

    // 只有极小屏幕才改为100%宽度
    @media (max-width: 375px) {
      flex: none;
      width: 100%;
      min-width: auto;
    }

    .muscle-category {
      margin-bottom: var(--space-1); // 进一步减少主要和次要标签之间的间隔
      flex-shrink: 0; // 不让分类标题收缩

      // 进一步减少最后一个分类的底部间距
      &:last-child {
        margin-bottom: 0;
      }

      h3 {
        font-size: var(--text-sm);
        font-weight: var(--font-bold);
        color: var(--text-primary);
        margin: 0 0 var(--space-2) 0;
        text-transform: uppercase;
        letter-spacing: 0.05em;
        position: relative;
        padding-bottom: var(--space-2);
        display: inline-block;
        width: auto;

        // 使用CSS变量的下划线
        background-image: linear-gradient(transparent, transparent);
        background-repeat: no-repeat;
        background-size: 100% 3px;
        background-position: 0 calc(100% - 2px);

        // iOS小屏幕优化
        @media (max-width: 768px) {
          font-size: var(--text-xs);
          margin: 0 0 var(--space-1) 0;
          padding-bottom: var(--space-1);
        }
      }

      // 主要肌肉标题 - 使用设计系统颜色
      &:has(.muscle-tag.primary) h3 {
        background-image: linear-gradient(var(--accent-500), var(--accent-500));
      }

      // 次要肌肉标题 - 使用设计系统颜色
      &:has(.muscle-tag.secondary) h3 {
        background-image: linear-gradient(var(--accent-300), var(--accent-300));
      }

      .muscle-tags {
        display: flex;
        flex-direction: column;
        gap: var(--space-2);
        align-items: flex-start;
        width: 100%;
        // 移除独立滚动设置，使用父容器的整体滚动

        .muscle-tag {
          display: inline-block;
          padding: var(--space-1) var(--space-2); // 减少内边距，更紧凑包裹文字
          border-radius: var(--radius-xl); // 使用大圆角设计
          font-size: var(--text-xs); // 使用更小的字体
          font-weight: var(--font-medium);
          text-align: center;
          white-space: nowrap;
          width: fit-content;
          max-width: 100%;
          transition: all var(--transition-normal) var(--ease-in-out);
          line-height: 1.2; // 紧凑的行高

          // 统一所有肌肉标签的外观，无论主要还是次要
          background: var(--bg-tertiary); // 统一背景色：#d1d5db (gray-300)
          color: var(--text-secondary); // 统一文字色：#4b5563 (gray-600)
          border: none; // 移除边框，保持简洁

          // iOS触摸反馈 - 统一的悬停效果
          &:hover {
            transform: translateY(-1px);
            box-shadow: var(--shadow-sm);
            background: var(--bg-hover); // 悬停时使用统一的悬停背景色
          }

          &:active {
            transform: scale(0.98);
          }

          // iOS小屏幕优化
          @media (max-width: 768px) {
            padding: var(--space-1) var(--space-2); // 保持紧凑的padding
            font-size: var(--text-xs); // 保持小字体
          }
        }
      }
    }
  }

  // 右侧肌肉图容器（严格80%宽度）
  .muscle-illustration-container {
    flex: 0 0 80%;
    display: flex;
    justify-content: center; // 水平居中对齐
    align-items: center; // 垂直居中对齐，解决iOS端示意图不居中问题
    height: 100%; // 使用100%高度，确保完全填充父容器
    min-height: 280px; // 减少最小高度，避免顶部空间过大
    overflow: visible; // 移除 hidden，防止图像被遮挡
    position: relative; // 为层级控制做准备
    z-index: 1100; // 设置高 z-index，确保在所有其他元素之上

    // 移除过度限制，让肌肉图自然显示
    > * {
      width: 100%;
      height: 100%; // 改为100%高度，确保完全填充
      object-fit: contain;
    }

    // 优化肌肉示意图的显示，确保完全填充容器
    .static-muscle-illustration {
      width: 100% !important;
      height: 100% !important; // 强制100%高度，完全填充容器
      max-width: none !important; // 移除最大宽度限制
      max-height: none !important; // 移除最大高度限制
      margin: 0 !important; // 移除所有外边距
      padding: 0 !important; // 移除所有内边距
      position: relative; // 为 iOS 层级优化做准备
      z-index: inherit; // 继承父容器的高 z-index
      display: flex !important; // 强制flex布局
      align-items: center !important; // 垂直居中
      justify-content: center !important; // 水平居中
      overflow: visible !important; // 确保内容不被裁剪

      svg {
        width: 100% !important;
        height: 100% !important; // 强制SVG高度为100%
        display: block !important; // 移除默认的 inline 间距
        max-width: none !important; // 移除SVG最大宽度限制
        max-height: none !important; // 移除SVG最大高度限制
        object-fit: contain !important; // 保持比例的情况下适应容器大小
        position: relative; // iOS Safari 优化
        z-index: inherit; // 继承父容器的 z-index

        // iOS 特定优化：确保 SVG 不被裁剪
        @supports (-webkit-touch-callout: none) {
          transform: translateZ(0); // 启用硬件加速
          will-change: transform; // 优化渲染性能
        }
      }
    }

    // iOS设备保持80%比例
    @media (max-width: 768px) {
      flex: 0 0 80%;
      height: 100%; // 确保移动端也使用100%高度
      min-height: 240px; // 减少移动端最小高度，避免顶部空间过大
      align-items: center; // 移动端也保持垂直居中对齐，解决iOS显示问题

      .static-muscle-illustration {
        max-width: none; // 移除移动端的最大宽度限制
      }
    }

    // 只有极小屏幕才改为100%宽度
    @media (max-width: 375px) {
      flex: none;
      width: 100%;
      height: 100%; // 确保极小屏幕也使用100%高度
      min-height: 200px; // 极小屏幕下进一步减少高度
    }

    // 暂无数据状态
    .no-muscle-data {
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      color: var(--text-secondary);
      font-size: var(--text-sm);
      background: var(--bg-secondary);
      border-radius: var(--radius-md);
      border: 2px dashed var(--border-color);
      
      p {
        margin: 0;
      }
    }
  }
} 