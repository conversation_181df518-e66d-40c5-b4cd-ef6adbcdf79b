// WorkoutStatsCard 组件样式
.workout-stats-card {
  // 移除背景框样式
  background: transparent;
  border: none;
  padding: 0;
  margin: var(--space-2) 0;
  
  // 可点击状态样式
  &.clickable {
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    border-radius: 8px;
    
    &:hover {
      background: var(--bg-hover);
      transform: translateY(-1px);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
    
    &:active {
      transform: translateY(0);
      background: var(--bg-active);
    }
    
    &:focus {
      outline: 2px solid var(--accent-500);
      outline-offset: 2px;
    }
  }
  
  .workout-stats-summary {
    display: flex;
    align-items: stretch; // 确保Divider与内容高度一致
    justify-content: space-between; // 均匀分布
    width: 100%; // 确保占满页面宽度
    gap: var(--space-2);
    padding: var(--space-2) 0;
    background: transparent;
    border: none;

    .workout-stat {
      flex: 1; // 均匀分布
      text-align: center; // 居中对齐
      display: flex;
      flex-direction: column;
      justify-content: center;
      // 精确计算文本内容高度
      // stat-label: 12px * 1.2 = 14.4px
      // margin-bottom: 4px
      // stat-value: 18px * 1.2 = 21.6px
      // 总高度: 14.4 + 4 + 21.6 = 40px
      height: 40px; // 精确高度
      position: relative;

      .stat-label {
        display: block;
        font-size: var(--text-xs); // 12px
        color: #9ca3af; // 灰色字体
        margin-bottom: var(--space-1); // 4px
        text-transform: uppercase;
        letter-spacing: 0.05em;
        font-weight: var(--font-medium);
        line-height: 1.2; // 12px * 1.2 = 14.4px
      }

      .stat-value {
        display: block;
        font-size: var(--text-lg); // 18px
        font-weight: var(--font-bold);
        color: var(--accent-500);
        line-height: 1.2; // 18px * 1.2 = 21.6px
        font-family: var(--font-mono);
      }
    }

    // HeroUI Divider样式修复 - 精确匹配文本内容高度
    .stats-divider {
      height: 40px; // 与workout-stat的精确高度保持一致
      width: 1px;
      margin: 0 var(--space-1-5);
      align-self: stretch; // 确保垂直拉伸填满容器
      
      // 强制显示HeroUI Divider
      &.heroui-divider.vertical {
        width: 1px !important;
        height: 100% !important; // 填满父容器高度
        background: var(--border-color, #d1d5db) !important;
        opacity: 1 !important;
        border: none;
        display: flex;
        align-items: stretch;
        
        // 移除伪元素，使用简单的背景色实现
        &::before,
        &::after {
          display: none;
        }
        
        // 直接设置背景和边框确保可见性
        border-left: 1px solid var(--border-color, #d1d5db);
        background: var(--border-color, #d1d5db);
      }
      
      // 前导分割线（第一个）特殊处理
      &.leading-divider {
        margin-left: 0;
        margin-right: var(--space-1-5);
      }
    }
  }

  // 紧凑模式
  &.compact {
    .workout-stats-summary {
      padding: var(--space-3) 0; // 增加垂直内边距
      
      .workout-stat {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        height: auto;
        
        .stat-label {
          font-size: var(--text-xs) !important; // 减小标题字体，使用!important确保生效
          margin-bottom: var(--space-2) !important; // 增加间隙
          color: var(--text-secondary);
        }
        
        .stat-value {
          font-size: var(--text-xl) !important; // 放大数据字体
          font-weight: var(--font-bold);
          color: var(--text-primary);
        }
      }
    }
  }
}

// 响应式设计 - 移动端优化
@media (max-width: 768px) {
  .workout-stats-card {
    padding: 0; // 移除外层padding
    margin: var(--space-1-5) 0; // 移动端减少外层margin
    
    .workout-stats-summary {
      padding: var(--space-1-5) 0; // 移动端减少内边距
      gap: var(--space-1); // 移动端紧凑间距
      
      .workout-stat {
        // 移动端精确计算: stat-label (12px * 1.2) + margin (3px) + stat-value (16px * 1.2) 
        // = 14.4 + 3 + 19.2 = 36.6px, 约37px
        height: 37px; // 移动端固定高度
        
        .stat-value {
          font-size: var(--text-base); // 16px移动端
          line-height: 1.2; // 16px * 1.2 = 19.2px
        }
        
        .stat-label {
          font-size: var(--text-xs); // 12px保持不变
          margin-bottom: var(--space-0-5); // 3px减少间距
          line-height: 1.2; // 12px * 1.2 = 14.4px
        }
      }
      
      .stats-divider {
        height: 37px; // 与移动端workout-stat精确高度保持一致
        margin: 0 var(--space-1); // 移动端更紧凑的间距
        
        &.heroui-divider.vertical {
          height: 100% !important; // 确保填满移动端容器高度
        }
        
        &.leading-divider {
          margin-left: 0;
          margin-right: var(--space-1);
        }
      }
    }
    
    // 移动端紧凑模式进一步优化
    &.compact {
      padding: 0; // 移除外层padding
      margin: var(--space-1) 0; // 移动端紧凑模式最小margin
      
      .workout-stats-summary {
        padding: var(--space-1) 0;
        gap: var(--space-0-5);
        
        .workout-stat {
          height: 32px; // 移动端紧凑模式更小
          
          .stat-label {
            font-size: var(--text-2xs);
            margin-bottom: var(--space-0-5);
          }
          
          .stat-value {
            font-size: var(--text-sm);
          }
        }
        
        .stats-divider {
          height: 32px;
          margin: 0 var(--space-0-5);
          
          &.leading-divider {
            margin-left: 0;
            margin-right: var(--space-0-5);
          }
        }
      }
    }
  }
}

// 高对比度模式支持
@media (prefers-contrast: high) {
  .workout-stats-card {
    .workout-stats-summary {
      .stats-divider {
        &.heroui-divider.vertical {
          border-left-width: 2px !important;
          background: var(--text-primary) !important;
        }
      }
    }
  }
}

// 暗色主题适配
.theme-dark {
  .workout-stats-card {
    .workout-stats-summary {
      .workout-stat {
        .stat-label {
          color: #6b7280; // 暗色主题下的灰色
        }
      }
      
      .stats-divider {
        &.heroui-divider.vertical {
          border-left-color: var(--border-dark, #374151) !important;
          background: var(--border-dark, #374151) !important;
        }
      }
    }
  }
} 