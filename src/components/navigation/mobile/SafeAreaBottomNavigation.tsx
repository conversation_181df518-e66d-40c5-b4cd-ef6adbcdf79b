/**
 * Safe Area适配的底部导航组件
 * 解决iOS Capacitor应用中Safe Area初始化延迟问题
 */

import React, { useEffect, useState } from 'react';
import { useSafeAreaReady } from '../../../hooks/useSafeAreaReady';
import { Capacitor } from '@capacitor/core';

interface SafeAreaBottomNavigationProps {
  children: React.ReactNode;
  className?: string;
  baseHeight?: number; // 基础高度，默认60px
}

/**
 * Safe Area适配的底部导航容器
 */
export const SafeAreaBottomNavigation: React.FC<SafeAreaBottomNavigationProps> = ({
  children,
  className = '',
  baseHeight = 60
}) => {
  const { isReady, insets, initializationTime, checkCount } = useSafeAreaReady();
  const [dynamicStyle, setDynamicStyle] = useState<React.CSSProperties>({});
  
  const isIOSDevice = Capacitor.getPlatform() === 'ios';
  const isNative = Capacitor.isNativePlatform();

  useEffect(() => {
    if (!isIOSDevice || !isNative) {
      // 非iOS设备使用标准高度
      setDynamicStyle({
        height: `${baseHeight}px`,
        paddingBottom: '0px'
      });
      return;
    }

    if (isReady) {
      // Safe Area已就绪，使用实际值
      const totalHeight = baseHeight + insets.bottom;
      
      setDynamicStyle({
        height: `${totalHeight}px`,
        paddingBottom: '0px',
        '--safe-area-bottom': `${insets.bottom}px`
      } as React.CSSProperties);

      console.log('🎯 底部导航Safe Area适配完成:', {
        baseHeight,
        safeAreaBottom: insets.bottom,
        totalHeight,
        initializationTime,
        checkCount
      });
    } else {
      // Safe Area未就绪，使用预估值
      const estimatedSafeArea = 34; // iPhone典型值
      const totalHeight = baseHeight + estimatedSafeArea;
      
      setDynamicStyle({
        height: `${totalHeight}px`,
        paddingBottom: '0px',
        '--safe-area-bottom': `${estimatedSafeArea}px`
      } as React.CSSProperties);

      console.log('⏳ 底部导航使用预估Safe Area值:', {
        baseHeight,
        estimatedSafeArea,
        totalHeight,
        checkCount
      });
    }
  }, [isReady, insets, baseHeight, isIOSDevice, isNative, initializationTime, checkCount]);

  // 调试信息（仅开发环境）
  useEffect(() => {
    if (process.env.NODE_ENV === 'development' && isIOSDevice && isNative) {
      console.log('🔍 SafeAreaBottomNavigation状态:', {
        isReady,
        insets,
        dynamicStyle,
        initializationTime,
        checkCount
      });
    }
  }, [isReady, insets, dynamicStyle, initializationTime, checkCount, isIOSDevice, isNative]);

  return (
    <div
      className={`bottom-navigation ${className}`}
      style={dynamicStyle}
      data-safe-area-ready={isReady}
      data-safe-area-bottom={insets.bottom}
    >
      {children}
      
      {/* Safe Area指示器（仅开发环境） */}
      {process.env.NODE_ENV === 'development' && isIOSDevice && isNative && (
        <div
          style={{
            position: 'absolute',
            bottom: 0,
            left: 0,
            right: 0,
            height: `${insets.bottom}px`,
            background: isReady 
              ? 'rgba(0, 255, 0, 0.2)' // 绿色：Safe Area已就绪
              : 'rgba(255, 165, 0, 0.2)', // 橙色：使用预估值
            borderTop: '1px dashed rgba(255, 255, 255, 0.5)',
            fontSize: '10px',
            color: 'white',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            zIndex: 10000,
            pointerEvents: 'none'
          }}
        >
          Safe Area: {insets.bottom}px {!isReady && '(预估)'}
        </div>
      )}
    </div>
  );
};

/**
 * 高阶组件：为现有底部导航添加Safe Area适配
 */
export const withSafeAreaAdaptation = <P extends object>(
  WrappedComponent: React.ComponentType<P>,
  baseHeight?: number
) => {
  return React.forwardRef<any, P>((props, ref) => (
    <SafeAreaBottomNavigation baseHeight={baseHeight}>
      <WrappedComponent {...(props as P)} ref={ref} />
    </SafeAreaBottomNavigation>
  ));
};

/**
 * Hook：获取底部导航的动态高度
 */
export const useBottomNavigationHeight = (baseHeight: number = 60): number => {
  const { isReady, insets } = useSafeAreaReady();
  const isIOSDevice = Capacitor.getPlatform() === 'ios';
  const isNative = Capacitor.isNativePlatform();

  if (!isIOSDevice || !isNative) {
    return baseHeight;
  }

  const safeAreaBottom = isReady ? insets.bottom : 34; // 使用预估值
  return baseHeight + safeAreaBottom;
};

/**
 * CSS变量注入Hook：将Safe Area值注入到CSS变量中
 */
export const useSafeAreaCSSVariables = (): void => {
  const { isReady, insets } = useSafeAreaReady();
  const isIOSDevice = Capacitor.getPlatform() === 'ios';
  const isNative = Capacitor.isNativePlatform();

  useEffect(() => {
    if (!isIOSDevice || !isNative) return;

    const root = document.documentElement;
    
    if (isReady) {
      // 注入实际Safe Area值
      root.style.setProperty('--safe-area-inset-top', `${insets.top}px`);
      root.style.setProperty('--safe-area-inset-bottom', `${insets.bottom}px`);
      root.style.setProperty('--safe-area-inset-left', `${insets.left}px`);
      root.style.setProperty('--safe-area-inset-right', `${insets.right}px`);
      
      console.log('✅ Safe Area CSS变量已更新:', insets);
    } else {
      // 注入预估值
      const fallbackInsets = { top: 47, bottom: 34, left: 0, right: 0 };
      root.style.setProperty('--safe-area-inset-top', `${fallbackInsets.top}px`);
      root.style.setProperty('--safe-area-inset-bottom', `${fallbackInsets.bottom}px`);
      root.style.setProperty('--safe-area-inset-left', `${fallbackInsets.left}px`);
      root.style.setProperty('--safe-area-inset-right', `${fallbackInsets.right}px`);
      
      console.log('⏳ Safe Area CSS变量使用预估值:', fallbackInsets);
    }
  }, [isReady, insets, isIOSDevice, isNative]);
};
