/**
 * DropdownMenu 通用下拉菜单组件
 * 
 * @fileoverview iOS风格的下拉菜单组件，支持触摸友好的操作体验
 * <AUTHOR> Team
 * @since 1.0.0
 */

import React, { useState, useRef, useEffect, useCallback } from 'react';
import './DropdownMenu.scss';

/**
 * 菜单项数据接口
 */
export interface DropdownMenuItem {
  key: string;
  label: string;
  icon?: string;
  disabled?: boolean;
  onClick: () => void;
}

/**
 * DropdownMenu 组件属性
 */
export interface DropdownMenuProps {
  /** 触发器元素 */
  trigger: React.ReactNode;
  /** 菜单项列表 */
  items: DropdownMenuItem[];
  /** 菜单位置 */
  placement?: 'bottom-start' | 'bottom-end' | 'top-start' | 'top-end';
  /** 额外的CSS类名 */
  className?: string;
  /** 是否禁用 */
  disabled?: boolean;
}

/**
 * iOS风格下拉菜单组件
 * 提供触摸友好的操作体验和流畅的动画效果
 */
export const DropdownMenu: React.FC<DropdownMenuProps> = ({
  trigger,
  items,
  placement = 'bottom-end',
  className = '',
  disabled = false
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const triggerRef = useRef<HTMLDivElement>(null);

  // iOS触摸反馈
  const provideiOSTouchFeedback = useCallback(() => {
    try {
      if (window.navigator.vibrate) {
        window.navigator.vibrate(10);
      }
    } catch (error) {
      // 静默忽略错误
    }
  }, []);

  // 切换菜单显示状态
  const toggleMenu = useCallback(() => {
    if (disabled) return;
    
    provideiOSTouchFeedback();
    setIsOpen(prev => !prev);
  }, [disabled, provideiOSTouchFeedback]);

  // 关闭菜单
  const closeMenu = useCallback(() => {
    setIsOpen(false);
  }, []);

  // 处理菜单项点击
  const handleItemClick = useCallback((item: DropdownMenuItem) => {
    if (item.disabled) return;
    
    provideiOSTouchFeedback();
    item.onClick();
    closeMenu();
  }, [provideiOSTouchFeedback, closeMenu]);

  // 点击外部关闭菜单
  useEffect(() => {
    const handleClickOutside = (event: Event) => {
      const target = event.target as Node;
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(target) &&
        triggerRef.current &&
        !triggerRef.current.contains(target)
      ) {
        closeMenu();
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
      document.addEventListener('touchstart', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      document.removeEventListener('touchstart', handleClickOutside);
    };
  }, [isOpen, closeMenu]);

  // ESC键关闭菜单
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isOpen) {
        closeMenu();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleKeyDown);
    }

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [isOpen, closeMenu]);

  return (
    <div className={`dropdown-menu-container ${className}`}>
      {/* 触发器 */}
      <div
        ref={triggerRef}
        className={`dropdown-trigger ${disabled ? 'disabled' : ''} ${isOpen ? 'active' : ''}`}
        onClick={toggleMenu}
        role="button"
        aria-expanded={isOpen}
        aria-haspopup="menu"
        tabIndex={disabled ? -1 : 0}
      >
        {trigger}
      </div>

      {/* 下拉菜单 */}
      {isOpen && (
        <div
          ref={dropdownRef}
          className={`dropdown-menu ${placement} ${isOpen ? 'open' : ''}`}
          role="menu"
          aria-hidden={!isOpen}
        >
          <div className="dropdown-menu-content">
            {items.map((item) => (
              <button
                key={item.key}
                className={`dropdown-menu-item ${item.disabled ? 'disabled' : ''}`}
                onClick={() => handleItemClick(item)}
                disabled={item.disabled}
                role="menuitem"
                tabIndex={item.disabled ? -1 : 0}
              >
                {item.icon && (
                  <span className="menu-item-icon" aria-hidden="true">
                    {item.icon}
                  </span>
                )}
                <span className="menu-item-label">
                  {item.label}
                </span>
              </button>
            ))}
          </div>
        </div>
      )}

      {/* 背景遮罩 */}
      {isOpen && (
        <div 
          className="dropdown-backdrop"
          onClick={closeMenu}
          aria-hidden="true"
        />
      )}
    </div>
  );
};

export default DropdownMenu;