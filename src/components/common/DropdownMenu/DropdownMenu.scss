/**
 * DropdownMenu 组件样式
 * iOS风格的下拉菜单设计
 */

.dropdown-menu-container {
  position: relative;
  display: inline-block;

  .dropdown-trigger {
    cursor: pointer;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    border-radius: 6px;
    min-width: 44px; // iOS最小触摸目标
    min-height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
    
    &:hover:not(.disabled) {
      background: var(--bg-hover);
      transform: scale(1.02);
    }

    &:active:not(.disabled) {
      transform: scale(0.98);
    }

    &.active {
      background: var(--bg-active);
    }

    &.disabled {
      opacity: 0.5;
      cursor: not-allowed;
      pointer-events: none;
    }
  }

  .dropdown-menu {
    position: absolute;
    z-index: 1000;
    background: var(--bg-surface);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    box-shadow: 
      0 10px 38px -10px rgba(22, 23, 24, 0.35),
      0 10px 20px -15px rgba(22, 23, 24, 0.2);
    backdrop-filter: blur(8px);
    overflow: hidden;
    min-width: 180px;
    max-width: 280px;
    
    // 动画效果
    transform: scale(0.95) translateY(-10px);
    opacity: 0;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    
    &.open {
      transform: scale(1) translateY(0);
      opacity: 1;
    }

    // 位置定位
    &.bottom-start {
      top: calc(100% + 8px);
      left: 0;
    }

    &.bottom-end {
      top: calc(100% + 8px);
      right: 0;
    }

    &.top-start {
      bottom: calc(100% + 8px);
      left: 0;
    }

    &.top-end {
      bottom: calc(100% + 8px);
      right: 0;
    }

    .dropdown-menu-content {
      padding: var(--space-2);
    }

    .dropdown-menu-item {
      display: flex;
      align-items: center;
      gap: var(--space-3);
      width: 100%;
      padding: var(--space-3) var(--space-4);
      background: transparent;
      border: none;
      border-radius: 8px;
      color: var(--text-primary);
      font-size: var(--text-base);
      font-weight: var(--font-medium);
      text-align: left;
      cursor: pointer;
      transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
      min-height: 44px; // iOS最小触摸目标

      .menu-item-icon {
        font-size: 1.125rem;
        flex-shrink: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 20px;
        height: 20px;
      }

      .menu-item-label {
        flex: 1;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      &:hover:not(.disabled) {
        background: var(--bg-hover);
        color: var(--text-primary);
        transform: translateX(2px);
      }

      &:active:not(.disabled) {
        background: var(--bg-active);
        transform: scale(0.98);
      }

      &.disabled {
        opacity: 0.5;
        cursor: not-allowed;
        pointer-events: none;
      }

      // 分割线（如果需要）
      &:not(:last-child) {
        margin-bottom: var(--space-1);
      }
    }
  }

  .dropdown-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 999;
    background: transparent;
  }
}

// iOS专用优化
@media only screen and (max-width: 768px) {
  .dropdown-menu-container {
    .dropdown-trigger {
      min-width: 40px;
      min-height: 40px;
    }

    .dropdown-menu {
      min-width: 160px;
      border-radius: 10px;
      
      .dropdown-menu-item {
        padding: var(--space-2-5) var(--space-3);
        font-size: var(--text-sm);
        min-height: 40px;

        .menu-item-icon {
          font-size: 1rem;
          width: 18px;
          height: 18px;
        }
      }
    }
  }
}

// 横屏模式优化
@media only screen and (max-height: 500px) and (orientation: landscape) {
  .dropdown-menu-container {
    .dropdown-menu {
      max-height: 300px;
      overflow-y: auto;
      -webkit-overflow-scrolling: touch;

      .dropdown-menu-item {
        min-height: 36px;
        padding: var(--space-2) var(--space-3);
      }
    }
  }
}

// 高对比度支持
@media (prefers-contrast: high) {
  .dropdown-menu-container {
    .dropdown-menu {
      border-width: 2px;
      border-color: var(--border-high-contrast);

      .dropdown-menu-item {
        border: 1px solid transparent;
        
        &:hover:not(.disabled) {
          border-color: var(--accent-500);
        }
      }
    }
  }
}

// 深色主题支持
@media (prefers-color-scheme: dark) {
  .dropdown-menu-container {
    .dropdown-menu {
      background: var(--bg-surface-dark);
      border-color: var(--border-color-dark);
      box-shadow: 
        0 10px 38px -10px rgba(0, 0, 0, 0.5),
        0 10px 20px -15px rgba(0, 0, 0, 0.4);

      .dropdown-menu-item {
        color: var(--text-primary-dark);
        
        &:hover:not(.disabled) {
          background: var(--bg-hover-dark);
        }

        &:active:not(.disabled) {
          background: var(--bg-active-dark);
        }
      }
    }
  }
}

// 减少动画偏好支持
@media (prefers-reduced-motion: reduce) {
  .dropdown-menu-container {
    .dropdown-trigger {
      transition: none;
      
      &:hover:not(.disabled) {
        transform: none;
      }

      &:active:not(.disabled) {
        transform: none;
      }
    }

    .dropdown-menu {
      transition: none;
      transform: none !important;
      
      .dropdown-menu-item {
        transition: none;
        
        &:hover:not(.disabled) {
          transform: none;
        }

        &:active:not(.disabled) {
          transform: none;
        }
      }
    }
  }
}