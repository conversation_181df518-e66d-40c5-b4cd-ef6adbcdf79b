/**
 * PostActions 组件样式
 * 基于Feed页面的post-actions样式，确保完全一致
 */

.post-actions {
  display: flex;
  justify-content: space-around;
  padding-top: var(--space-4);
  // 移除顶部边框线
  border-top: none;
  
  .action-btn {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    padding: var(--space-2) var(--space-3);
    background: transparent;
    border: none;
    border-radius: var(--radius-md);
    color: var(--text-secondary);
    font-size: var(--text-sm);
    font-weight: var(--font-medium);
    cursor: pointer;
    transition: all var(--transition-normal) var(--ease-in-out);
    min-height: 44px; // iOS最小触摸目标
    min-width: 44px;
    
    svg {
      width: 1.125rem;
      height: 1.125rem;
      stroke-width: 2;
      flex-shrink: 0;
    }
    
    span {
      white-space: nowrap;
    }
    
    // 默认悬停效果
    &:hover:not(:disabled) {
      background: var(--primary-600);
      color: var(--text-primary);
      transform: translateY(-1px);
    }

    // iOS触摸反馈
    &:active:not(:disabled) {
      transform: scale(0.98);
    }

    // 禁用状态
    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
      pointer-events: none;
    }

    // 加载状态
    &.loading {
      opacity: 0.7;
      
      svg {
        animation: spin 1s linear infinite;
      }
    }
    
    // 点赞按钮特殊样式
    &.like-btn {
      &.liked {
        color: var(--error-500);
        
        svg {
          fill: currentColor;
        }
      }
      
      &:hover:not(:disabled) {
        color: var(--error-500);
      }
    }
    
    // 评论按钮特殊样式
    &.comment-btn {
      &:hover:not(:disabled) {
        color: var(--accent-500);
      }
    }
    
    // 分享按钮特殊样式
    &.share-btn {
      &:hover:not(:disabled) {
        color: var(--success-500);
      }
    }
  }
}

// 加载动画
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

// iOS专用优化
@media only screen and (max-width: 768px) {
  .post-actions {
    padding-top: var(--space-3);
    gap: var(--space-1);

    .action-btn {
      padding: var(--space-1-5) var(--space-2);
      gap: var(--space-1-5);
      font-size: var(--text-xs);
      min-height: 40px;
      min-width: 40px;

      svg {
        width: 1rem;
        height: 1rem;
      }
    }
  }
}

// 横屏模式优化
@media only screen and (max-height: 500px) and (orientation: landscape) {
  .post-actions {
    .action-btn {
      padding: var(--space-1) var(--space-2);
      min-height: 36px;
      min-width: 36px;
    }
  }
}

// 高对比度支持
@media (prefers-contrast: high) {
  .post-actions {
    .action-btn {
      border: 1px solid var(--border-color);
      
      &:hover:not(:disabled) {
        border-color: currentColor;
      }

      &.like-btn.liked {
        background: var(--error-50);
        border-color: var(--error-500);
      }
    }
  }
}

// 深色主题支持
@media (prefers-color-scheme: dark) {
  .post-actions {
    .action-btn {
      &:hover:not(:disabled) {
        background: var(--primary-700);
      }

      &.like-btn {
        &.liked {
          color: var(--error-400);
        }
        
        &:hover:not(:disabled) {
          color: var(--error-400);
        }
      }
      
      &.comment-btn:hover:not(:disabled) {
        color: var(--accent-400);
      }
      
      &.share-btn:hover:not(:disabled) {
        color: var(--success-400);
      }
    }
  }
}

// 减少动画偏好支持
@media (prefers-reduced-motion: reduce) {
  .post-actions {
    .action-btn {
      transition: none;
      
      &:hover:not(:disabled) {
        transform: none;
      }

      &:active:not(:disabled) {
        transform: none;
      }

      &.loading svg {
        animation: none;
      }
    }
  }
}