/**
 * PostActions 通用操作按钮组件
 * 
 * @fileoverview 统一的帖子操作按钮组件，用于Feed页面和WorkoutDetail页面
 * <AUTHOR> Team
 * @since 1.0.0
 */

import React, { useCallback, useState } from 'react';
import './PostActions.scss';

/**
 * 社交统计数据接口
 */
export interface PostStats {
  likes: number;
  comments: number;
  views: number;
  isLiked: boolean;
}

/**
 * PostActions 组件属性
 */
export interface PostActionsProps {
  /** 社交统计数据 */
  stats: PostStats;
  /** 点赞操作回调 */
  onLike: () => Promise<void>;
  /** 评论操作回调 */
  onComment: () => Promise<void>;
  /** 分享操作回调 */
  onShare: () => Promise<void>;
  /** 额外的CSS类名 */
  className?: string;
  /** 是否显示加载状态 */
  loading?: boolean;
}

/**
 * 通用操作按钮组件
 * 提供统一的点赞、评论、分享按钮，支持乐观更新和错误处理
 */
export const PostActions: React.FC<PostActionsProps> = ({
  stats,
  onLike,
  onComment,
  onShare,
  className = '',
  loading = false
}) => {
  const [isLiking, setIsLiking] = useState(false);
  const [isCommenting, setIsCommenting] = useState(false);
  const [isSharing, setIsSharing] = useState(false);

  // iOS触摸反馈
  const provideiOSTouchFeedback = useCallback(() => {
    try {
      if (window.navigator.vibrate) {
        window.navigator.vibrate(10);
      }
    } catch (error) {
      // 静默忽略错误
    }
  }, []);

  // 格式化数字显示（k, w）
  const formatNumber = (num: number): string => {
    if (num >= 10000) {
      return `${(num / 10000).toFixed(1)}w`;
    } else if (num >= 1000) {
      return `${(num / 1000).toFixed(1)}k`;
    }
    return num.toString();
  };

  // 处理点赞操作
  const handleLike = useCallback(async () => {
    if (isLiking || loading) return;

    provideiOSTouchFeedback();
    setIsLiking(true);

    try {
      await onLike();
    } catch (error) {
      console.error('点赞操作失败:', error);
    } finally {
      setIsLiking(false);
    }
  }, [onLike, isLiking, loading, provideiOSTouchFeedback]);

  // 处理评论操作
  const handleComment = useCallback(async () => {
    if (isCommenting || loading) return;

    provideiOSTouchFeedback();
    setIsCommenting(true);

    try {
      await onComment();
    } catch (error) {
      console.error('评论操作失败:', error);
    } finally {
      setIsCommenting(false);
    }
  }, [onComment, isCommenting, loading, provideiOSTouchFeedback]);

  // 处理分享操作
  const handleShare = useCallback(async () => {
    if (isSharing || loading) return;

    provideiOSTouchFeedback();
    setIsSharing(true);

    try {
      await onShare();
    } catch (error) {
      console.error('分享操作失败:', error);
    } finally {
      setIsSharing(false);
    }
  }, [onShare, isSharing, loading, provideiOSTouchFeedback]);

  return (
    <div className={`post-actions ${className}`}>
      {/* 点赞按钮 */}
      <button 
        className={`action-btn like-btn ${stats.isLiked ? 'liked' : ''} ${isLiking ? 'loading' : ''}`}
        onClick={handleLike}
        disabled={isLiking || loading}
        aria-label={stats.isLiked ? '取消点赞' : '点赞'}
      >
        <svg viewBox="0 0 24 24" fill={stats.isLiked ? "currentColor" : "none"} stroke="currentColor">
          <path d="M20.84 4.61C20.3292 4.099 19.7228 3.69364 19.0554 3.41708C18.3879 3.14052 17.6725 2.99817 16.95 2.99817C16.2275 2.99817 15.5121 3.14052 14.8446 3.41708C14.1772 3.69364 13.5708 4.099 13.06 4.61L12 5.67L10.94 4.61C9.9083 3.57831 8.50903 2.99871 7.05 2.99871C5.59096 2.99871 4.19169 3.57831 3.16 4.61C2.1283 5.64169 1.54871 7.04096 1.54871 8.5C1.54871 9.95904 2.1283 11.3583 3.16 12.39L12 21.23L20.84 12.39C21.351 11.8792 21.7563 11.2728 22.0329 10.6053C22.3095 9.93789 22.4518 9.22248 22.4518 8.5C22.4518 7.77752 22.3095 7.06211 22.0329 6.39468C21.7563 5.72725 21.351 5.12087 20.84 4.61Z"/>
        </svg>
        <span>{formatNumber(stats.likes)}</span>
      </button>
      
      {/* 评论按钮 */}
      <button 
        className={`action-btn comment-btn ${isCommenting ? 'loading' : ''}`}
        onClick={handleComment}
        disabled={isCommenting || loading}
        aria-label="评论"
      >
        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
          <path d="M21 15C21 15.5304 20.7893 16.0391 20.4142 16.4142C20.0391 16.7893 19.5304 17 19 17H7L3 21V5C3 4.46957 3.21071 3.96086 3.58579 3.58579C3.96086 3.21071 4.46957 3 5 3H19C19.5304 3 20.0391 3.21071 20.4142 3.58579C20.7893 3.96086 21 4.46957 21 5V15Z"/>
        </svg>
        <span>{formatNumber(stats.comments)}</span>
      </button>
      
      {/* 分享按钮 */}
      <button 
        className={`action-btn share-btn ${isSharing ? 'loading' : ''}`}
        onClick={handleShare}
        disabled={isSharing || loading}
        aria-label="分享"
      >
        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
          <path d="M4 12V20A2 2 0 0 0 6 22H18A2 2 0 0 0 20 20V12"/>
          <polyline points="16,6 12,2 8,6"/>
          <line x1="12" y1="2" x2="12" y2="15"/>
        </svg>
        <span>{formatNumber(stats.views)}</span>
      </button>
    </div>
  );
};

export default PostActions;