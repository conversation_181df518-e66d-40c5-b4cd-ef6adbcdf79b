/**
 * PostUserSection 组件样式
 * 基于Feed页面的post-user-section样式，确保完全一致
 */

.post-user-section {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  flex: 1;

  // HeroUI Avatar样式调整
  .user-avatar {
    flex-shrink: 0;
    
    // 确保HeroUI Avatar正确显示
    &.heroui-avatar {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      overflow: hidden;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      
      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }
  }

  .user-info {
    flex: 1;
    min-width: 0;

    .user-name-row {
      margin-bottom: var(--space-1);

      .user-name {
        display: inline-flex;
        align-items: center;
        gap: var(--space-1);
        font-size: var(--text-base);
        font-weight: var(--font-semibold);
        color: var(--text-primary);

        .verified-icon {
          width: 1rem;
          height: 1rem;
          color: var(--accent-500);
          flex-shrink: 0;
        }
      }
    }

    .post-timestamp {
      font-size: var(--text-sm);
      color: var(--text-tertiary);
      line-height: 1.2;
    }
  }

  // 关注按钮区域
  .user-actions {
    flex-shrink: 0;

    .follow-btn {
      padding: var(--space-1-5) var(--space-3); // 调整内边距
      min-height: auto; // 覆盖HeroUI默认高度
      height: auto;
      
      // 确保HeroUI Button bordered样式正确显示
      &.heroui-button {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        border-radius: 6px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s ease;
        
        // 未关注状态 - 蓝色边框
        &.not-following {
          border: 2px solid var(--accent-500, #3b82f6) !important;
          background: transparent !important;
          color: var(--accent-500, #3b82f6) !important;
          
          &:hover {
            background: var(--bg-hover, rgba(59, 130, 246, 0.1)) !important;
            border-color: var(--accent-600, #2563eb) !important;
          }

          // iOS触摸反馈
          &:active {
            transform: scale(0.98);
          }
        }
        
        // 已关注状态 - 灰色边框
        &.following {
          border: 2px solid var(--primary-400, #94a3b8) !important;
          background: transparent !important;
          color: var(--primary-600, #475569) !important;
          
          &:hover {
            background: var(--bg-hover, rgba(148, 163, 184, 0.1)) !important;
            border-color: var(--primary-500, #64748b) !important;
          }

          // iOS触摸反馈
          &:active {
            transform: scale(0.98);
          }
        }
      }
    }
  }
}

// iOS专用优化
@media only screen and (max-width: 768px) {
  .post-user-section {
    gap: var(--space-2-5);

    .user-avatar.heroui-avatar {
      width: 36px;
      height: 36px;
    }

    .user-info {
      .user-name-row .user-name {
        font-size: var(--text-sm);
      }

      .post-timestamp {
        font-size: var(--text-xs);
      }
    }

    .user-actions .follow-btn {
      padding: var(--space-1) var(--space-2);
      font-size: var(--text-xs);
    }
  }
}

// 高对比度和无障碍支持
@media (prefers-contrast: high) {
  .post-user-section {
    .user-info .user-name-row .user-name .verified-icon {
      color: var(--accent-600);
    }

    .user-actions .follow-btn.heroui-button {
      &.not-following {
        border-width: 3px;
      }
      
      &.following {
        border-width: 3px;
      }
    }
  }
}

// 深色主题支持
@media (prefers-color-scheme: dark) {
  .post-user-section {
    .user-actions .follow-btn.heroui-button {
      &.not-following {
        border-color: var(--accent-400, #60a5fa) !important;
        color: var(--accent-400, #60a5fa) !important;
        
        &:hover {
          background: var(--bg-hover, rgba(96, 165, 250, 0.1)) !important;
        }
      }
      
      &.following {
        border-color: var(--primary-300, #cbd5e1) !important;
        color: var(--primary-300, #cbd5e1) !important;
        
        &:hover {
          background: var(--bg-hover, rgba(203, 213, 225, 0.1)) !important;
        }
      }
    }
  }
}