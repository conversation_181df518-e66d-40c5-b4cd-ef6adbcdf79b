/**
 * PostUserSection 通用用户信息组件
 * 
 * @fileoverview 统一的帖子用户信息展示组件，用于Feed页面和WorkoutDetail页面
 * <AUTHOR> Team
 * @since 1.0.0
 */

import React, { useCallback } from 'react';
import { Avatar, Button } from '@heroui/react';
import './PostUserSection.scss';

/**
 * 用户信息数据接口
 */
export interface PostUser {
  id: string;
  name: string;
  avatar: string;
  isVerified: boolean;
  isFollowing?: boolean;
}

/**
 * PostUserSection 组件属性
 */
export interface PostUserSectionProps {
  /** 用户信息 */
  user: PostUser;
  /** 发布时间戳 */
  timestamp: string;
  /** 是否显示关注按钮 */
  showFollowButton?: boolean;
  /** 关注状态变更回调 */
  onFollow?: (userId: string, isCurrentlyFollowing: boolean) => Promise<void>;
  /** 额外的CSS类名 */
  className?: string;
}

/**
 * 通用用户信息展示组件
 * 提供统一的用户头像、姓名、认证状态、时间戳和关注按钮展示
 */
export const PostUserSection: React.FC<PostUserSectionProps> = ({
  user,
  timestamp,
  showFollowButton = true,
  onFollow,
  className = ''
}) => {
  // iOS触摸反馈
  const provideiOSTouchFeedback = useCallback(() => {
    try {
      if (window.navigator.vibrate) {
        window.navigator.vibrate(10);
      }
    } catch (error) {
      // 静默忽略错误
    }
  }, []);

  // 处理关注操作
  const handleFollow = useCallback(async () => {
    if (!onFollow) return;
    
    provideiOSTouchFeedback();
    try {
      await onFollow(user.id, user.isFollowing || false);
    } catch (error) {
      console.error('关注操作失败:', error);
    }
  }, [onFollow, user.id, user.isFollowing, provideiOSTouchFeedback]);

  // 格式化时间显示
  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);
    
    if (diffInSeconds < 60) {
      return '刚刚';
    } else if (diffInSeconds < 3600) {
      return `${Math.floor(diffInSeconds / 60)}分钟前`;
    } else if (diffInSeconds < 86400) {
      return `${Math.floor(diffInSeconds / 3600)}小时前`;
    } else {
      return `${Math.floor(diffInSeconds / 86400)}天前`;
    }
  };

  return (
    <div className={`post-user-section ${className}`}>
      {/* 用户头像 */}
      <Avatar
        src={user.avatar}
        alt={`${user.name}的头像`}
        size="md"
        className="user-avatar heroui-avatar"
      />

      {/* 用户信息 */}
      <div className="user-info">
        <div className="user-name-row">
          <span className="user-name">
            {user.name}
            {user.isVerified && (
              <svg className="verified-icon" viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 2L13.09 5.26L16 6L13.09 6.74L12 10L10.91 6.74L8 6L10.91 5.26L12 2Z"/>
                <path d="M12 12L13.09 15.26L16 16L13.09 16.74L12 20L10.91 16.74L8 16L10.91 15.26L12 12Z"/>
              </svg>
            )}
          </span>
        </div>
        <div className="post-timestamp">
          {formatTimestamp(timestamp)}
        </div>
      </div>

      {/* 关注按钮 */}
      {showFollowButton && !user.isFollowing && (
        <div className="user-actions">
          <Button
            variant="bordered"
            size="sm"
            className={`follow-btn heroui-button ${user.isFollowing ? 'following' : 'not-following'}`}
            aria-label={user.isFollowing ? '已关注' : '关注'}
            onClick={handleFollow}
          >
            {user.isFollowing ? '已关注' : '关注'}
          </Button>
        </div>
      )}
    </div>
  );
};

export default PostUserSection;