/**
 * iOS风格加载指示器
 * 提供原生iOS体验的加载动画
 */

import React from 'react';
import { motion } from 'framer-motion';

interface IOSLoadingIndicatorProps {
  size?: 'small' | 'medium' | 'large';
  color?: string;
  message?: string;
  visible?: boolean;
  overlay?: boolean;
}

/**
 * iOS风格加载指示器组件
 */
export const IOSLoadingIndicator: React.FC<IOSLoadingIndicatorProps> = ({
  size = 'medium',
  color = '#007AFF',
  message,
  visible = true,
  overlay = false
}) => {
  // 简单的iOS设备检测
  const isIOSDevice = /iPad|iPhone|iPod/.test(navigator.userAgent);

  if (!visible) return null;

  const sizeMap = {
    small: 20,
    medium: 32,
    large: 48
  };

  const spinnerSize = sizeMap[size];

  const spinnerVariants = {
    animate: {
      rotate: 360,
      transition: {
        duration: 1,
        repeat: Infinity,
        ease: 'linear'
      }
    }
  };

  const overlayVariants = {
    hidden: { opacity: 0 },
    visible: { 
      opacity: 1,
      transition: { duration: 0.2 }
    }
  };

  const contentVariants = {
    hidden: { 
      opacity: 0,
      scale: 0.8,
      y: 10
    },
    visible: { 
      opacity: 1,
      scale: 1,
      y: 0,
      transition: { 
        duration: 0.3,
        ease: 'easeOut'
      }
    }
  };

  const LoadingSpinner = () => (
    <motion.div
      className="ios-loading-spinner"
      style={{
        width: spinnerSize,
        height: spinnerSize,
        border: `2px solid ${color}20`,
        borderTop: `2px solid ${color}`,
        borderRadius: '50%'
      }}
      variants={spinnerVariants}
      animate="animate"
    />
  );

  const LoadingContent = () => (
    <motion.div
      className="ios-loading-content"
      variants={contentVariants}
      initial="hidden"
      animate="visible"
      style={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        gap: '12px',
        padding: '20px',
        backgroundColor: isIOSDevice ? 'rgba(255, 255, 255, 0.95)' : 'rgba(255, 255, 255, 0.9)',
        backdropFilter: 'blur(10px)',
        borderRadius: isIOSDevice ? '16px' : '12px',
        boxShadow: isIOSDevice 
          ? '0 10px 30px rgba(0, 0, 0, 0.1)' 
          : '0 4px 20px rgba(0, 0, 0, 0.15)',
        border: isIOSDevice ? '1px solid rgba(255, 255, 255, 0.2)' : 'none'
      }}
    >
      <LoadingSpinner />
      {message && (
        <motion.p
          className="ios-loading-message"
          style={{
            margin: 0,
            fontSize: size === 'small' ? '14px' : '16px',
            color: 'var(--text-secondary)',
            textAlign: 'center',
            fontWeight: isIOSDevice ? '500' : '400',
            letterSpacing: isIOSDevice ? '-0.01em' : '0'
          }}
          initial={{ opacity: 0, y: 5 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
        >
          {message}
        </motion.p>
      )}
    </motion.div>
  );

  if (overlay) {
    return (
      <motion.div
        className="ios-loading-overlay"
        variants={overlayVariants}
        initial="hidden"
        animate="visible"
        exit="hidden"
        style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundColor: 'rgba(0, 0, 0, 0.3)',
          backdropFilter: 'blur(2px)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          zIndex: 9999,
          padding: '20px'
        }}
      >
        <LoadingContent />
      </motion.div>
    );
  }

  return <LoadingContent />;
};

/**
 * iOS风格错误提示组件
 */
interface IOSErrorAlertProps {
  title: string;
  message: string;
  visible: boolean;
  onClose: () => void;
  actionText?: string;
  onAction?: () => void;
}

export const IOSErrorAlert: React.FC<IOSErrorAlertProps> = ({
  title,
  message,
  visible,
  onClose,
  actionText = '确定',
  onAction
}) => {
  const isIOSDevice = /iPad|iPhone|iPod/.test(navigator.userAgent);

  const handleAction = () => {
    if (onAction) {
      onAction();
    } else {
      onClose();
    }
  };

  if (!visible) return null;

  return (
    <motion.div
      className="ios-error-overlay"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      style={{
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: 'rgba(0, 0, 0, 0.4)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        zIndex: 10000,
        padding: '20px'
      }}
      onClick={onClose}
    >
      <motion.div
        className="ios-error-alert"
        initial={{ opacity: 0, scale: 0.8, y: 20 }}
        animate={{ opacity: 1, scale: 1, y: 0 }}
        exit={{ opacity: 0, scale: 0.8, y: 20 }}
        transition={{ type: 'spring', damping: 20, stiffness: 300 }}
        style={{
          backgroundColor: isIOSDevice ? 'rgba(255, 255, 255, 0.95)' : '#ffffff',
          backdropFilter: 'blur(20px)',
          borderRadius: isIOSDevice ? '16px' : '12px',
          padding: '24px',
          maxWidth: '320px',
          width: '100%',
          boxShadow: isIOSDevice 
            ? '0 20px 40px rgba(0, 0, 0, 0.15)' 
            : '0 8px 32px rgba(0, 0, 0, 0.2)',
          border: isIOSDevice ? '1px solid rgba(255, 255, 255, 0.3)' : 'none'
        }}
        onClick={(e) => e.stopPropagation()}
      >
        <h3 style={{
          margin: '0 0 12px 0',
          fontSize: '18px',
          fontWeight: isIOSDevice ? '600' : '500',
          color: 'var(--text-primary)',
          textAlign: 'center',
          letterSpacing: isIOSDevice ? '-0.02em' : '0'
        }}>
          {title}
        </h3>
        
        <p style={{
          margin: '0 0 24px 0',
          fontSize: '16px',
          color: 'var(--text-secondary)',
          textAlign: 'center',
          lineHeight: '1.4',
          fontWeight: isIOSDevice ? '400' : '400'
        }}>
          {message}
        </p>
        
        <button
          onClick={handleAction}
          style={{
            width: '100%',
            padding: '12px',
            backgroundColor: 'var(--ios-system-blue)',
            color: '#ffffff',
            border: 'none',
            borderRadius: isIOSDevice ? '12px' : '8px',
            fontSize: '16px',
            fontWeight: isIOSDevice ? '600' : '500',
            cursor: 'pointer',
            transition: 'all 0.2s ease',
            letterSpacing: isIOSDevice ? '-0.01em' : '0'
          }}
          onMouseDown={(e) => {
            e.currentTarget.style.transform = 'scale(0.98)';
          }}
          onMouseUp={(e) => {
            e.currentTarget.style.transform = 'scale(1)';
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.transform = 'scale(1)';
          }}
        >
          {actionText}
        </button>
      </motion.div>
    </motion.div>
  );
};

export default IOSLoadingIndicator;
