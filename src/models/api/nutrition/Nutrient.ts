/**
 * 营养素分析相关API数据模型
 * 对应后端API返回的营养分析数据结构
 */

// 营养素类型
export type NutrientType = 
  | 'calories' 
  | 'protein' 
  | 'carbohydrates' 
  | 'fat' 
  | 'fiber' 
  | 'sugar' 
  | 'sodium' 
  | 'potassium' 
  | 'calcium' 
  | 'iron' 
  | 'vitamin_c' 
  | 'vitamin_a' 
  | 'vitamin_d' 
  | 'vitamin_b12' 
  | 'folate' 
  | 'magnesium' 
  | 'zinc';

// 营养素信息
export interface ApiNutrientInfo {
  type: NutrientType;
  name: string;
  name_en: string;
  unit: string;
  daily_value: number; // 每日推荐值
  min_value?: number; // 最小推荐值
  max_value?: number; // 最大推荐值
  category: 'macronutrient' | 'micronutrient' | 'vitamin' | 'mineral';
  description?: string;
  health_benefits?: string[];
  food_sources?: string[];
}

// 营养素摄入记录
export interface ApiNutrientIntake {
  nutrient_type: NutrientType;
  amount: number;
  unit: string;
  percentage_dv: number; // 占每日推荐值的百分比
  source: 'food' | 'supplement' | 'manual';
  source_details?: string;
}

// 营养分析报告
export interface ApiNutritionAnalysis {
  user_id: number;
  analysis_date: string; // YYYY-MM-DD
  period_type: 'day' | 'week' | 'month';
  start_date: string;
  end_date: string;
  nutrient_intakes: ApiNutrientIntake[];
  total_calories: number;
  macronutrient_breakdown: {
    protein_percentage: number;
    carbs_percentage: number;
    fat_percentage: number;
  };
  nutrient_adequacy_score: number; // 营养充足性评分 (0-100)
  deficient_nutrients: NutrientType[];
  excessive_nutrients: NutrientType[];
  recommendations: ApiNutritionRecommendation[];
  created_at: string;
}

// 营养建议
export interface ApiNutritionRecommendation {
  type: 'increase' | 'decrease' | 'maintain' | 'supplement';
  nutrient_type: NutrientType;
  current_amount: number;
  target_amount: number;
  priority: 'high' | 'medium' | 'low';
  reason: string;
  suggested_foods: string[];
  suggested_actions: string[];
}

// 营养评分
export interface ApiNutritionScore {
  overall_score: number; // 总体评分 (0-100)
  category_scores: {
    macronutrient_balance: number;
    micronutrient_adequacy: number;
    calorie_balance: number;
    food_variety: number;
    meal_timing: number;
  };
  improvement_areas: string[];
  strengths: string[];
}

// 营养趋势数据
export interface ApiNutritionTrend {
  nutrient_type: NutrientType;
  period: 'week' | 'month' | 'quarter' | 'year';
  data_points: ApiNutritionDataPoint[];
  trend_direction: 'increasing' | 'decreasing' | 'stable';
  average_value: number;
  target_value: number;
  compliance_rate: number; // 达标率
}

export interface ApiNutritionDataPoint {
  date: string; // YYYY-MM-DD
  value: number;
  percentage_dv: number;
  is_target_met: boolean;
}

// 营养对比分析
export interface ApiNutritionComparison {
  user_id: number;
  comparison_type: 'period' | 'goal' | 'population';
  baseline_period: {
    start_date: string;
    end_date: string;
    label: string;
  };
  comparison_period: {
    start_date: string;
    end_date: string;
    label: string;
  };
  nutrient_changes: ApiNutrientChange[];
  overall_improvement: number; // 整体改善百分比
  summary: string;
}

export interface ApiNutrientChange {
  nutrient_type: NutrientType;
  baseline_value: number;
  comparison_value: number;
  change_amount: number;
  change_percentage: number;
  change_direction: 'improved' | 'declined' | 'unchanged';
  significance: 'high' | 'medium' | 'low';
}

// 营养缺陷分析
export interface ApiNutrientDeficiency {
  nutrient_type: NutrientType;
  severity: 'mild' | 'moderate' | 'severe';
  current_intake: number;
  recommended_intake: number;
  deficit_amount: number;
  deficit_percentage: number;
  duration_days: number; // 缺陷持续天数
  health_risks: string[];
  correction_plan: ApiNutrientCorrectionPlan;
}

export interface ApiNutrientCorrectionPlan {
  target_increase: number;
  timeline_weeks: number;
  recommended_foods: string[];
  supplement_suggestions?: string[];
  monitoring_frequency: 'daily' | 'weekly' | 'monthly';
  success_metrics: string[];
}

// 营养目标追踪
export interface ApiNutritionGoalTracking {
  goal_id: number;
  user_id: number;
  nutrient_type: NutrientType;
  target_amount: number;
  current_amount: number;
  achievement_percentage: number;
  days_tracked: number;
  days_on_target: number;
  consistency_score: number;
  trend_direction: 'improving' | 'declining' | 'stable';
  projected_achievement_date?: string;
}

// 营养报告生成请求
export interface ApiNutritionReportRequest {
  user_id: number;
  report_type: 'daily' | 'weekly' | 'monthly' | 'custom';
  start_date: string;
  end_date?: string;
  include_trends?: boolean;
  include_recommendations?: boolean;
  include_comparisons?: boolean;
  format?: 'summary' | 'detailed' | 'scientific';
}

// 营养报告响应
export interface ApiNutritionReport {
  report_id: string;
  user_id: number;
  report_type: string;
  period: {
    start_date: string;
    end_date: string;
    duration_days: number;
  };
  executive_summary: string;
  nutrition_analysis: ApiNutritionAnalysis;
  nutrition_score: ApiNutritionScore;
  trends: ApiNutritionTrend[];
  deficiencies: ApiNutrientDeficiency[];
  recommendations: ApiNutritionRecommendation[];
  goal_tracking: ApiNutritionGoalTracking[];
  generated_at: string;
  expires_at?: string;
}

// 营养数据导出请求
export interface ApiNutritionExportRequest {
  user_id: number;
  start_date: string;
  end_date: string;
  format: 'csv' | 'excel' | 'pdf' | 'json';
  include_meals?: boolean;
  include_analysis?: boolean;
  include_trends?: boolean;
}

// 营养数据导入请求
export interface ApiNutritionImportRequest {
  user_id: number;
  data_source: 'myfitnesspal' | 'cronometer' | 'loseit' | 'custom';
  file_data: string; // base64编码的文件数据
  merge_strategy: 'replace' | 'merge' | 'skip_duplicates';
  date_range?: {
    start_date: string;
    end_date: string;
  };
}

// 营养AI分析请求
export interface ApiNutritionAIAnalysisRequest {
  user_id: number;
  analysis_type: 'pattern_detection' | 'goal_optimization' | 'health_risk_assessment' | 'meal_planning';
  time_period: {
    start_date: string;
    end_date: string;
  };
  user_profile?: {
    age: number;
    gender: 'male' | 'female' | 'other';
    weight: number;
    height: number;
    activity_level: 'sedentary' | 'light' | 'moderate' | 'active' | 'very_active';
    health_conditions?: string[];
    dietary_restrictions?: string[];
  };
}

// 营养AI分析响应
export interface ApiNutritionAIAnalysisResponse {
  analysis_id: string;
  user_id: number;
  analysis_type: string;
  insights: string[];
  patterns_detected: string[];
  risk_factors: string[];
  optimization_suggestions: string[];
  confidence_score: number;
  generated_at: string;
}

// API响应包装器
export interface ApiNutrientResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

// 营养相关错误
export interface ApiNutrientError {
  code: string;
  message: string;
  field?: string;
  details?: any;
}
