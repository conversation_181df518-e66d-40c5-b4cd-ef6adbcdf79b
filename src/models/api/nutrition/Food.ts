/**
 * 食物相关API数据模型
 * 对应后端API返回的食物数据结构
 */

// 基础食物信息
export interface ApiFoodItem {
  id: number;
  name: string;
  name_en?: string;
  category: string;
  food_type: string;
  brand?: string;
  barcode?: string;
  thumb_image_url?: string;
  image_url?: string;
  description?: string;
  created_at: string;
  updated_at: string;
}

// 食物营养信息
export interface ApiFoodNutrition {
  food_id: number;
  serving_size: number; // 标准份量（克）
  serving_unit: string; // 份量单位
  calories: number; // 卡路里（每100g）
  protein: number; // 蛋白质（g）
  carbohydrates: number; // 碳水化合物（g）
  fat: number; // 脂肪（g）
  fiber?: number; // 膳食纤维（g）
  sugar?: number; // 糖分（g）
  sodium?: number; // 钠（mg）
  potassium?: number; // 钾（mg）
  calcium?: number; // 钙（mg）
  iron?: number; // 铁（mg）
  vitamin_c?: number; // 维生素C（mg）
  vitamin_a?: number; // 维生素A（μg）
}

// 完整的食物详情
export interface ApiFoodDetail extends ApiFoodItem {
  nutrition: ApiFoodNutrition;
  is_favorite?: boolean;
  user_rating?: number;
  avg_rating?: number;
  usage_count?: number;
}

// 食物搜索结果
export interface ApiFoodSearchResult {
  foods: ApiFoodItem[];
  total: number;
  skip: number;
  limit: number;
  has_more: boolean;
}

// 食物分类
export interface ApiFoodCategory {
  id: number;
  name: string;
  name_en?: string;
  description?: string;
  icon?: string;
  food_count?: number;
}

// 食物类型
export interface ApiFoodType {
  id: number;
  name: string;
  name_en?: string;
  description?: string;
  category_id?: number;
}

// 食物识别结果
export interface ApiFoodRecognitionResult {
  recognized_foods: ApiFoodRecognitionItem[];
  confidence: number;
  meal_type: string;
  meal_date: string;
  image_url?: string;
  processing_time?: number;
}

export interface ApiFoodRecognitionItem {
  food: ApiFoodDetail;
  confidence: number;
  estimated_weight: number; // 估计重量（克）
  estimated_calories: number; // 估计卡路里
  bounding_box?: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
}

// 用户食物收藏
export interface ApiFoodFavorite {
  id: number;
  user_id: number;
  food_id: number;
  food: ApiFoodDetail;
  created_at: string;
  notes?: string;
}

// 食物搜索参数
export interface ApiFoodSearchParams {
  skip?: number;
  limit?: number;
  name?: string;
  category?: string;
  food_type?: string;
  brand?: string;
  barcode?: string;
  min_protein?: number;
  max_calories?: number;
  sort_by?: 'name' | 'calories' | 'protein' | 'popularity' | 'created_at';
  sort_order?: 'asc' | 'desc';
}

// 食物图片分析请求
export interface ApiFoodAnalysisRequest {
  image: string; // base64编码的图片
  meal_type: 'breakfast' | 'lunch' | 'dinner' | 'snack';
  meal_date?: string; // YYYY-MM-DD格式
  optimize?: boolean;
}

// 食物图片分析响应
export interface ApiFoodAnalysisResponse {
  success: boolean;
  data: ApiFoodRecognitionResult;
  message?: string;
  error?: string;
}

// 批量食物查询请求
export interface ApiBatchFoodRequest {
  food_ids: number[];
  include_nutrition?: boolean;
  include_favorites?: boolean;
}

// 批量食物查询响应
export interface ApiBatchFoodResponse {
  foods: ApiFoodDetail[];
  not_found_ids: number[];
}

// 食物评分请求
export interface ApiFoodRatingRequest {
  food_id: number;
  rating: number; // 1-5分
  comment?: string;
}

// 食物评分响应
export interface ApiFoodRatingResponse {
  success: boolean;
  food_id: number;
  user_rating: number;
  avg_rating: number;
  rating_count: number;
}

// 食物使用统计
export interface ApiFoodUsageStats {
  food_id: number;
  usage_count: number;
  last_used_at: string;
  avg_portion_size: number;
  total_calories_consumed: number;
  favorite_meal_type: string;
}

// 热门食物
export interface ApiPopularFood {
  food: ApiFoodDetail;
  usage_count: number;
  user_count: number;
  avg_rating: number;
  trend_score: number;
}

// 食物建议
export interface ApiFoodSuggestion {
  food: ApiFoodDetail;
  reason: string;
  confidence: number;
  meal_type_suggestion: string;
  portion_suggestion: number;
}

// API响应包装器
export interface ApiFoodResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
  timestamp?: string;
}

// 食物相关错误类型
export interface ApiFoodError {
  code: string;
  message: string;
  details?: any;
  field?: string;
}
