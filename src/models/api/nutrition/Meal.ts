/**
 * 餐食相关API数据模型
 * 对应后端API返回的餐食数据结构
 */

import { ApiFoodDetail } from './Food';

// 餐食类型
export type MealType = 'breakfast' | 'lunch' | 'dinner' | 'snack';

// 餐食条目（单个食物在餐食中的记录）
export interface ApiMealItem {
  id: number;
  meal_id: number;
  food_id: number;
  food: ApiFoodDetail;
  quantity: number; // 数量
  unit: string; // 单位（g, ml, 个, 份等）
  weight_grams: number; // 实际重量（克）
  calories: number; // 实际卡路里
  protein: number; // 实际蛋白质（g）
  carbohydrates: number; // 实际碳水化合物（g）
  fat: number; // 实际脂肪（g）
  notes?: string;
  created_at: string;
  updated_at: string;
}

// 餐食记录
export interface ApiMealRecord {
  id: number;
  user_id: number;
  meal_type: MealType;
  meal_date: string; // YYYY-MM-DD格式
  meal_name?: string;
  meal_time?: string; // HH:MM格式
  items: ApiMealItem[];
  total_calories: number;
  total_protein: number;
  total_carbohydrates: number;
  total_fat: number;
  total_fiber?: number;
  total_sugar?: number;
  total_sodium?: number;
  notes?: string;
  image_url?: string;
  is_template?: boolean;
  template_name?: string;
  created_at: string;
  updated_at: string;
}

// 餐食计划
export interface ApiMealPlan {
  id: number;
  user_id: number;
  plan_name: string;
  plan_date: string; // YYYY-MM-DD格式
  target_calories: number;
  target_protein: number;
  target_carbohydrates: number;
  target_fat: number;
  meals: ApiMealRecord[];
  is_active: boolean;
  is_template: boolean;
  created_at: string;
  updated_at: string;
}

// 餐食模板
export interface ApiMealTemplate {
  id: number;
  user_id: number;
  template_name: string;
  meal_type: MealType;
  description?: string;
  items: ApiMealTemplateItem[];
  total_calories: number;
  total_protein: number;
  total_carbohydrates: number;
  total_fat: number;
  is_public: boolean;
  usage_count: number;
  rating: number;
  tags: string[];
  created_at: string;
  updated_at: string;
}

export interface ApiMealTemplateItem {
  id: number;
  template_id: number;
  food_id: number;
  food: ApiFoodDetail;
  quantity: number;
  unit: string;
  weight_grams: number;
  order_index: number;
}

// 每日营养摘要
export interface ApiDailyNutritionSummary {
  date: string; // YYYY-MM-DD格式
  user_id: number;
  meals: ApiMealRecord[];
  total_calories: number;
  total_protein: number;
  total_carbohydrates: number;
  total_fat: number;
  total_fiber: number;
  total_sugar: number;
  total_sodium: number;
  target_calories: number;
  target_protein: number;
  target_carbohydrates: number;
  target_fat: number;
  calories_remaining: number;
  protein_remaining: number;
  carbs_remaining: number;
  fat_remaining: number;
  water_intake?: number; // 水分摄入（ml）
  meal_count: number;
  completion_percentage: number;
}

// 营养目标设置
export interface ApiNutritionGoal {
  id: number;
  user_id: number;
  goal_type: 'weight_loss' | 'weight_gain' | 'maintenance' | 'muscle_gain' | 'custom';
  target_calories: number;
  target_protein: number; // g
  target_carbohydrates: number; // g
  target_fat: number; // g
  target_fiber?: number; // g
  target_sugar?: number; // g
  target_sodium?: number; // mg
  target_water?: number; // ml
  start_date: string;
  end_date?: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

// 餐食搜索参数
export interface ApiMealSearchParams {
  skip?: number;
  limit?: number;
  meal_type?: MealType;
  start_date?: string; // YYYY-MM-DD
  end_date?: string; // YYYY-MM-DD
  min_calories?: number;
  max_calories?: number;
  has_image?: boolean;
  is_template?: boolean;
  sort_by?: 'date' | 'calories' | 'protein' | 'created_at';
  sort_order?: 'asc' | 'desc';
}

// 餐食创建请求
export interface ApiMealCreateRequest {
  meal_type: MealType;
  meal_date: string;
  meal_name?: string;
  meal_time?: string;
  items: ApiMealItemCreateRequest[];
  notes?: string;
  image?: string; // base64编码的图片
  save_as_template?: boolean;
  template_name?: string;
}

export interface ApiMealItemCreateRequest {
  food_id: number;
  quantity: number;
  unit: string;
  notes?: string;
}

// 餐食更新请求
export interface ApiMealUpdateRequest {
  meal_name?: string;
  meal_time?: string;
  items?: ApiMealItemUpdateRequest[];
  notes?: string;
  image?: string;
}

export interface ApiMealItemUpdateRequest {
  id?: number; // 如果有ID则更新，否则新增
  food_id: number;
  quantity: number;
  unit: string;
  notes?: string;
  _action?: 'add' | 'update' | 'delete';
}

// 餐食复制请求
export interface ApiMealCopyRequest {
  source_meal_id: number;
  target_date: string;
  target_meal_type?: MealType;
  copy_notes?: boolean;
}

// 餐食分享请求
export interface ApiMealShareRequest {
  meal_id: number;
  share_type: 'public' | 'friends' | 'private';
  description?: string;
  tags?: string[];
}

// 餐食统计
export interface ApiMealStats {
  user_id: number;
  period: 'week' | 'month' | 'year';
  start_date: string;
  end_date: string;
  total_meals: number;
  avg_calories_per_day: number;
  avg_protein_per_day: number;
  avg_carbs_per_day: number;
  avg_fat_per_day: number;
  most_frequent_foods: ApiFoodDetail[];
  favorite_meal_type: MealType;
  goal_achievement_rate: number;
  consistency_score: number;
}

// 餐食建议
export interface ApiMealSuggestion {
  meal_type: MealType;
  suggested_foods: ApiFoodDetail[];
  reason: string;
  estimated_calories: number;
  estimated_protein: number;
  estimated_carbs: number;
  estimated_fat: number;
  confidence: number;
}

// API响应类型
export interface ApiMealResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

// 餐食相关错误
export interface ApiMealError {
  code: string;
  message: string;
  field?: string;
  details?: any;
}
