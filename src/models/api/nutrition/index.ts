/**
 * 营养模块API数据模型统一导出
 */

// 食物相关模型
export * from './Food';

// 餐食相关模型
export * from './Meal';

// 营养素相关模型
export * from './Nutrient';

// 营养模块通用类型
export type NutritionModuleType = 'food' | 'meal' | 'nutrient' | 'analysis';

export interface NutritionApiConfig {
  baseUrl: string;
  timeout: number;
  retryAttempts: number;
  cacheEnabled: boolean;
  cacheTTL: number;
}

export interface NutritionApiError {
  module: NutritionModuleType;
  operation: string;
  code: string;
  message: string;
  timestamp: string;
  details?: any;
}
