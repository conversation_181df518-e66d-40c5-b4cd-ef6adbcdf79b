/**
 * 团队训练相关API数据模型
 * 对应后端API返回的团队训练数据结构
 */

// 团队训练状态
export type TeamTrainingStatus = 'active' | 'completed' | 'paused' | 'cancelled';

// 训练会话状态
export type TrainingSessionStatus = 'scheduled' | 'in_progress' | 'completed' | 'cancelled' | 'missed';

// 团队角色
export type TeamRole = 'coach' | 'member' | 'admin';

// 训练模板
export interface TeamTrainingTemplate {
  id: number;
  team_id: number;
  name: string;
  description: string;
  duration_weeks: number;
  sessions_per_week: number;
  difficulty_level: number; // 1-5
  target_audience: string;
  equipment_required: number[];
  is_public: boolean;
  exercises: TeamTemplateExercise[];
  created_by: number;
  created_at: string;
  updated_at: string;
}

// 团队模板练习
export interface TeamTemplateExercise {
  id: number;
  template_id: number;
  day: number; // 第几天
  exercise_id: number;
  sets: number;
  reps: string; // 例如 "8-10", "12"
  rest_seconds?: number;
  notes?: string;
  order_index: number;
}

// 客户训练计划
export interface ClientTrainingPlan {
  id: number;
  client_relation_id: number;
  training_plan_id: number;
  status: TeamTrainingStatus;
  start_date: string;
  end_date: string;
  scheduled_time: { [dayOfWeek: string]: string }; // 1-7 对应周一到周日
  notes?: string;
  progress_percentage: number;
  created_at: string;
  updated_at: string;
}

// 训练会话
export interface TrainingSession {
  id: number;
  plan_id: number;
  client_id: number;
  coach_id: number;
  status: TrainingSessionStatus;
  scheduled_date: string;
  start_time?: string;
  end_time?: string;
  duration_minutes?: number;
  exercises: SessionExercise[];
  notes?: string;
  feedback?: string;
  mood_rating?: number; // 1-5
  difficulty_rating?: number; // 1-5
  created_at: string;
  updated_at: string;
}

// 会话练习
export interface SessionExercise {
  id: number;
  session_id: number;
  exercise_id: number;
  planned_sets: number;
  completed_sets: number;
  records: SessionSetRecord[];
  notes?: string;
}

// 会话组记录
export interface SessionSetRecord {
  id: number;
  session_exercise_id: number;
  set_number: number;
  weight: number;
  reps: number;
  rpe?: number; // Rate of Perceived Exertion (1-10)
  notes?: string;
  completed_at: string;
}

// 团队信息
export interface TeamInfo {
  id: number;
  name: string;
  description?: string;
  coach_id: number;
  member_count: number;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

// 团队成员
export interface TeamMember {
  id: number;
  team_id: number;
  user_id: number;
  role: TeamRole;
  joined_at: string;
  is_active: boolean;
  user_info: {
    name: string;
    email: string;
    avatar_url?: string;
  };
}

// 客户关系
export interface ClientRelation {
  id: number;
  coach_id: number;
  client_id: number;
  team_id?: number;
  status: 'active' | 'inactive' | 'pending';
  notes?: string;
  created_at: string;
  updated_at: string;
}

// 创建团队模板请求
export interface CreateTeamTemplateRequest {
  team_id: number;
  name: string;
  description?: string;
  duration_weeks: number;
  sessions_per_week: number;
  difficulty_level?: number;
  target_audience?: string;
  equipment_required?: number[];
  is_public?: boolean;
  exercises: CreateTemplateExerciseRequest[];
}

export interface CreateTemplateExerciseRequest {
  day: number;
  exercise_id: number;
  sets: number;
  reps: string;
  rest_seconds?: number;
  notes?: string;
  order_index: number;
}

// 创建客户计划请求
export interface CreateClientPlanRequest {
  client_relation_id: number;
  training_plan_id: number;
  start_date: string;
  end_date: string;
  scheduled_time: { [dayOfWeek: string]: string };
  notes?: string;
}

// 更新客户计划请求
export interface UpdateClientPlanRequest {
  status?: TeamTrainingStatus;
  end_date?: string;
  scheduled_time?: { [dayOfWeek: string]: string };
  notes?: string;
}

// 记录训练组请求
export interface RecordSessionSetRequest {
  weight: number;
  reps: number;
  rpe?: number;
  notes?: string;
}

// 完成会话请求
export interface CompleteSessionRequest {
  feedback?: string;
  mood_rating?: number;
  difficulty_rating?: number;
  notes?: string;
}

// 团队训练统计
export interface TeamTrainingStats {
  team_id: number;
  period: 'week' | 'month' | 'quarter' | 'year';
  total_sessions: number;
  completed_sessions: number;
  completion_rate: number;
  avg_session_duration: number;
  active_members: number;
  most_active_members: TeamMember[];
  popular_exercises: any[];
  progress_trends: any[];
}

// 搜索参数
export interface TeamTrainingSearchParams {
  skip?: number;
  limit?: number;
  team_id?: number;
  status?: TeamTrainingStatus;
  start_date?: string;
  end_date?: string;
  coach_id?: number;
  client_id?: number;
  sort_by?: 'created_at' | 'updated_at' | 'scheduled_date' | 'name';
  sort_order?: 'asc' | 'desc';
}

// API响应包装器
export interface TeamTrainingResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
  metadata?: {
    total?: number;
    page?: number;
    limit?: number;
  };
}

// 团队训练相关错误
export interface TeamTrainingError {
  code: string;
  message: string;
  field?: string;
  details?: any;
  retryable: boolean;
}
