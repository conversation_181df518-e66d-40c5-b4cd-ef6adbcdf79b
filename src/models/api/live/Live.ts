/**
 * 视频转动态卡片服务相关API数据模型
 * 对应后端API返回的视频处理和卡片生成数据结构
 */

// 卡片生成状态
export type CardGenerationStatus = 'pending' | 'processing' | 'completed' | 'failed';

// 视频质量选项
export type VideoQuality = 'high' | 'medium' | 'low';

// 输出格式
export type OutputFormat = 'livephoto' | 'mp4' | 'gif' | 'png' | 'jpg';

// 卡片样式类型
export type CardStyleType = 'fitness' | 'nutrition' | 'progress' | 'achievement' | 'custom';

// 视频转卡片请求
export interface VideoToCardRequest {
  video_data: string; // base64编码的视频数据或视频URL
  video_source: 'upload' | 'url' | 'camera';
  card_config: CardConfig;
  processing_options: VideoProcessingOptions;
  metadata?: {
    title?: string;
    description?: string;
    tags?: string[];
    user_id?: number;
  };
}

// 卡片配置
export interface CardConfig {
  style_type: CardStyleType;
  dimensions: {
    width: number;
    height: number;
    aspect_ratio: '3:4' | '4:3' | '1:1' | '16:9';
  };
  design: {
    background_color?: string;
    background_gradient?: string[];
    overlay_opacity?: number;
    border_radius?: number;
    shadow_enabled?: boolean;
  };
  content: {
    show_title?: boolean;
    show_description?: boolean;
    show_logo?: boolean;
    show_timestamp?: boolean;
    show_progress_bar?: boolean;
  };
  branding?: {
    logo_url?: string;
    brand_colors?: string[];
    watermark_text?: string;
    watermark_position?: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right';
  };
}

// 视频处理选项
export interface VideoProcessingOptions {
  quality: VideoQuality;
  output_format: OutputFormat;
  duration_limit?: number; // 秒数，限制视频长度
  frame_rate?: number;
  compression_level?: number; // 1-10
  auto_enhance?: boolean;
  stabilization?: boolean;
  noise_reduction?: boolean;
}

// 卡片生成结果
export interface CardGenerationResult {
  id: string;
  status: CardGenerationStatus;
  card_url?: string;
  thumbnail_url?: string;
  download_url?: string;
  file_info: {
    file_size: number;
    format: OutputFormat;
    dimensions: {
      width: number;
      height: number;
    };
    duration?: number; // 对于动态卡片
  };
  processing_info: {
    processing_time: number;
    quality_score?: number;
    compression_ratio?: number;
  };
  created_at: string;
  expires_at?: string;
  error_message?: string;
}

// 卡片模板
export interface CardTemplate {
  id: string;
  name: string;
  description: string;
  style_type: CardStyleType;
  preview_url: string;
  config: CardConfig;
  is_premium: boolean;
  usage_count: number;
  rating: number;
  tags: string[];
  created_at: string;
  updated_at: string;
}

// 图表类型
export type ChartType = 'line' | 'bar' | 'pie' | 'area';

// 导出格式
export type ExportFormat = 'png' | 'jpg' | 'pdf' | 'svg';

// 直播类型
export type LiveType = 'workout' | 'nutrition' | 'education';

// 直播状态
export type LiveStatus = 'scheduled' | 'live' | 'ended' | 'cancelled';

// 图表配置
export interface ChartConfig {
  id: string;
  title: string;
  type: ChartType;
  data_source: 'workout' | 'nutrition' | 'progress' | 'custom';
  dimensions: {
    width: number;
    height: number;
  };
  style: {
    theme: 'light' | 'dark' | 'custom';
    color_scheme: string[];
    font_family?: string;
    font_size?: number;
  };
  options: {
    show_legend: boolean;
    show_grid: boolean;
    show_labels: boolean;
    animation_enabled: boolean;
    responsive: boolean;
  };
  data_config: {
    x_axis: string;
    y_axis: string[];
    filters?: any;
    date_range?: {
      start_date: string;
      end_date: string;
    };
  };
}

// 图表数据
export interface ChartData {
  labels: string[];
  datasets: ChartDataset[];
  metadata: {
    total_points: number;
    data_range: {
      min: number;
      max: number;
    };
    last_updated: string;
  };
}

export interface ChartDataset {
  label: string;
  data: number[];
  backgroundColor?: string | string[];
  borderColor?: string;
  borderWidth?: number;
  fill?: boolean;
  tension?: number;
}

// 图表生成请求
export interface ChartGenerationRequest {
  config: ChartConfig;
  export_format: ExportFormat;
  quality?: 'low' | 'medium' | 'high';
  include_watermark?: boolean;
  custom_branding?: {
    logo_url?: string;
    brand_colors?: string[];
    footer_text?: string;
  };
}

// 图表生成结果
export interface ChartGenerationResult {
  id: string;
  chart_url: string;
  download_url: string;
  thumbnail_url: string;
  file_size: number;
  format: ExportFormat;
  dimensions: {
    width: number;
    height: number;
  };
  generated_at: string;
  expires_at: string;
}

// 直播创建请求
export interface CreateLiveStreamRequest {
  title: string;
  description?: string;
  type: LiveType;
  scheduled_start: string;
  duration_minutes: number;
  max_participants?: number;
  thumbnail?: string; // base64编码的图片
  chat_enabled?: boolean;
  recording_enabled?: boolean;
  tags?: string[];
  is_premium?: boolean;
  price?: number;
}

// 直播更新请求
export interface UpdateLiveStreamRequest {
  title?: string;
  description?: string;
  scheduled_start?: string;
  duration_minutes?: number;
  max_participants?: number;
  thumbnail?: string;
  chat_enabled?: boolean;
  recording_enabled?: boolean;
  tags?: string[];
}

// 直播搜索参数
export interface LiveSearchParams {
  skip?: number;
  limit?: number;
  type?: LiveType;
  status?: LiveStatus;
  host_id?: number;
  start_date?: string;
  end_date?: string;
  is_premium?: boolean;
  tags?: string[];
  search_query?: string;
  sort_by?: 'scheduled_start' | 'created_at' | 'participants' | 'title';
  sort_order?: 'asc' | 'desc';
}

// 直播统计
export interface LiveStats {
  live_id: number;
  total_participants: number;
  peak_participants: number;
  avg_watch_duration: number;
  total_messages: number;
  engagement_rate: number;
  completion_rate: number;
  rating: number;
  revenue?: number;
  viewer_demographics: {
    age_groups: { [key: string]: number };
    locations: { [key: string]: number };
    devices: { [key: string]: number };
  };
}

// 视频处理请求
export interface VideoProcessingRequest {
  video_data: string; // base64编码的视频数据
  processing_options: {
    quality: 'low' | 'medium' | 'high';
    output_format: 'mp4' | 'webm' | 'gif' | 'livephoto';
    compression_level?: number;
    resolution?: string; // 例如 "1920x1080"
    frame_rate?: number;
  };
  metadata: {
    title?: string;
    description?: string;
    tags?: string[];
    thumbnail_time?: number; // 缩略图时间点（秒）
  };
}

// 视频处理结果
export interface VideoProcessingResult {
  id: string;
  original_url: string;
  processed_url: string;
  thumbnail_url: string;
  duration_seconds: number;
  file_size: number;
  resolution: string;
  format: string;
  processing_time: number;
  status: 'processing' | 'completed' | 'failed';
  created_at: string;
}

// 图表数据
export interface ChartData {
  labels: string[];
  datasets: ChartDataset[];
  metadata: {
    total_points: number;
    data_range: {
      min: number;
      max: number;
    };
    last_updated: string;
  };
}

export interface ChartDataset {
  label: string;
  data: number[];
  backgroundColor?: string | string[];
  borderColor?: string;
  borderWidth?: number;
  fill?: boolean;
  tension?: number;
}

// 文件上传请求
export interface FileUploadRequest {
  file_data: string; // base64编码的文件数据
  file_type: 'image' | 'video' | 'document';
  file_name: string;
  folder?: string;
  is_public?: boolean;
  metadata?: {
    title?: string;
    description?: string;
    tags?: string[];
  };
}

// 文件上传结果
export interface FileUploadResult {
  id: string;
  file_url: string;
  thumbnail_url?: string;
  file_name: string;
  file_size: number;
  file_type: string;
  mime_type: string;
  is_public: boolean;
  uploaded_at: string;
  expires_at?: string;
}

// API响应包装器
export interface LiveResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
  metadata?: {
    total?: number;
    page?: number;
    limit?: number;
    processing_time?: number;
  };
}

// 直播相关错误
export interface LiveError {
  code: string;
  message: string;
  details?: any;
  retryable: boolean;
  suggested_action?: string;
}
