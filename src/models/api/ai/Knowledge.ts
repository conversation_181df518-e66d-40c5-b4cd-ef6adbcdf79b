/**
 * AI知识库相关API数据模型
 * 对应后端API返回的知识库数据结构
 */

// 知识库分类
export type KnowledgeCategory = 
  | 'exercise_technique'
  | 'workout_planning'
  | 'nutrition_basics'
  | 'meal_planning'
  | 'injury_prevention'
  | 'recovery'
  | 'supplements'
  | 'goal_setting'
  | 'motivation'
  | 'equipment'
  | 'anatomy'
  | 'physiology';

// 知识条目类型
export type KnowledgeType = 
  | 'fact'
  | 'instruction'
  | 'advice'
  | 'warning'
  | 'tip'
  | 'example'
  | 'definition'
  | 'procedure';

// 知识库条目
export interface KnowledgeEntry {
  id: string;
  category: KnowledgeCategory;
  type: KnowledgeType;
  title: string;
  content: string;
  summary: string;
  keywords: string[];
  tags: string[];
  difficulty_level: 'beginner' | 'intermediate' | 'advanced';
  confidence_score: number;
  usage_count: number;
  effectiveness_score: number;
  last_updated: string;
  created_at: string;
  metadata?: {
    source?: string;
    author?: string;
    verified_by?: string;
    references?: string[];
    related_entries?: string[];
    media_urls?: string[];
  };
}

// 知识库搜索结果
export interface KnowledgeSearchResult {
  entries: KnowledgeEntry[];
  total: number;
  query: string;
  search_time: number;
  suggestions: string[];
  related_topics: string[];
}

// 知识库搜索参数
export interface KnowledgeSearchParams {
  query: string;
  category?: KnowledgeCategory;
  type?: KnowledgeType;
  difficulty_level?: 'beginner' | 'intermediate' | 'advanced';
  min_confidence?: number;
  keywords?: string[];
  tags?: string[];
  skip?: number;
  limit?: number;
  sort_by?: 'relevance' | 'confidence' | 'usage_count' | 'updated_at';
  sort_order?: 'asc' | 'desc';
}

// 知识库推荐
export interface KnowledgeRecommendation {
  entry: KnowledgeEntry;
  relevance_score: number;
  reason: string;
  context: string;
  personalization_factors: string[];
}

// 知识库统计
export interface KnowledgeStats {
  total_entries: number;
  categories_count: { [key in KnowledgeCategory]: number };
  types_count: { [key in KnowledgeType]: number };
  avg_confidence_score: number;
  most_used_entries: KnowledgeEntry[];
  recent_updates: KnowledgeEntry[];
  coverage_gaps: string[];
}

// 知识验证请求
export interface KnowledgeValidationRequest {
  entry_id: string;
  validator_id: number;
  validation_type: 'accuracy' | 'completeness' | 'relevance' | 'safety';
  score: number; // 1-10
  comments?: string;
  suggested_improvements?: string[];
}

// 知识验证结果
export interface KnowledgeValidationResult {
  entry_id: string;
  overall_score: number;
  validation_count: number;
  validations: KnowledgeValidation[];
  status: 'pending' | 'approved' | 'rejected' | 'needs_review';
  last_validated: string;
}

// 知识验证记录
export interface KnowledgeValidation {
  id: string;
  entry_id: string;
  validator_id: number;
  validation_type: 'accuracy' | 'completeness' | 'relevance' | 'safety';
  score: number;
  comments?: string;
  suggested_improvements?: string[];
  created_at: string;
}

// 知识库更新请求
export interface KnowledgeUpdateRequest {
  title?: string;
  content?: string;
  summary?: string;
  keywords?: string[];
  tags?: string[];
  category?: KnowledgeCategory;
  type?: KnowledgeType;
  difficulty_level?: 'beginner' | 'intermediate' | 'advanced';
  metadata?: any;
}

// 知识库创建请求
export interface KnowledgeCreateRequest {
  category: KnowledgeCategory;
  type: KnowledgeType;
  title: string;
  content: string;
  summary: string;
  keywords: string[];
  tags: string[];
  difficulty_level: 'beginner' | 'intermediate' | 'advanced';
  metadata?: any;
}

// 知识库使用记录
export interface KnowledgeUsageRecord {
  id: string;
  entry_id: string;
  user_id: number;
  conversation_id: string;
  message_id: string;
  usage_type: 'direct_answer' | 'reference' | 'suggestion' | 'related_content';
  context: string;
  effectiveness_rating?: number; // 用户反馈评分
  timestamp: string;
}

// 知识库分析报告
export interface KnowledgeAnalyticsReport {
  period: {
    start_date: string;
    end_date: string;
  };
  usage_stats: {
    total_queries: number;
    successful_matches: number;
    match_rate: number;
    avg_response_time: number;
  };
  popular_topics: {
    topic: string;
    query_count: number;
    success_rate: number;
  }[];
  performance_metrics: {
    entry_id: string;
    title: string;
    usage_count: number;
    effectiveness_score: number;
    user_satisfaction: number;
  }[];
  gaps_analysis: {
    missing_topics: string[];
    low_confidence_areas: string[];
    improvement_suggestions: string[];
  };
}

// 知识库同步状态
export interface KnowledgeSyncStatus {
  last_sync: string;
  total_entries: number;
  updated_entries: number;
  new_entries: number;
  deleted_entries: number;
  sync_status: 'success' | 'partial' | 'failed';
  errors?: string[];
  next_sync_scheduled: string;
}

// 知识库配置
export interface KnowledgeConfig {
  auto_update_enabled: boolean;
  sync_interval_hours: number;
  min_confidence_threshold: number;
  max_results_per_query: number;
  enable_personalization: boolean;
  enable_usage_tracking: boolean;
  cache_ttl_minutes: number;
  fallback_enabled: boolean;
}

// 知识库导出请求
export interface KnowledgeExportRequest {
  categories?: KnowledgeCategory[];
  types?: KnowledgeType[];
  min_confidence?: number;
  format: 'json' | 'csv' | 'xml' | 'markdown';
  include_metadata: boolean;
  include_usage_stats: boolean;
  date_range?: {
    start_date: string;
    end_date: string;
  };
}

// 知识库导入请求
export interface KnowledgeImportRequest {
  source: 'file' | 'url' | 'api';
  data: string; // base64编码的文件数据或URL
  format: 'json' | 'csv' | 'xml' | 'markdown';
  merge_strategy: 'replace' | 'merge' | 'skip_duplicates';
  validation_required: boolean;
  auto_categorize: boolean;
}

// 知识库备份信息
export interface KnowledgeBackup {
  id: string;
  backup_type: 'full' | 'incremental';
  total_entries: number;
  file_size: number;
  created_at: string;
  expires_at: string;
  download_url: string;
  checksum: string;
  status: 'creating' | 'ready' | 'expired' | 'failed';
}

// API响应包装器
export interface KnowledgeResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
  metadata?: {
    query_time?: number;
    cache_hit?: boolean;
    total_entries?: number;
  };
}

// 知识库相关错误
export interface KnowledgeError {
  code: string;
  message: string;
  category?: string;
  details?: any;
  retryable: boolean;
}
