/**
 * 简化AI聊天相关API数据模型
 * 专注于信息接收、展示和缓存的基础功能
 */

// 消息角色类型
export type MessageRole = 'user' | 'assistant' | 'system';

// 消息状态
export type MessageStatus = 'sending' | 'sent' | 'delivered' | 'failed';

// 简化的聊天消息
export interface SimpleChatMessage {
  id: string;
  conversation_id: string;
  role: MessageRole;
  content: string;
  timestamp: string;
  status: MessageStatus;
  metadata?: {
    response_time?: number;
    cached?: boolean;
    source?: string;
  };
}

// 简化的对话
export interface SimpleConversation {
  id: string;
  user_id: number;
  title: string;
  last_message?: SimpleChatMessage;
  message_count: number;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

// 预设回复
export interface PresetReply {
  id: string;
  category: 'fitness' | 'nutrition' | 'motivation' | 'general';
  trigger_keywords: string[];
  response: string;
  usage_count: number;
  is_active: boolean;
}

// 快速回复建议
export interface QuickReply {
  id: string;
  text: string;
  category: string;
  order: number;
}

// 聊天统计
export interface ChatStats {
  user_id: number;
  total_conversations: number;
  total_messages: number;
  avg_response_time: number;
  most_used_categories: string[];
  last_active: string;
}

// 发送消息请求
export interface SendMessageRequest {
  conversation_id?: string; // 如果为空则创建新对话
  content: string;
  message_type?: 'text' | 'quick_reply';
}

// 发送消息响应
export interface SendMessageResponse {
  user_message: SimpleChatMessage;
  ai_response: SimpleChatMessage;
  conversation: SimpleConversation;
  quick_replies?: QuickReply[];
}

// 对话搜索参数
export interface ConversationSearchParams {
  skip?: number;
  limit?: number;
  search_query?: string;
  is_active?: boolean;
  start_date?: string;
  end_date?: string;
  sort_by?: 'created_at' | 'updated_at' | 'message_count';
  sort_order?: 'asc' | 'desc';
}

// 消息搜索参数
export interface MessageSearchParams {
  conversation_id?: string;
  content_query?: string;
  role?: MessageRole;
  start_date?: string;
  end_date?: string;
  skip?: number;
  limit?: number;
}

// 缓存配置
export interface ChatCacheConfig {
  conversations_ttl: number;
  messages_ttl: number;
  preset_replies_ttl: number;
  quick_replies_ttl: number;
  enable_offline_mode: boolean;
}

// 离线消息
export interface OfflineMessage {
  id: string;
  content: string;
  timestamp: string;
  retry_count: number;
  max_retries: number;
}

// 聊天配置
export interface SimpleChatConfig {
  max_message_length: number;
  max_conversations: number;
  enable_quick_replies: boolean;
  enable_preset_responses: boolean;
  auto_delete_old_conversations: boolean;
  conversation_retention_days: number;
}

// API响应包装器
export interface SimpleChatResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
  metadata?: {
    cached?: boolean;
    response_time?: number;
    source?: string;
  };
}

// 简化聊天错误
export interface SimpleChatError {
  code: string;
  message: string;
  retryable: boolean;
  details?: any;
}
