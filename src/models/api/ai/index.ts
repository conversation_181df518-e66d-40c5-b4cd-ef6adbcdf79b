/**
 * AI模块API数据模型统一导出
 */

// AI聊天相关模型
export * from './Chat';

// AI知识库相关模型
export * from './Knowledge';

// AI模块通用类型
export type AIModuleType = 'chat' | 'knowledge' | 'personalization' | 'analytics';

// AI服务配置
export interface AIServiceConfig {
  baseUrl: string;
  apiKey: string;
  timeout: number;
  retryAttempts: number;
  cacheEnabled: boolean;
  cacheTTL: number;
  enablePersonalization: boolean;
  enableKnowledgeBase: boolean;
  enableAnalytics: boolean;
  maxTokens: number;
  temperature: number;
}

// AI服务状态
export interface AIServiceStatus {
  service: AIModuleType;
  status: 'online' | 'offline' | 'degraded' | 'maintenance';
  last_check: string;
  response_time: number;
  error_rate: number;
  version: string;
}

// AI服务错误
export interface AIServiceError {
  module: AIModuleType;
  operation: string;
  code: string;
  message: string;
  timestamp: string;
  details?: any;
  user_id?: number;
  conversation_id?: string;
}

// AI服务指标
export interface AIServiceMetrics {
  module: AIModuleType;
  period: 'hour' | 'day' | 'week' | 'month';
  metrics: {
    total_requests: number;
    successful_requests: number;
    failed_requests: number;
    avg_response_time: number;
    cache_hit_rate: number;
    user_satisfaction: number;
  };
  timestamp: string;
}
