/**
 * AI聊天相关API数据模型
 * 对应后端API返回的AI聊天数据结构
 */

// 消息角色类型
export type MessageRole = 'user' | 'assistant' | 'system';

// 对话上下文类型
export type ContextType = 'fitness' | 'nutrition' | 'training' | 'health' | 'general';

// 意图类型
export type IntentType = 
  | 'workout_advice'
  | 'nutrition_advice'
  | 'exercise_question'
  | 'meal_planning'
  | 'goal_setting'
  | 'progress_inquiry'
  | 'general_chat'
  | 'unknown';

// AI对话消息
export interface AIChatMessage {
  id: string;
  conversation_id: string;
  role: MessageRole;
  content: string;
  timestamp: string;
  metadata?: {
    intent?: IntentType;
    confidence?: number;
    context_used?: string[];
    suggestions?: string[];
    response_time?: number;
    tokens_used?: number;
  };
  attachments?: MessageAttachment[];
  is_edited?: boolean;
  edited_at?: string;
}

// 消息附件
export interface MessageAttachment {
  id: string;
  type: 'image' | 'file' | 'workout' | 'meal' | 'exercise';
  url?: string;
  data?: any;
  metadata?: {
    size?: number;
    format?: string;
    description?: string;
  };
}

// AI对话会话
export interface AIConversation {
  id: string;
  user_id: number;
  title: string;
  context_type: ContextType;
  messages: AIChatMessage[];
  is_active: boolean;
  is_pinned?: boolean;
  last_message_at: string;
  created_at: string;
  updated_at: string;
  metadata?: {
    total_messages?: number;
    avg_response_time?: number;
    user_satisfaction?: number;
    topics_discussed?: string[];
  };
}

// 意图分析结果
export interface IntentAnalysis {
  intent: IntentType;
  confidence: number;
  entities: IntentEntity[];
  context_requirements: string[];
  suggested_actions: string[];
  response_template?: string;
}

// 意图实体
export interface IntentEntity {
  type: 'exercise' | 'muscle_group' | 'nutrition' | 'goal' | 'time' | 'number';
  value: string;
  confidence: number;
  start_pos?: number;
  end_pos?: number;
}

// AI知识库条目
export interface AIKnowledgeItem {
  id: string;
  category: 'exercise' | 'nutrition' | 'training' | 'health' | 'general';
  topic: string;
  title: string;
  content: string;
  keywords: string[];
  tags: string[];
  confidence_score: number;
  usage_count: number;
  last_updated: string;
  source?: string;
  verified?: boolean;
}

// AI个性化配置
export interface AIPersonalizationProfile {
  user_id: number;
  preferences: {
    communication_style: 'formal' | 'casual' | 'friendly' | 'professional';
    response_length: 'short' | 'medium' | 'detailed';
    expertise_level: 'beginner' | 'intermediate' | 'advanced';
    focus_areas: ContextType[];
    language: string;
  };
  learning_data: {
    frequent_topics: string[];
    preferred_advice_types: string[];
    interaction_patterns: any;
    feedback_history: UserFeedback[];
  };
  created_at: string;
  updated_at: string;
}

// 用户反馈
export interface UserFeedback {
  message_id: string;
  rating: number; // 1-5
  feedback_type: 'helpful' | 'not_helpful' | 'incorrect' | 'too_long' | 'too_short';
  comment?: string;
  timestamp: string;
}

// AI建议
export interface AISuggestion {
  id: string;
  type: 'quick_reply' | 'follow_up' | 'related_topic' | 'action_item';
  content: string;
  confidence: number;
  context: string;
  metadata?: {
    action_type?: string;
    parameters?: any;
  };
}

// 对话创建请求
export interface CreateConversationRequest {
  title?: string;
  context_type: ContextType;
  initial_message?: string;
  user_context?: {
    current_goals?: string[];
    fitness_level?: string;
    preferences?: any;
  };
}

// 发送消息请求
export interface SendMessageRequest {
  conversation_id: string;
  content: string;
  attachments?: MessageAttachment[];
  context?: {
    current_workout?: any;
    current_meal?: any;
    user_state?: any;
  };
}

// AI响应
export interface AIResponse {
  message: AIChatMessage;
  suggestions: AISuggestion[];
  context_updates?: any;
  follow_up_questions?: string[];
  related_content?: RelatedContent[];
}

// 相关内容
export interface RelatedContent {
  type: 'exercise' | 'meal' | 'article' | 'video' | 'workout_plan';
  id: string;
  title: string;
  description?: string;
  url?: string;
  thumbnail?: string;
  relevance_score: number;
}

// 对话搜索参数
export interface ConversationSearchParams {
  skip?: number;
  limit?: number;
  context_type?: ContextType;
  start_date?: string;
  end_date?: string;
  is_active?: boolean;
  search_query?: string;
  sort_by?: 'created_at' | 'updated_at' | 'last_message_at' | 'title';
  sort_order?: 'asc' | 'desc';
}

// 消息搜索参数
export interface MessageSearchParams {
  conversation_id?: string;
  content_query?: string;
  role?: MessageRole;
  intent?: IntentType;
  start_date?: string;
  end_date?: string;
  skip?: number;
  limit?: number;
}

// AI统计数据
export interface AIStats {
  user_id: number;
  period: 'day' | 'week' | 'month' | 'year';
  total_conversations: number;
  total_messages: number;
  avg_response_time: number;
  most_common_intents: IntentType[];
  satisfaction_score: number;
  topics_discussed: string[];
  knowledge_items_used: number;
  personalization_score: number;
}

// AI配置
export interface AIConfig {
  model_version: string;
  max_tokens: number;
  temperature: number;
  response_timeout: number;
  enable_personalization: boolean;
  enable_context_memory: boolean;
  enable_suggestions: boolean;
  knowledge_base_enabled: boolean;
  fallback_responses: string[];
}

// 对话导出请求
export interface ConversationExportRequest {
  conversation_ids: string[];
  format: 'json' | 'txt' | 'pdf';
  include_metadata: boolean;
  include_attachments: boolean;
  date_range?: {
    start_date: string;
    end_date: string;
  };
}

// AI训练数据
export interface AITrainingData {
  conversation_id: string;
  user_input: string;
  ai_response: string;
  user_feedback: UserFeedback;
  context: any;
  intent: IntentType;
  entities: IntentEntity[];
  quality_score: number;
  created_at: string;
}

// API响应包装器
export interface AIChatResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
  metadata?: {
    request_id?: string;
    processing_time?: number;
    model_version?: string;
  };
}

// AI聊天相关错误
export interface AIChatError {
  code: string;
  message: string;
  details?: any;
  retryable: boolean;
  suggested_action?: string;
}
