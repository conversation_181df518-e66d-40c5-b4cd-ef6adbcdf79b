/**
 * 训练动作转换器
 * 负责API训练动作和前端训练动作之间的数据转换，集成肌肉数据处理
 */

import { ApiWorkoutExercise } from '../../api/feed/Exercise';
import { WorkoutExercise } from '../../ui/exercise/Exercise';
import { MuscleGroupEnum } from '../../../types/muscle.types';
import { ExerciseDetailResponse } from '../../api/exercise/ExerciseDetail';
import { AbstractAsyncBaseTransformer } from '../base/BaseTransformer';
import { TransformerOptions } from '../../../types/transformers';
import { processImageUrl } from '../base/ImageUrlProcessor';
import { setRecordTransformer } from './SetRecordTransformer';
import { apiService } from '../../../services/api';
import { smartMuscleMappingForExercise } from '../../../utils/muscleUtils';
import { MUSCLE_CATEGORIES } from '../../../constants/exerciseCategories';

// 类型安全转换工具函数
function safeToString(value: any): string {
  if (value === null || value === undefined) {
    return '';
  }
  return String(value);
}

function safeToNumber(value: any, defaultValue: number = 0): number {
  if (value === null || value === undefined) {
    return defaultValue;
  }
  const num = Number(value);
  return isNaN(num) ? defaultValue : num;
}

/**
 * 训练动作转换器类
 */
export class ExerciseTransformer extends AbstractAsyncBaseTransformer<ApiWorkoutExercise, WorkoutExercise> {
  
  constructor(options?: TransformerOptions) {
    super(options);
  }

  /**
   * 将肌肉ID数组转换为MuscleGroupEnum数组
   * @param muscleIds 肌肉ID数组
   * @returns MuscleGroupEnum数组
   */
  private convertMuscleIdsToEnum(muscleIds: number[]): MuscleGroupEnum[] {
    const muscleGroups: MuscleGroupEnum[] = [];
    
    for (const id of muscleIds) {
      const muscle = MUSCLE_CATEGORIES[id as keyof typeof MUSCLE_CATEGORIES];
      if (muscle && muscle.group) {
        muscleGroups.push(muscle.group);
      }
    }
    
    return muscleGroups;
  }

  /**
   * 将API训练动作转换为前端训练动作
   * @param apiExercise API训练动作
   * @returns 前端训练动作
   */
  async transform(apiExercise: ApiWorkoutExercise): Promise<WorkoutExercise> {
    this.log('debug', '转换API训练动作', { apiExercise });

    try {
      // 转换组记录
      const setRecords = apiExercise.set_records?.map(record =>
        setRecordTransformer.transform(record)
      ) || [];

      // 从组记录计算总重量
      const totalWeight = setRecords.reduce((sum, record) =>
        sum + (record.weight * record.reps), 0
      );

      // 获取真实的肌肉群数据
      let primary_muscles: MuscleGroupEnum[] = [];
      let secondary_muscles: MuscleGroupEnum[] = [];
      
      // 保存原始肌肉ID数据（用于整合）
      let targetMuscleIds: number[] = [];
      let synergistMuscleIds: number[] = [];

      try {
        // 通过exercise_id获取动作详情（包含真实的肌肉数据）
        const exerciseDetail: ExerciseDetailResponse = await apiService.getExerciseDetail(apiExercise.exercise_id);
        
        // 使用智能肌肉映射处理肌肉群数据
        const muscleMapping = smartMuscleMappingForExercise(
          exerciseDetail.target_muscles_id || [],
          exerciseDetail.synergist_muscles_id || []
        );
        
        // 将肌肉ID转换为MuscleGroupEnum
        primary_muscles = this.convertMuscleIdsToEnum(muscleMapping.adjustedPrimary);
        secondary_muscles = this.convertMuscleIdsToEnum(muscleMapping.adjustedSecondary);
        
        // 保存原始肌肉ID数据用于后续整合
        targetMuscleIds = muscleMapping.adjustedPrimary;
        synergistMuscleIds = muscleMapping.adjustedSecondary;
        
        this.log('debug', '获取动作肌肉数据成功', {
          exerciseId: apiExercise.exercise_id,
          targetMuscles: exerciseDetail.target_muscles_id,
          synergistMuscles: exerciseDetail.synergist_muscles_id,
          primaryMapped: primary_muscles,
          secondaryMapped: secondary_muscles
        });

        this.log('info', `获取肌肉群数据成功: ${apiExercise.exercise_name}`, {
          primary: primary_muscles,
          secondary: secondary_muscles
        });
      } catch (error) {
        this.log('warn', `获取动作详情失败，使用推断数据: ${apiExercise.exercise_name}`, {
          error: error instanceof Error ? error.message : error,
          exerciseId: apiExercise.exercise_id
        });
        // 降级到推断数据
        const muscleGroups = this.inferMuscleGroupsFromExerciseName(apiExercise.exercise_name);
        primary_muscles = muscleGroups.primary;
        secondary_muscles = muscleGroups.secondary;
      }

      const workoutExercise: WorkoutExercise = {
        id: safeToString(apiExercise.id),
        name: safeToString(apiExercise.exercise_name),
        image_url: processImageUrl(apiExercise.exercise_image),
        sets: safeToNumber(apiExercise.sets),
        reps: typeof apiExercise.reps === 'string'
          ? safeToNumber(apiExercise.reps.split(',')[0])
          : safeToNumber(apiExercise.reps),
        weight: totalWeight / Math.max(setRecords.length, 1), // 平均重量
        rest_time: safeToNumber(apiExercise.rest_seconds),
        primary_muscles: primary_muscles || [],
        secondary_muscles: secondary_muscles || [],
        targetMuscleIds, // 新增：原始目标肌肉ID数组
        synergistMuscleIds, // 新增：原始协同肌肉ID数组
        notes: apiExercise.notes || undefined,
        exercise_type: apiExercise.exercise_type,
        video_url: apiExercise.video_url,
        set_records: setRecords,
        order: apiExercise.order
      };

      this.log('debug', '训练动作转换成功', { 
        input: apiExercise, 
        output: workoutExercise 
      });

      return workoutExercise;
    } catch (error) {
      this.handleError(
        `训练动作转换失败: ${apiExercise.exercise_name} (ID: ${apiExercise.id})`,
        'TRANSFORM_FAILED',
        { apiExercise, error }
      );
      throw error;
    }
  }

  /**
   * 将前端训练动作转换为API训练动作
   * @param exercise 前端训练动作
   * @param workoutId 训练ID（可选）
   * @param exerciseId 动作ID（可选）
   * @returns API训练动作
   */
  toApi(exercise: WorkoutExercise, workoutId?: number, exerciseId?: number): ApiWorkoutExercise {
    this.log('debug', '转换前端训练动作到API', { exercise, workoutId, exerciseId });

    try {
      const apiExercise: ApiWorkoutExercise = {
        id: parseInt(exercise.id),
        workout_id: workoutId || 0, // 需要在上层设置
        exercise_id: exerciseId || 0, // 需要在上层设置
        exercise_name: exercise.name,
        exercise_image: exercise.image_url || '',
        exercise_description: null,
        video_url: exercise.video_url || '',
        sets: exercise.sets,
        reps: safeToString(exercise.reps),
        rest_seconds: exercise.rest_time || 60,
        order: exercise.order || 1,
        notes: exercise.notes || null,
        exercise_type: exercise.exercise_type || 'weight_reps',
        superset_group: null,
        weight: exercise.weight,
        set_records: exercise.set_records?.map(record =>
          setRecordTransformer.toApi(record, parseInt(exercise.id))
        ) || []
      };

      this.log('debug', 'API训练动作转换成功', { 
        input: exercise, 
        output: apiExercise 
      });

      return apiExercise;
    } catch (error) {
      this.handleError(
        `API训练动作转换失败: ${exercise.name} (ID: ${exercise.id})`,
        'TO_API_FAILED',
        { exercise, error }
      );
      throw error;
    }
  }

  /**
   * 从动作名称推断主要肌肉群
   * @param exerciseName 动作名称
   * @returns 推断的肌肉群
   */
  private inferMuscleGroupsFromExerciseName(exerciseName: string): {
    primary: MuscleGroupEnum[];
    secondary: MuscleGroupEnum[];
  } {
    const name = exerciseName.toLowerCase();
    const primary: MuscleGroupEnum[] = [];
    const secondary: MuscleGroupEnum[] = [];

    // 胸部动作
    if (name.includes('卧推') || name.includes('推举') || name.includes('飞鸟')) {
      primary.push(MuscleGroupEnum.CHEST);
      secondary.push(MuscleGroupEnum.TRICEPS, MuscleGroupEnum.SHOULDERS_FRONT);
    }
    // 背部动作
    else if (name.includes('划船') || name.includes('引体') || name.includes('下拉')) {
      primary.push(MuscleGroupEnum.BACK);
      secondary.push(MuscleGroupEnum.BICEPS);
    }
    // 肩部动作
    else if (name.includes('肩') || name.includes('推举')) {
      primary.push(MuscleGroupEnum.SHOULDERS_FRONT);
      secondary.push(MuscleGroupEnum.TRICEPS);
    }
    // 腿部动作
    else if (name.includes('深蹲') || name.includes('腿')) {
      primary.push(MuscleGroupEnum.QUADRICEPS);
      secondary.push(MuscleGroupEnum.GLUTES);
    }
    // 手臂动作
    else if (name.includes('弯举') || name.includes('二头')) {
      primary.push(MuscleGroupEnum.BICEPS);
    }
    else if (name.includes('三头') || name.includes('臂屈伸')) {
      primary.push(MuscleGroupEnum.TRICEPS);
    }
    // 核心动作
    else if (name.includes('卷腹') || name.includes('仰卧起坐') || name.includes('平板支撑')) {
      primary.push(MuscleGroupEnum.ABS);
    }

    // 如果没有匹配到，设置默认值
    if (primary.length === 0) {
      primary.push(MuscleGroupEnum.CHEST); // 默认胸部
    }

    return { primary, secondary };
  }

  /**
   * 验证API训练动作数据
   * @param data API训练动作
   * @returns 是否有效
   */
  validate(data: ApiWorkoutExercise): boolean {
    if (!super.validate(data)) {
      return false;
    }

    // 验证必需字段
    const requiredFields = ['id', 'exercise_name', 'sets', 'reps', 'weight'];
    for (const field of requiredFields) {
      if (!(field in data) || data[field as keyof ApiWorkoutExercise] === null || data[field as keyof ApiWorkoutExercise] === undefined) {
        this.log('warn', `训练动作缺少必需字段: ${field}`, data);
        return false;
      }
    }

    // 验证数值字段
    if (typeof data.sets !== 'number' || data.sets <= 0) {
      this.log('warn', '训练动作组数无效', { sets: data.sets });
      return false;
    }

    if (typeof data.weight !== 'number' || data.weight < 0) {
      this.log('warn', '训练动作重量无效', { weight: data.weight });
      return false;
    }

    // 验证动作名称
    if (typeof data.exercise_name !== 'string' || data.exercise_name.trim() === '') {
      this.log('warn', '训练动作名称无效', { exercise_name: data.exercise_name });
      return false;
    }

    return true;
  }

  /**
   * 验证前端训练动作数据
   * @param exercise 前端训练动作
   * @returns 是否有效
   */
  validateWorkoutExercise(exercise: WorkoutExercise): boolean {
    if (!exercise) {
      return false;
    }

    // 验证必需字段
    const requiredFields = ['id', 'name', 'sets', 'reps', 'weight'];
    for (const field of requiredFields) {
      const value = exercise[field as keyof WorkoutExercise];
      if (value === null || value === undefined) {
        this.log('warn', `前端训练动作缺少必需字段: ${field}`, exercise);
        return false;
      }
    }

    // 验证数值字段
    if (typeof exercise.sets !== 'number' || exercise.sets <= 0) {
      this.log('warn', '前端训练动作组数无效', { sets: exercise.sets });
      return false;
    }

    if (typeof exercise.weight !== 'number' || exercise.weight < 0) {
      this.log('warn', '前端训练动作重量无效', { weight: exercise.weight });
      return false;
    }

    if (typeof exercise.reps !== 'number' || exercise.reps <= 0) {
      this.log('warn', '前端训练动作次数无效', { reps: exercise.reps });
      return false;
    }

    return true;
  }

  /**
   * 计算训练动作的训练量
   * @param exercise 训练动作
   * @returns 训练量
   */
  calculateExerciseVolume(exercise: WorkoutExercise): number {
    if (!this.validateWorkoutExercise(exercise)) {
      return 0;
    }
    return exercise.sets * exercise.weight * exercise.reps;
  }

  /**
   * 获取动作涉及的所有肌肉群
   * @param exercise 训练动作
   * @returns 肌肉群列表
   */
  getInvolvedMuscles(exercise: WorkoutExercise): MuscleGroupEnum[] {
    const muscles = new Set<MuscleGroupEnum>();
    
    if (exercise.primary_muscles) {
      exercise.primary_muscles.forEach(muscle => muscles.add(muscle));
    }
    
    if (exercise.secondary_muscles) {
      exercise.secondary_muscles.forEach(muscle => muscles.add(muscle));
    }
    
    return Array.from(muscles);
  }

  /**
   * 批量转换API训练动作
   * @param apiExercises API训练动作数组
   * @returns 前端训练动作数组
   */
  async transformManyFromApi(apiExercises: ApiWorkoutExercise[]): Promise<WorkoutExercise[]> {
    return await this.transformMany(apiExercises);
  }

  /**
   * 批量转换前端训练动作到API
   * @param exercises 前端训练动作数组
   * @param workoutId 训练ID（可选）
   * @returns API训练动作数组
   */
  transformManyToApi(exercises: WorkoutExercise[], workoutId?: number): ApiWorkoutExercise[] {
    return exercises.map(exercise => this.toApi(exercise, workoutId));
  }
}

/**
 * 创建默认的训练动作转换器实例
 */
export const exerciseTransformer = new ExerciseTransformer({
  enableValidation: true,
  errorStrategy: 'throw',
  logLevel: 'warn'
});

/**
 * 向后兼容的导出对象
 */
export const workoutExerciseTransformerCompat = {
  /**
   * 从API转换（向后兼容）
   */
  async fromApi(apiExercise: ApiWorkoutExercise): Promise<WorkoutExercise> {
    return await exerciseTransformer.transform(apiExercise);
  },

  /**
   * 转换到API（向后兼容）
   */
  toApi(exercise: WorkoutExercise): ApiWorkoutExercise {
    return exerciseTransformer.toApi(exercise);
  }
}; 