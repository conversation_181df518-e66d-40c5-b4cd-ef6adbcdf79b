/**
 * 用户转换器
 * 负责API用户和前端用户之间的数据转换
 */

import { ApiUser } from '../../api/feed/User';
import { FeedUser } from '../../ui/feed/User';
import { AbstractBaseTransformer } from '../base/BaseTransformer';
import { TransformerOptions } from '../../../types/transformers';
import { processImageUrl } from '../base/ImageUrlProcessor';

// 类型安全转换工具函数
function safeToString(value: any): string {
  if (value === null || value === undefined) {
    return '';
  }
  return String(value);
}

/**
 * 用户转换器类
 */
export class UserTransformer extends AbstractBaseTransformer<ApiUser, FeedUser> {
  
  constructor(options?: TransformerOptions) {
    super(options);
  }

  /**
   * 将API用户转换为前端用户
   * @param apiUser API用户
   * @returns 前端用户
   */
  transform(apiUser: ApiUser): FeedUser {
    this.log('debug', '转换API用户', { apiUser });

    try {
      const feedUser: FeedUser = {
        id: safeToString(apiUser.id),
        name: safeToString(apiUser.nickname) || '未知用户',
        username: `user_${safeToString(apiUser.id)}`,
        avatar: processImageUrl(apiUser.avatar_url) || '/api/placeholder/40/40',
        isVerified: false // 暂时设为false，后续可根据需要调整
      };

      this.log('debug', '用户转换成功', { 
        input: apiUser, 
        output: feedUser 
      });

      return feedUser;
    } catch (error) {
      this.handleError(
        `用户转换失败: ID ${apiUser.id}`,
        'TRANSFORM_FAILED',
        { apiUser, error }
      );
      throw error;
    }
  }

  /**
   * 将前端用户转换为API用户
   * @param feedUser 前端用户
   * @returns API用户
   */
  toApi(feedUser: FeedUser): ApiUser {
    this.log('debug', '转换前端用户到API', { feedUser });

    try {
      const apiUser: ApiUser = {
        id: parseInt(feedUser.id),
        nickname: feedUser.name,
        avatar_url: feedUser.avatar,
        gender: 0, // 默认值，性别未知
        activity_level: 3, // 默认活动等级
        completed: false // 默认未完成设置
      };

      this.log('debug', 'API用户转换成功', { 
        input: feedUser, 
        output: apiUser 
      });

      return apiUser;
    } catch (error) {
      this.handleError(
        `API用户转换失败: ID ${feedUser.id}`,
        'TO_API_FAILED',
        { feedUser, error }
      );
      throw error;
    }
  }

  /**
   * 验证API用户数据
   * @param data API用户
   * @returns 是否有效
   */
  validate(data: ApiUser): boolean {
    if (!super.validate(data)) {
      return false;
    }

    // 验证必需字段
    if (typeof data.id !== 'number' || data.id <= 0) {
      this.log('warn', '用户ID无效', { id: data.id });
      return false;
    }

    // nickname可以为空，但如果存在应该是字符串
    if (data.nickname !== null && data.nickname !== undefined && typeof data.nickname !== 'string') {
      this.log('warn', '用户昵称类型无效', { nickname: data.nickname });
      return false;
    }

    // avatar_url可以为空，但如果存在应该是字符串
    if (data.avatar_url !== null && data.avatar_url !== undefined && typeof data.avatar_url !== 'string') {
      this.log('warn', '用户头像URL类型无效', { avatar_url: data.avatar_url });
      return false;
    }

    return true;
  }

  /**
   * 验证前端用户数据
   * @param feedUser 前端用户
   * @returns 是否有效
   */
  validateFeedUser(feedUser: FeedUser): boolean {
    if (!feedUser) {
      return false;
    }

    // 验证必需字段
    const requiredFields = ['id', 'name', 'username'];
    for (const field of requiredFields) {
      const value = feedUser[field as keyof FeedUser];
      if (!value || typeof value !== 'string' || value.trim() === '') {
        this.log('warn', `前端用户缺少必需字段: ${field}`, feedUser);
        return false;
      }
    }

    // 验证ID格式
    const idNum = parseInt(feedUser.id);
    if (isNaN(idNum) || idNum <= 0) {
      this.log('warn', '前端用户ID格式无效', { id: feedUser.id });
      return false;
    }

    return true;
  }

  /**
   * 生成默认用户名
   * @param userId 用户ID
   * @returns 默认用户名
   */
  generateDefaultUsername(userId: string | number): string {
    return `user_${userId}`;
  }

  /**
   * 生成默认头像URL
   * @param userId 用户ID（可选）
   * @returns 默认头像URL
   */
  generateDefaultAvatar(_userId?: string | number): string {
    return '/api/placeholder/40/40';
  }

  /**
   * 更新用户信息（部分更新）
   * @param originalUser 原始用户
   * @param updates 更新内容
   * @returns 更新后的用户
   */
  updateFeedUser(originalUser: FeedUser, updates: Partial<FeedUser>): FeedUser {
    if (!this.validateFeedUser(originalUser)) {
      throw new Error('原始用户数据无效');
    }

    const updatedUser: FeedUser = {
      ...originalUser,
      ...updates,
      // 确保ID不会被意外修改
      id: originalUser.id
    };

    // 处理头像URL
    if (updates.avatar) {
      updatedUser.avatar = processImageUrl(updates.avatar) || originalUser.avatar;
    }

    this.log('debug', '用户信息更新', { 
      original: originalUser, 
      updates, 
      result: updatedUser 
    });

    return updatedUser;
  }

  /**
   * 检查用户是否已验证
   * @param feedUser 前端用户
   * @returns 是否已验证
   */
  isUserVerified(feedUser: FeedUser): boolean {
    return Boolean(feedUser.isVerified);
  }

  /**
   * 获取用户显示名称
   * @param feedUser 前端用户
   * @returns 显示名称
   */
  getDisplayName(feedUser: FeedUser): string {
    if (!feedUser.name || feedUser.name.trim() === '') {
      return feedUser.username || this.generateDefaultUsername(feedUser.id);
    }
    return feedUser.name;
  }

  /**
   * 批量转换API用户
   * @param apiUsers API用户数组
   * @returns 前端用户数组
   */
  transformManyFromApi(apiUsers: ApiUser[]): FeedUser[] {
    return this.transformMany(apiUsers);
  }

  /**
   * 批量转换前端用户到API
   * @param feedUsers 前端用户数组
   * @returns API用户数组
   */
  transformManyToApi(feedUsers: FeedUser[]): ApiUser[] {
    return feedUsers.map(feedUser => this.toApi(feedUser));
  }
}

/**
 * 创建默认的用户转换器实例
 */
export const userTransformer = new UserTransformer({
  enableValidation: true,
  errorStrategy: 'throw',
  logLevel: 'warn'
});

/**
 * 向后兼容的导出对象
 */
export const userTransformerCompat = {
  /**
   * 从API转换（向后兼容）
   */
  fromApi(apiUser: ApiUser): FeedUser {
    return userTransformer.transform(apiUser);
  },

  /**
   * 转换到API（向后兼容）
   */
  toApi(user: FeedUser): ApiUser {
    return userTransformer.toApi(user);
  }
}; 