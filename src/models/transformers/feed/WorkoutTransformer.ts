/**
 * 训练详情转换器
 * 负责API训练详情和前端训练数据之间的转换，集成肌肉强度计算
 */

import { ApiWorkoutDetail } from '../../api/feed/Workout';
import { WorkoutData } from '../../ui/exercise/Workout';
import { WorkoutExercise } from '../../ui/exercise/Exercise';
import { AbstractAsyncBaseTransformer } from '../base/BaseTransformer';
import { TransformerOptions } from '../../../types/transformers';
import { exerciseTransformer } from '../exercise/ExerciseTransformer';
import { calculateMuscleIntensities } from '../../../utils/muscle/MuscleIntensityCalculator';

// 类型安全转换工具函数
function safeToString(value: any): string {
  if (value === null || value === undefined) {
    return '';
  }
  return String(value);
}

function safeToNumber(value: any, defaultValue: number = 0): number {
  if (value === null || value === undefined) {
    return defaultValue;
  }
  const num = Number(value);
  return isNaN(num) ? defaultValue : num;
}

/**
 * 训练详情转换器类
 */
export class WorkoutTransformer extends AbstractAsyncBaseTransformer<ApiWorkoutDetail, WorkoutData> {
  
  constructor(options?: TransformerOptions) {
    super(options);
  }

  /**
   * 将API训练详情转换为前端训练数据
   * @param apiWorkoutDetail API训练详情
   * @returns 前端训练数据
   */
  async transform(apiWorkoutDetail: ApiWorkoutDetail): Promise<WorkoutData> {
    this.log('debug', '转换API训练详情', { apiWorkoutDetail });

    try {
      // 转换所有训练动作
      const exercises = await Promise.all(
        apiWorkoutDetail.workout_exercises.map(ex =>
          exerciseTransformer.transform(ex)
        )
      );

      // 计算总数据
      const totalSets = exercises.reduce((sum, ex) => sum + safeToNumber(ex.sets), 0);
      const totalVolume = exercises.reduce((sum, ex) => {
        const reps = typeof ex.reps === 'number' ? ex.reps : 10; // 默认值
        return sum + (safeToNumber(ex.weight) * safeToNumber(ex.sets) * reps);
      }, 0);

      // 计算持续时间（秒）
      const durationSeconds = this.calculateWorkoutDuration(
        apiWorkoutDetail.start_time,
        apiWorkoutDetail.end_time,
        apiWorkoutDetail.actual_duration
      );

      // 计算肌肉强度
      const muscleIntensities = calculateMuscleIntensities(exercises);
      
      // 整合肌肉ID数据（去重处理）
      const { targetMuscleIds, synergistMuscleIds } = this.aggregateWorkoutMuscles(exercises);

      // 估算消耗的卡路里
      const caloriesBurned = this.estimateCaloriesBurned(totalVolume, durationSeconds);

      const workoutData: WorkoutData = {
        id: safeToString(apiWorkoutDetail.id),
        name: safeToString(apiWorkoutDetail.name),
        duration_seconds: durationSeconds,
        total_sets: totalSets,
        total_volume: totalVolume,
        calories_burned: caloriesBurned,
        exercises,
        muscle_intensities: muscleIntensities,
        targetMuscleIds, // 新增：整合的目标肌肉ID
        synergistMuscleIds, // 新增：整合的协同肌肉ID
        created_at: apiWorkoutDetail.created_at,
        status: apiWorkoutDetail.status,
        start_time: apiWorkoutDetail.start_time,
        end_time: apiWorkoutDetail.end_time
      };

      this.log('debug', '训练详情转换成功', { 
        input: apiWorkoutDetail, 
        output: workoutData,
        exerciseCount: exercises.length,
        totalSets,
        totalVolume,
        durationMinutes: Math.round(durationSeconds / 60)
      });

      return workoutData;
    } catch (error) {
      this.handleError(
        `训练详情转换失败: ${apiWorkoutDetail.name} (ID: ${apiWorkoutDetail.id})`,
        'TRANSFORM_FAILED',
        { apiWorkoutDetail, error }
      );
      throw error;
    }
  }

  /**
   * 计算训练持续时间
   * @param startTime 开始时间
   * @param endTime 结束时间
   * @param actualDuration 实际持续时间（可选）
   * @returns 持续时间（秒）
   */
  private calculateWorkoutDuration(
    startTime: string,
    endTime: string,
    actualDuration?: number
  ): number {
    try {
      const start = new Date(startTime);
      const end = new Date(endTime);
      
      // 验证日期有效性
      if (isNaN(start.getTime()) || isNaN(end.getTime())) {
        this.log('warn', '训练时间格式无效，使用实际持续时间或默认值', {
          startTime, endTime, actualDuration
        });
        return actualDuration || 3600; // 默认1小时
      }

      const calculatedDuration = Math.floor((end.getTime() - start.getTime()) / 1000);
      
      // 使用最大值，确保合理性
      const finalDuration = Math.max(
        calculatedDuration,
        actualDuration || 0,
        0 // 最小值为0
      );

      this.log('debug', '训练持续时间计算', {
        startTime,
        endTime,
        calculatedDuration,
        actualDuration,
        finalDuration
      });

      return finalDuration;
    } catch (error) {
      this.log('warn', '计算训练持续时间失败，使用默认值', { error });
      return actualDuration || 3600; // 默认1小时
    }
  }

  /**
   * 估算消耗的卡路里
   * @param totalVolume 总训练量
   * @param durationSeconds 持续时间（秒）
   * @returns 估算卡路里
   */
  private estimateCaloriesBurned(totalVolume: number, durationSeconds: number): number {
    // 简单的卡路里估算公式
    // 基于训练量和时间的综合计算
    const volumeCalories = totalVolume * 0.1; // 每单位训练量0.1卡路里
    const timeCalories = (durationSeconds / 60) * 5; // 每分钟5卡路里基础代谢
    
    const totalCalories = Math.floor(volumeCalories + timeCalories);
    
    this.log('debug', '卡路里估算', {
      totalVolume,
      durationMinutes: Math.round(durationSeconds / 60),
      volumeCalories,
      timeCalories,
      totalCalories
    });

    return Math.max(totalCalories, 0); // 确保非负数
  }

  /**
   * 验证API训练详情数据
   * @param data API训练详情
   * @returns 是否有效
   */
  validate(data: ApiWorkoutDetail): boolean {
    if (!super.validate(data)) {
      return false;
    }

    // 验证必需字段
    const requiredFields = ['id', 'name', 'workout_exercises'];
    for (const field of requiredFields) {
      if (!(field in data) || data[field as keyof ApiWorkoutDetail] === null || data[field as keyof ApiWorkoutDetail] === undefined) {
        this.log('warn', `训练详情缺少必需字段: ${field}`, data);
        return false;
      }
    }

    // 验证训练名称
    if (typeof data.name !== 'string' || data.name.trim() === '') {
      this.log('warn', '训练名称无效', { name: data.name });
      return false;
    }

    // 验证训练动作列表
    if (!Array.isArray(data.workout_exercises)) {
      this.log('warn', '训练动作列表无效', { workout_exercises: data.workout_exercises });
      return false;
    }

    // 验证时间格式（如果提供）
    if (data.start_time && data.end_time) {
      const startTime = new Date(data.start_time);
      const endTime = new Date(data.end_time);
      
      if (isNaN(startTime.getTime()) || isNaN(endTime.getTime())) {
        this.log('warn', '训练时间格式无效', { 
          start_time: data.start_time, 
          end_time: data.end_time 
        });
        return false;
      }

      if (endTime.getTime() < startTime.getTime()) {
        this.log('warn', '结束时间早于开始时间', { 
          start_time: data.start_time, 
          end_time: data.end_time 
        });
        return false;
      }
    }

    return true;
  }

  /**
   * 验证前端训练数据
   * @param workoutData 前端训练数据
   * @returns 是否有效
   */
  validateWorkoutData(workoutData: WorkoutData): boolean {
    if (!workoutData) {
      return false;
    }

    // 验证必需字段
    const requiredFields = ['id', 'name', 'exercises'];
    for (const field of requiredFields) {
      const value = workoutData[field as keyof WorkoutData];
      if (!value) {
        this.log('warn', `前端训练数据缺少必需字段: ${field}`, workoutData);
        return false;
      }
    }

    // 验证训练动作列表
    if (!Array.isArray(workoutData.exercises)) {
      this.log('warn', '前端训练动作列表无效', { exercises: workoutData.exercises });
      return false;
    }

    // 验证数值字段
    const numericFields = ['duration_seconds', 'total_sets', 'total_volume', 'calories_burned'];
    for (const field of numericFields) {
      const value = workoutData[field as keyof WorkoutData];
      if (typeof value !== 'number' || value < 0) {
        this.log('warn', `前端训练数据数值字段无效: ${field}`, { [field]: value });
        return false;
      }
    }

    return true;
  }

  /**
   * 生成训练摘要
   * @param workoutData 训练数据
   * @returns 训练摘要
   */
  generateWorkoutSummary(workoutData: WorkoutData): {
    totalExercises: number;
    totalSets: number;
    totalVolume: number;
    durationMinutes: number;
    mainMuscleGroups: string[];
    intensityLevel: string;
  } {
    const durationMinutes = Math.round(workoutData.duration_seconds / 60);
    
    // 获取主要肌肉群
    const mainMuscleGroups = workoutData.muscle_intensities
      ?.filter(intensity => intensity.isPrimary)
      .slice(0, 3) // 取前3个
      .map(intensity => intensity.muscle) || [];

    // 简单的强度评估
    let intensityLevel = 'low';
    if (workoutData.total_volume > 5000) {
      intensityLevel = 'high';
    } else if (workoutData.total_volume > 2000) {
      intensityLevel = 'medium';
    }

    return {
      totalExercises: workoutData.exercises.length,
      totalSets: workoutData.total_sets,
      totalVolume: workoutData.total_volume,
      durationMinutes,
      mainMuscleGroups,
      intensityLevel
    };
  }

  /**
   * 计算训练效率指标
   * @param workoutData 训练数据
   * @returns 效率指标
   */
  calculateEfficiencyMetrics(workoutData: WorkoutData): {
    volumePerMinute: number;
    setsPerMinute: number;
    avgRestTime: number;
    muscleGroupCoverage: number;
  } {
    const durationMinutes = workoutData.duration_seconds / 60;
    
    const volumePerMinute = durationMinutes > 0 ? workoutData.total_volume / durationMinutes : 0;
    const setsPerMinute = durationMinutes > 0 ? workoutData.total_sets / durationMinutes : 0;
    
    // 估算平均休息时间
    const totalExerciseTime = workoutData.exercises.reduce((sum, ex) => {
      const estimatedSetTime = 30; // 假设每组30秒
      return sum + (ex.sets * estimatedSetTime);
    }, 0);
    
    const totalRestTime = workoutData.duration_seconds - totalExerciseTime;
    const avgRestTime = workoutData.total_sets > 0 ? totalRestTime / workoutData.total_sets : 0;
    
    // 肌肉群覆盖率
    const uniqueMuscles = new Set<string>();
    workoutData.exercises.forEach(ex => {
      ex.primary_muscles?.forEach(muscle => uniqueMuscles.add(muscle));
      ex.secondary_muscles?.forEach(muscle => uniqueMuscles.add(muscle));
    });
    const muscleGroupCoverage = uniqueMuscles.size;

    return {
      volumePerMinute: Math.round(volumePerMinute),
      setsPerMinute: Math.round(setsPerMinute * 100) / 100,
      avgRestTime: Math.round(avgRestTime),
      muscleGroupCoverage
    };
  }

  /**
   * 批量转换API训练详情
   * @param apiWorkouts API训练详情数组
   * @returns 前端训练数据数组
   */
  async transformManyFromApi(apiWorkouts: ApiWorkoutDetail[]): Promise<WorkoutData[]> {
    return await this.transformMany(apiWorkouts);
  }

  /**
   * 整合训练中所有动作的肌肉ID数据
   * @param exercises 训练动作数组
   * @returns 整合后的肌肉ID数据
   */
  private aggregateWorkoutMuscles(exercises: WorkoutExercise[]): {
    targetMuscleIds: number[];
    synergistMuscleIds: number[];
  } {
    const allTargetIds: number[] = [];
    const allSynergistIds: number[] = [];
    
    // 收集所有肌肉ID
    exercises.forEach(exercise => {
      if (exercise.targetMuscleIds) {
        allTargetIds.push(...exercise.targetMuscleIds);
      }
      if (exercise.synergistMuscleIds) {
        allSynergistIds.push(...exercise.synergistMuscleIds);
      }
    });
    
    // 去重目标肌肉
    const uniqueTargetIds = [...new Set(allTargetIds)];
    
    // 去重协同肌肉，并排除与目标肌肉重复的部分（target优先）
    const uniqueSynergistIds = [...new Set(allSynergistIds)]
      .filter(id => !uniqueTargetIds.includes(id));
    
    this.log('debug', '肌肉数据整合结果', {
      totalExercises: exercises.length,
      rawTargetIds: allTargetIds.length,
      rawSynergistIds: allSynergistIds.length,
      uniqueTargetIds: uniqueTargetIds.length,
      uniqueSynergistIds: uniqueSynergistIds.length,
      finalTarget: uniqueTargetIds,
      finalSynergist: uniqueSynergistIds
    });
    
    return {
      targetMuscleIds: uniqueTargetIds,
      synergistMuscleIds: uniqueSynergistIds
    };
  }
}

/**
 * 创建默认的训练详情转换器实例
 */
export const workoutTransformer = new WorkoutTransformer({
  enableValidation: true,
  errorStrategy: 'throw',
  logLevel: 'warn'
});

/**
 * 向后兼容的导出对象
 */
export const workoutDetailTransformerCompat = {
  /**
   * 从API转换（向后兼容）
   */
  async fromApi(apiWorkoutDetail: ApiWorkoutDetail): Promise<WorkoutData> {
    return await workoutTransformer.transform(apiWorkoutDetail);
  }
}; 