import { ApiFeedPost } from '../../api/feed/Post';
import { FeedPost, FeedPostContent, FeedPostStats } from '../../ui/feed/Post';
import { CarouselItem } from '../../ui/common/Carousel';
import { AbstractAsyncBaseTransformer } from '../base/BaseTransformer';
import { userTransformer } from './UserTransformer';
import { workoutTransformer } from './WorkoutTransformer';
import { processMultipleImages } from '../base/ImageUrlProcessor';

// 类型安全转换工具函数
function safeToString(value: any): string {
  if (value === null || value === undefined) {
    return '';
  }
  return String(value);
}

function safeToNumber(value: any, defaultValue: number = 0): number {
  if (value === null || value === undefined) {
    return defaultValue;
  }
  const num = Number(value);
  return isNaN(num) ? defaultValue : num;
}

// 安全处理API数据的递归函数
function safeProcessApiData(data: any): any {
  if (data === null || data === undefined) {
    return data;
  }

  if (typeof data === 'object' && !Array.isArray(data)) {
    const processed: any = {};
    for (const [key, value] of Object.entries(data)) {
      processed[key] = safeProcessApiData(value);
    }
    return processed;
  }

  if (Array.isArray(data)) {
    return data.map(item => safeProcessApiData(item));
  }

  return data;
}

/**
 * Feed Post 转换器
 * 处理 API Feed Post 数据到前端格式的转换
 */
export class FeedPostTransformer extends AbstractAsyncBaseTransformer<ApiFeedPost, FeedPost> {

  /**
   * 安全处理API数据
   */
  private safeProcessApiData(data: any): any {
    return safeProcessApiData(data);
  }

  /**
   * 将 API Feed Post 转换为前端 Feed Post 格式
   * @param apiFeedPost API Feed Post 数据
   * @returns Promise<FeedPost> 前端 Feed Post 数据
   */
  async transform(apiFeedPost: ApiFeedPost): Promise<FeedPost> {
    try {
      // 安全处理输入数据，防止类型转换错误
      const safeApiFeedPost = this.safeProcessApiData(apiFeedPost);
      this.log('info', 'Transforming API feed post to frontend format', { id: safeApiFeedPost.id });

      // 转换用户信息
      const user = userTransformer.transform(safeApiFeedPost.user);

      // 处理内容部分
      const content = await this.transformContent(safeApiFeedPost);

      // 处理统计信息（使用安全转换）
      const stats: FeedPostStats = {
        likes: safeToNumber(safeApiFeedPost.like_count),
        comments: safeToNumber(safeApiFeedPost.comment_count),
        shares: safeToNumber(safeApiFeedPost.share_count),
        views: safeToNumber(safeApiFeedPost.view_count),
        reports: safeToNumber(safeApiFeedPost.reported_count),
      };

      // 处理图片URL
      const processedImages = processMultipleImages(safeApiFeedPost.images || []);
      const images = processedImages.success.map(result => result.url).filter((url): url is string => url !== undefined);

      // 创建 FeedPost 对象（使用安全转换）
      const feedPost: FeedPost = {
        id: safeToString(safeApiFeedPost.id),
        title: safeToString(safeApiFeedPost.title),
        user,
        content,
        stats,
        isLiked: apiFeedPost.is_liked_by_current_user || false,
        timestamp: new Date(apiFeedPost.created_at),
        visibility: this.inferVisibility(apiFeedPost),
        tags: apiFeedPost.tags || [],
        status: apiFeedPost.status,
        images: images.length > 0 ? images : undefined,
      };

      this.log('info', 'Successfully transformed feed post', { 
        id: feedPost.id, 
        hasWorkout: !!content.workout,
        imageCount: images.length 
      });

      return feedPost;
    } catch (error) {
      this.handleError('Failed to transform feed post', 'TRANSFORM_ERROR', { 
        apiFeedPost: apiFeedPost.id,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  /**
   * 转换帖子内容
   * @param apiFeedPost API Feed Post 数据
   * @returns Promise<FeedPostContent> 前端内容格式
   */
  private async transformContent(apiFeedPost: ApiFeedPost): Promise<FeedPostContent> {
    const content: FeedPostContent = {
      text: apiFeedPost.content || undefined,
    };

    // 如果有关联的训练详情，转换为 WorkoutData
    if (apiFeedPost.related_workout_detail) {
      try {
        content.workout = await workoutTransformer.transform(apiFeedPost.related_workout_detail);
      } catch (error) {
        this.log('warn', 'Failed to transform workout detail, skipping', { 
          workoutId: apiFeedPost.related_workout_detail.id,
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }

         // 处理图片
     if (apiFeedPost.images && apiFeedPost.images.length > 0) {
       const processedImages = processMultipleImages(apiFeedPost.images);
       content.images = processedImages.success.map(result => result.url).filter((url): url is string => url !== undefined);
     }

    // 如果有训练数据，创建轮播项目
    if (content.workout) {
      content.carousel_items = this.createCarouselItems(content.workout, content.images);
    }

    return content;
  }

  /**
   * 创建轮播项目
   * @param workout 训练数据
   * @param userImages 用户图片
   * @returns CarouselItem[] 轮播项目数组
   */
  private createCarouselItems(workout: any, userImages?: string[]): CarouselItem[] {
    const items: CarouselItem[] = [];

    // 添加训练数据项目
    if (workout && workout.exercises && workout.muscle_intensities) {
      items.push({
        id: `workout-${workout.id}`,
        type: 'workout_data',
        content: {
          workout_data: {
            exercises: workout.exercises,
            selectedMuscles: workout.muscle_intensities.map((intensity: any) => intensity.muscle),
            muscleColorConfig: this.generateMuscleColorConfig(workout.muscle_intensities),
            intensities: workout.muscle_intensities,
          }
        }
      });
    }

    // 添加用户图片项目
    if (userImages && userImages.length > 0) {
      userImages.forEach((imageUrl, index) => {
        items.push({
          id: `user-image-${index}`,
          type: 'user_image',
          content: {
            image_data: {
              url: imageUrl,
              alt: `User uploaded image ${index + 1}`,
              caption: undefined,
            }
          }
        });
      });
    }

    return items;
  }

  /**
   * 生成肌肉颜色配置
   * @param intensities 肌肉强度数组
   * @returns 肌肉颜色配置对象
   */
  private generateMuscleColorConfig(intensities: any[]): { [key: string]: 'primary' | 'secondary' } {
    const config: { [key: string]: 'primary' | 'secondary' } = {};
    
    intensities.forEach(intensity => {
      if (intensity.muscle) {
        config[intensity.muscle] = intensity.isPrimary ? 'primary' : 'secondary';
      }
    });

    return config;
  }

  /**
   * 推断帖子可见性
   * @param apiFeedPost API Feed Post 数据
   * @returns 可见性设置
   */
  private inferVisibility(apiFeedPost: ApiFeedPost): 'everyone' | 'friends' | 'private' {
    // 基于状态推断可见性，这可以根据实际业务逻辑调整
    if (apiFeedPost.status === 'HIDDEN') {
      return 'private';
    }
    // 默认为公开
    return 'everyone';
  }

  /**
   * 将前端 FeedPost 转换为 API 格式（用于创建/更新）
   * @param feedPost 前端 Feed Post 数据
   * @returns Partial<ApiFeedPost> API Feed Post 数据（部分字段）
   */
  toApi(feedPost: FeedPost): Partial<ApiFeedPost> {
    const apiPost: Partial<ApiFeedPost> = {
      title: feedPost.title,
      content: feedPost.content.text || '',
      image_urls: feedPost.images || [],
      tags: feedPost.tags,
      status: feedPost.status || 'ACTIVE',
    };

    // 如果有关联的训练数据
    if (feedPost.content.workout) {
      apiPost.related_workout_id = feedPost.content.workout.id ? Number(feedPost.content.workout.id) : undefined;
    }

    return apiPost;
  }

  /**
   * 验证 API Feed Post 数据
   * @param data API Feed Post 数据
   * @returns 是否有效
   */
  validate(data: ApiFeedPost): boolean {
    if (!data) {
      this.log('error', 'Feed post data is null or undefined');
      return false;
    }

    const requiredFields = ['id', 'user', 'created_at'];
    for (const field of requiredFields) {
      if (!(field in data) || data[field as keyof ApiFeedPost] === null || data[field as keyof ApiFeedPost] === undefined) {
        this.log('error', `Missing required field: ${field}`, { data });
        return false;
      }
    }

    // 验证用户数据
    if (!data.user || typeof data.user !== 'object') {
      this.log('error', 'Invalid user data in feed post', { data });
      return false;
    }

    return true;
  }

  /**
   * 验证前端 FeedPost 数据
   * @param feedPost 前端 Feed Post 数据
   * @returns 验证结果
   */
  validateFeedPost(feedPost: FeedPost): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!feedPost.id) {
      errors.push('Missing feed post ID');
    }

    if (!feedPost.user || !feedPost.user.id) {
      errors.push('Missing or invalid user data');
    }

    if (!feedPost.content || (typeof feedPost.content !== 'object')) {
      errors.push('Missing or invalid content data');
    }

    if (!feedPost.timestamp || !(feedPost.timestamp instanceof Date)) {
      errors.push('Missing or invalid timestamp');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * 批量转换 API Feed Posts
   * @param apiFeedPosts API Feed Post 数组
   * @returns Promise<FeedPost[]> 前端 Feed Post 数组
   */
  async transformMany(apiFeedPosts: ApiFeedPost[]): Promise<FeedPost[]> {
    this.log('info', `Starting batch transformation of ${apiFeedPosts.length} feed posts`);
    
    const results = await Promise.allSettled(
      apiFeedPosts.map(post => this.transform(post))
    );

    const successfulResults = results
      .filter((result): result is PromiseFulfilledResult<FeedPost> => result.status === 'fulfilled')
      .map(result => result.value);

    const failedCount = results.length - successfulResults.length;
    if (failedCount > 0) {
      this.log('warn', `${failedCount} feed posts failed to transform`);
    }

    this.log('info', `Successfully transformed ${successfulResults.length} out of ${apiFeedPosts.length} feed posts`);
    return successfulResults;
  }
}

// 导出默认实例
export const feedPostTransformer = new FeedPostTransformer({
  logLevel: 'info',
  enableValidation: true,
  errorStrategy: 'throw'
});

// 向后兼容性对象
export const feedPostTransformerCompat = {
  // 新方法名称
  transformApiFeedPost: (data: ApiFeedPost) => feedPostTransformer.transform(data),
  transformApiFeedPosts: (data: ApiFeedPost[]) => feedPostTransformer.transformMany(data),
  validateFeedPost: (data: FeedPost) => feedPostTransformer.validateFeedPost(data),
  
  // 旧方法名称兼容
  transform: (data: ApiFeedPost) => feedPostTransformer.transform(data),
  transformMany: (data: ApiFeedPost[]) => feedPostTransformer.transformMany(data),
  validate: (data: ApiFeedPost) => feedPostTransformer.validate(data),
}; 