/**
 * WorkoutDetail 数据转换器
 * 
 * @fileoverview 处理不同数据源到UIWorkoutDetail的转换
 * <AUTHOR> Team
 * @since 1.0.0
 */

import { UIWorkoutDetail, UITrainingExercise } from '../../ui/workout';
import { WorkoutExercise } from '../../ui/exercise/Exercise';
import { FeedPost } from '../../ui/feed/Post';
import { ApiWorkoutResponse } from '../../../services/workout/types';

export class WorkoutDetailTransformer {
  /**
   * 将Feed帖子详情转换为UI模型
   */
  static transformPostDetailToUIModel(
    feedPost: FeedPost
  ): UIWorkoutDetail {
    const workoutData = feedPost.content.workout;
    
    if (!workoutData) {
      throw new Error('Feed帖子中缺少训练数据');
    }

    return {
      id: feedPost.id,
      name: workoutData.name,
      source: 'feed',
      
      socialInfo: {
        user: {
          id: feedPost.user.id,
          name: feedPost.user.name,
          avatar: feedPost.user.avatar,
          isFollowing: feedPost.user.isFollowing || false,
          isVerified: feedPost.user.isVerified || false
        },
        post: {
          id: feedPost.id,
          content: feedPost.content.text || '',
          timestamp: feedPost.timestamp.toISOString(),
          images: feedPost.images || []
        },
        stats: {
          likes: feedPost.stats.likes,
          comments: feedPost.stats.comments,
          views: feedPost.stats.views || 0,
          isLiked: feedPost.isLiked
        }
      },
      
      workoutStats: {
        id: feedPost.id,
        name: workoutData.name,
        duration_seconds: workoutData.duration_seconds,
        total_sets: workoutData.total_sets,
        total_volume: workoutData.total_volume,
        calories_burned: workoutData.calories_burned,
        exercises: this.convertToUIWorkoutExercises(workoutData.exercises || []),
        targetMuscleIds: workoutData.targetMuscleIds || [],
        synergistMuscleIds: workoutData.synergistMuscleIds || [],
        created_at: feedPost.timestamp.toISOString(),
        status: 'completed'
      },
      exercises: this.transformWorkoutExercisesToUI(workoutData.exercises || []),
      muscleData: {
        targetMuscleIds: workoutData.targetMuscleIds || [],
        synergistMuscleIds: workoutData.synergistMuscleIds || []
      },
      
      metadata: {
        createdAt: feedPost.timestamp.toISOString(),
        updatedAt: feedPost.timestamp.toISOString(),
        status: 'completed',
        visibility: feedPost.visibility === 'everyone' ? 'public' : 'private'
      }
    };
  }
  
  /**
   * 将训练详情响应转换为UI模型
   */
  static transformWorkoutResponseToUIModel(
    apiWorkout: ApiWorkoutResponse
  ): UIWorkoutDetail {
    // 从实际的 ApiWorkoutResponse 构建基础数据
    const exercises = apiWorkout.exercises || [];
    const statistics = apiWorkout.statistics;
    
    return {
      id: apiWorkout.id.toString(),
      name: `训练 ${apiWorkout.id}`, // 使用默认名称，因为 ApiWorkoutResponse 中没有 name 字段
      source: 'profile',
      
      // profile场景不包含社交信息
      socialInfo: undefined,
      
      workoutStats: {
        id: apiWorkout.id.toString(),
        name: `训练 ${apiWorkout.id}`,
        duration_seconds: statistics?.actual_duration || 0,
        total_sets: exercises.reduce((sum, ex) => sum + (ex.sets?.length || 0), 0),
        total_volume: exercises.reduce((sum, ex) => {
          const exerciseVolume = (ex.sets || []).reduce((vol, set) => vol + (set.weight || 0) * (set.reps || 0), 0);
          return sum + exerciseVolume;
        }, 0),
        calories_burned: undefined,
        exercises: this.convertToUIWorkoutExercises(exercises),
        targetMuscleIds: [],
        synergistMuscleIds: [],
        created_at: apiWorkout.created_at,
        status: apiWorkout.status as any
      },
      exercises: this.transformWorkoutExercisesToUI(exercises),
      muscleData: {
        targetMuscleIds: [],
        synergistMuscleIds: []
      },
      
      metadata: {
        createdAt: apiWorkout.created_at,
        updatedAt: apiWorkout.updated_at,
        status: apiWorkout.status === 'completed' ? 'completed' : 'in_progress',
        visibility: 'private' // Profile中的训练默认私有
      }
    };
  }
  
  /**
   * 将服务层的 WorkoutExercise 转换为 UI 层的 WorkoutExercise
   */
  private static convertToUIWorkoutExercises(exercises: any[]): WorkoutExercise[] {
    return exercises.map((exercise, index) => ({
      id: exercise.id?.toString() || index.toString(),
      name: exercise.name || exercise.exercise_name || '未知动作',
      image_url: exercise.image_url || exercise.exercise_image,
      sets: exercise.sets || 0,
      reps: exercise.reps || 0,
      weight: exercise.weight || 0,
      rest_time: exercise.rest_time || exercise.rest_seconds,
      primary_muscles: [],
      secondary_muscles: [],
      targetMuscleIds: [],
      synergistMuscleIds: [],
      notes: exercise.notes,
      exercise_type: exercise.exercise_type || 'weight_reps',
      set_records: exercise.set_records || [],
      order: exercise.order || index
    }));
  }

  /**
   * 转换已有的WorkoutExercise到UI模型
   */
  private static transformWorkoutExercisesToUI(
    exercises: any[]
  ): UITrainingExercise[] {
    return exercises.map((exercise, index) => ({
      id: exercise.id?.toString() || index.toString(),
      name: exercise.name || exercise.exercise_name || '未知动作',
      imageUrl: exercise.image_url || exercise.exercise_image,
      description: exercise.description,
      order: exercise.order || index,
      
      sets: exercise.sets || 0,
      targetReps: exercise.reps || 0,
      targetWeight: exercise.weight || 0,
      restSeconds: exercise.rest_time || exercise.rest_seconds || 0,
      
      setRecords: (exercise.set_records || []).map((record: any, recordIndex: number) => ({
        id: record.id?.toString() || recordIndex.toString(),
        setNumber: record.set_number || recordIndex + 1,
        setType: record.set_type || 'normal',
        weight: record.weight || 0,
        reps: record.reps || 0,
        completed: record.completed || false,
        notes: record.notes,
        duration: record.duration,
        distance: record.distance
      })),
      
      totalVolume: (exercise.sets || 0) * (exercise.reps || 0) * (exercise.weight || 0),
      completedSets: exercise.sets || 0,
      averageWeight: exercise.weight || 0,
      
      notes: exercise.notes,
      exerciseType: exercise.exercise_type || 'weight_reps'
    }));
  }
}