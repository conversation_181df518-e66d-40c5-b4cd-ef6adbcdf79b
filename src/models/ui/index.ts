/**
 * UI模型统一导出
 * 
 * @fileoverview 所有UI数据模型的统一导出入口
 * <AUTHOR> Team
 * @since 1.0.0
 */

// Feed相关UI模型
export type { FeedUser, UserStats, FollowStatus } from './feed/User';
export type { FeedPost, FeedPostContent, FeedPostStats } from './feed/Post';

// Exercise相关UI模型
export type { WorkoutData, MuscleGroupIntensity } from './exercise/Workout';
export type { WorkoutExercise, ExerciseListProps } from './exercise/Exercise';
export type { SetRecord } from './exercise/SetRecord';

export type {
  ExerciseDetailState,
  MuscleGroupInfo,
  VideoPlayerConfig,
  InstructionStep,
  ExerciseTip
} from './exercise/ExerciseDetail';

// 通用UI模型
export type { 
  CarouselItem, 
  WorkoutCarouselProps, 
  MuscleIllustrationCardProps 
} from './common/Carousel';
export type { CreatePostRequest, ShareWorkoutRequest } from './common/Request';
export type { DataValidationResult } from './common/Validation';

// WorkoutDetail相关UI模型
export type { UIWorkoutDetail } from './workout/WorkoutDetail';
export type { UITrainingExercise, UISetRecord } from './workout/TrainingExercise';
