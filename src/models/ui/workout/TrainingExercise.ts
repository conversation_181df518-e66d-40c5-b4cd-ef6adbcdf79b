/**
 * TrainingExercise UI 数据模型
 * 
 * @fileoverview 训练动作详情的UI数据模型
 * <AUTHOR> Team
 * @since 1.0.0
 */

/**
 * 组记录UI数据模型
 */
export interface UISetRecord {
  id: string;
  setNumber: number;
  setType: 'normal' | 'warmup' | 'dropset' | 'failure';
  weight: number;
  reps: number;
  completed: boolean;
  notes?: string;
  duration?: number;  // 秒，用于时间类动作
  distance?: number;  // 米，用于距离类动作
}

/**
 * 训练动作UI数据模型
 * 专门用于WorkoutDetail页面的动作展示
 */
export interface UITrainingExercise {
  id: string;
  name: string;
  imageUrl?: string;
  description?: string;
  order: number;
  
  // 基础参数
  sets: number;
  targetReps: number;
  targetWeight: number;
  restSeconds: number;
  
  // 执行记录
  setRecords: UISetRecord[];
  
  // 统计信息
  totalVolume: number;      // 总重量
  completedSets: number;    // 完成组数
  averageWeight: number;    // 平均重量
  
  // 其他信息
  notes?: string;
  exerciseType: 'weight_reps' | 'time' | 'distance';
  superset?: string;
}