/**
 * WorkoutDetail UI 数据模型
 * 
 * @fileoverview WorkoutDetail页面专用的UI数据模型
 * <AUTHOR> Team
 * @since 1.0.0
 */

import { WorkoutData } from '../exercise/Workout';
import type { UITrainingExercise } from './TrainingExercise';

/**
 * 统一的训练详情UI数据模型
 * 支持两种业务场景：Feed社交场景和Profile纯数据场景
 */
export interface UIWorkoutDetail {
  // 基础信息
  id: string;
  name: string;
  source: 'feed' | 'profile';
  
  // 社交信息（仅feed场景）
  socialInfo?: {
    user: {
      id: string;
      name: string;
      avatar: string;
      isFollowing: boolean;
      isVerified: boolean;
    };
    post: {
      id: string;
      content: string;
      timestamp: string;
      images?: string[];
    };
    stats: {
      likes: number;
      comments: number;
      views: number;
      isLiked: boolean;
    };
  };
  
  // 训练统计（共用）
  workoutStats: WorkoutData;
  
  // 训练动作（共用）
  exercises: UITrainingExercise[];
  
  // 肌肉数据（共用）
  muscleData: {
    targetMuscleIds: number[];
    synergistMuscleIds: number[];
  };
  
  // 元数据
  metadata: {
    createdAt: string;
    updatedAt: string;
    status: 'completed' | 'in_progress' | 'planned';
    visibility: 'public' | 'private' | 'friends';
  };
}