/**
 * 底部导航可见性管理Hook
 * 确保底部导航在React组件挂载后立即可见
 * 修复版：解决循环依赖和重复注册问题
 */

import React, { useState, useEffect, useCallback, useRef } from 'react';
import { Capacitor } from '@capacitor/core';

interface BottomNavVisibilityState {
  isVisible: boolean;
  isMounted: boolean;
  hasContent: boolean;
  renderAttempts: number;
  lastError?: string;
}

interface BottomNavVisibilityConfig {
  forceVisible: boolean;
  enableAutoFix: boolean;
  maxRenderAttempts: number;
  checkInterval: number;
}

/**
 * 底部导航可见性管理Hook - 重构版，解决循环依赖问题
 */
export const useBottomNavVisibility = (
  config: Partial<BottomNavVisibilityConfig> = {}
): BottomNavVisibilityState & {
  forceVisible: () => void;
  checkVisibility: () => boolean;
  ensureVisible: () => Promise<boolean>;
} => {
  const finalConfig: BottomNavVisibilityConfig = {
    forceVisible: true,
    enableAutoFix: true,
    maxRenderAttempts: 5,
    checkInterval: 100,
    ...config
  };

  // 🔧 使用useRef避免循环依赖
  const stateRef = useRef<BottomNavVisibilityState>({
    isVisible: false,
    isMounted: false,
    hasContent: false,
    renderAttempts: 0
  });

  const [, forceUpdate] = useState({});
  const elementRef = useRef<HTMLElement | null>(null);
  const routeListenersRegistered = useRef(false);
  const lastRouteCheck = useRef(0);
  const isIOSDevice = Capacitor.getPlatform() === 'ios';
  const isNative = Capacitor.isNativePlatform();

  // 🔧 强制组件重新渲染的函数
  const triggerUpdate = useCallback(() => {
    forceUpdate({});
  }, []);

  /**
   * 检查元素可见性 - 增强版，添加详细日志
   */
  const checkVisibility = useCallback((): boolean => {
    const element = document.querySelector('.bottom-navigation') as HTMLElement;
    elementRef.current = element;

    console.log('🔍 === 底部导航可见性详细检查 ===');
    
    if (!element) {
      console.log('❌ 底部导航元素未找到');
      console.log('📋 DOM查询结果:', {
        querySelector: !!document.querySelector('.bottom-navigation'),
        querySelectorAll: document.querySelectorAll('.bottom-navigation').length,
        getElementById: !!document.getElementById('bottom-navigation'),
        bodyChildren: document.body.children.length,
        documentReady: document.readyState
      });
      
      stateRef.current = {
        ...stateRef.current,
        isVisible: false,
        isMounted: false,
        hasContent: false,
        renderAttempts: stateRef.current.renderAttempts + 1
      };
      triggerUpdate();
      return false;
    }

    const style = getComputedStyle(element);
    const rect = element.getBoundingClientRect();

    // 🔍 详细的CSS属性检查
    const cssProperties = {
      display: style.display,
      visibility: style.visibility,
      opacity: style.opacity,
      zIndex: style.zIndex,
      position: style.position,
      top: style.top,
      bottom: style.bottom,
      left: style.left,
      right: style.right,
      width: style.width,
      height: style.height,
      transform: style.transform,
      backgroundColor: style.backgroundColor,
      borderTop: style.borderTop
    };

    // 🔍 详细的尺寸和位置检查
    const geometryInfo = {
      boundingRect: {
        x: rect.x,
        y: rect.y,
        width: rect.width,
        height: rect.height,
        top: rect.top,
        bottom: rect.bottom,
        left: rect.left,
        right: rect.right
      },
      offsetDimensions: {
        offsetWidth: element.offsetWidth,
        offsetHeight: element.offsetHeight,
        offsetTop: element.offsetTop,
        offsetLeft: element.offsetLeft
      }
    };

    // 🔍 DOM结构检查
    const domInfo = {
      tagName: element.tagName,
      className: element.className,
      id: element.id,
      childrenCount: element.children.length,
      parentElement: element.parentElement?.tagName || 'none',
      attributes: Array.from(element.attributes).map(attr => `${attr.name}="${attr.value}"`),
      innerHTML: element.innerHTML.substring(0, 100) + (element.innerHTML.length > 100 ? '...' : '')
    };

    // 检查基础可见性
    const isDisplayed = style.display !== 'none';
    const isVisible = style.visibility !== 'hidden';
    const hasOpacity = parseFloat(style.opacity) > 0;
    const hasSize = rect.width > 0 && rect.height > 0;
    const hasChildren = element.children.length > 0;
    const inViewport = rect.top < window.innerHeight && rect.bottom > 0;

    const visible = isDisplayed && isVisible && hasOpacity && hasSize && inViewport;

    console.log('📊 CSS属性详情:', cssProperties);
    console.log('📐 几何信息详情:', geometryInfo);
    console.log('🏗️ DOM结构详情:', domInfo);
    console.log('✅ 可见性判断:', {
      isDisplayed,
      isVisible,
      hasOpacity,
      hasSize,
      hasChildren,
      inViewport,
      finalResult: visible
    });

    // 🔧 更新状态
    stateRef.current = {
      ...stateRef.current,
      isVisible: visible,
      isMounted: !!element,
      hasContent: hasChildren,
      renderAttempts: stateRef.current.renderAttempts + 1
    };
    triggerUpdate();

    return visible;
  }, [triggerUpdate]);

  /**
   * 强制设置可见性 - 增强版，多重保障
   */
  const forceVisible = useCallback(() => {
    const element = elementRef.current || document.querySelector('.bottom-navigation') as HTMLElement;
    
    if (!element) {
      console.warn('⚠️ 底部导航元素未找到，无法强制设置可见性');
      return;
    }

    console.log('🔧 === 强制设置底部导航可见性 ===');

    // 🔧 强制设置关键样式 - 修复定位冲突
    console.log('📍 设置前元素位置:', {
      top: getComputedStyle(element).top,
      bottom: getComputedStyle(element).bottom,
      position: getComputedStyle(element).position
    });

    // 🔧 强制清除所有可能影响定位的属性
    element.style.removeProperty('top');
    element.style.removeProperty('transform');
    element.style.removeProperty('translate');
    element.style.removeProperty('margin-top');
    element.style.removeProperty('margin-bottom');

    // 🔧 强制重置定位相关的CSS属性
    element.style.setProperty('top', 'auto', 'important');
    element.style.setProperty('transform', 'none', 'important');

    const criticalStyles = {
      'display': 'block',
      'visibility': 'visible',
      'opacity': '1',
      'position': 'fixed',
      'bottom': '0', // 只使用bottom定位
      'top': 'auto', // 明确设置top为auto
      'left': '0',
      'right': '0',
      'z-index': '99999',
      'width': '100vw',
      'height': 'calc(60px + env(safe-area-inset-bottom, 34px))',
      // 🔧 确保元素贴底显示
      'margin': '0',
      'margin-top': '0',
      'margin-bottom': '0',
      'padding': '0',
      'padding-bottom': '0',
      'transform': 'none'
    };

    // 方法1：使用setProperty with important
    Object.entries(criticalStyles).forEach(([property, value]) => {
      element.style.setProperty(property, value, 'important');
    });

    console.log('📍 设置后元素位置:', {
      top: getComputedStyle(element).top,
      bottom: getComputedStyle(element).bottom,
      position: getComputedStyle(element).position
    });

    // 方法2：添加强制可见的CSS类
    element.classList.add('force-visible-bottom-nav');

    // 方法3：强制重新布局，确保定位生效
    element.style.setProperty('display', 'none');
    element.offsetHeight; // 强制重排
    element.style.setProperty('display', 'block', 'important');

    // 🔧 添加标记属性
    element.setAttribute('data-react-mounted', 'true');
    element.setAttribute('data-visibility-forced', 'true');

    // 🔧 验证元素位置是否正确
    setTimeout(() => {
      const rect = element.getBoundingClientRect();
      const screenHeight = window.innerHeight;
      const expectedTop = screenHeight - rect.height;

      console.log('📐 位置验证:', {
        screenHeight,
        elementHeight: rect.height,
        actualTop: rect.top,
        expectedTop,
        actualBottom: rect.bottom,
        isAtBottom: Math.abs(rect.bottom - screenHeight) < 5, // 允许5px误差
        positionCorrect: Math.abs(rect.top - expectedTop) < 5
      });

      // 🔧 如果位置仍然不正确，尝试修复
      if (Math.abs(rect.top - expectedTop) > 5) {
        console.warn('⚠️ 元素位置仍然不正确，尝试进一步修复...');

        // 强制重新计算布局
        element.style.setProperty('display', 'none', 'important');
        element.offsetHeight; // 强制重排
        element.style.setProperty('display', 'block', 'important');

        // 再次验证
        setTimeout(() => {
          const newRect = element.getBoundingClientRect();
          console.log('🔧 修复后位置:', {
            top: newRect.top,
            bottom: newRect.bottom,
            isCorrect: Math.abs(newRect.bottom - screenHeight) < 5
          });
        }, 50);
      }
    }, 50);

    console.log('✅ 底部导航可见性强制设置完成');

    // 🔧 延迟重新检查可见性
    setTimeout(() => {
      checkVisibility();
    }, 150);
  }, [checkVisibility]);

  /**
   * 确保元素可见（异步版本）
   */
  const ensureVisible = useCallback(async (): Promise<boolean> => {
    return new Promise((resolve) => {
      let attempts = 0;
      const maxAttempts = finalConfig.maxRenderAttempts;

      const checkAndFix = () => {
        attempts++;
        const isVisible = checkVisibility();

        if (isVisible) {
          console.log(`✅ 底部导航可见性确认，尝试${attempts}次`);
          resolve(true);
          return;
        }

        if (attempts >= maxAttempts) {
          console.warn(`⚠️ 底部导航可见性确保失败，已尝试${attempts}次`);
          stateRef.current = {
            ...stateRef.current,
            lastError: `可见性确保失败，已尝试${attempts}次`
          };
          triggerUpdate();
          resolve(false);
          return;
        }

        // 如果启用自动修复，尝试强制可见
        if (finalConfig.enableAutoFix) {
          forceVisible();
        }

        // 继续检查
        setTimeout(checkAndFix, finalConfig.checkInterval);
      };

      checkAndFix();
    });
  }, [checkVisibility, forceVisible, finalConfig.maxRenderAttempts, finalConfig.checkInterval, finalConfig.enableAutoFix, triggerUpdate]);

  /**
   * 组件挂载后的初始化 - 修复版，避免循环依赖
   */
  useEffect(() => {
    if (!isIOSDevice || !isNative) {
      console.log('🔍 非iOS设备，直接标记为可见');
      stateRef.current = {
        ...stateRef.current,
        isVisible: true,
        isMounted: true,
        hasContent: true
      };
      triggerUpdate();
      return;
    }

    console.log('🔍 === 初始化底部导航可见性检查 ===');

    // 🔧 延迟检查，确保DOM已准备好
    const initialCheck = setTimeout(() => {
      console.log('🔍 执行初始可见性检查...');
      const isVisible = checkVisibility();

      // 🔧 无论是否可见，都强制修复位置
      console.log('🔧 强制修复底部导航位置...');
      forceVisible();

      if (!isVisible && finalConfig.forceVisible) {
        console.log('⚠️ 底部导航初始不可见，启动确保可见流程...');
        ensureVisible().then(success => {
          console.log(`🎯 确保可见流程完成: ${success ? '成功' : '失败'}`);
        });
      } else if (isVisible) {
        console.log('✅ 底部导航初始状态可见');
      }
    }, 200);

    // 🔧 额外的位置修复检查
    const positionFixCheck = setTimeout(() => {
      const element = document.querySelector('.bottom-navigation') as HTMLElement;
      if (element) {
        const rect = element.getBoundingClientRect();
        const screenHeight = window.innerHeight;

        // 如果位置仍然不正确，强制修复
        if (rect.bottom !== screenHeight) {
          console.log('🔧 检测到位置异常，执行强制修复...');
          forceVisible();
        }
      }
    }, 500);

    return () => {
      clearTimeout(initialCheck);
      clearTimeout(positionFixCheck);
    };
  }, [isIOSDevice, isNative, checkVisibility, ensureVisible, finalConfig.forceVisible, triggerUpdate]);

  /**
   * 监听路由变化 - 修复版，防止重复注册
   */
  useEffect(() => {
    // 🔧 防止重复注册
    if (routeListenersRegistered.current) {
      return;
    }

    console.log('🔄 === 注册路由变化监听器 ===');

    const handleRouteChange = (source: string = 'unknown') => {
      const now = Date.now();
      const currentPath = window.location.pathname;

      // 🔧 增强防抖：500ms内的重复调用将被忽略
      if (now - lastRouteCheck.current < 500) {
        console.log(`🔄 路由变化防抖跳过 (${source}):`, {
          timeSinceLastCheck: now - lastRouteCheck.current,
          currentPath
        });
        return;
      }

      lastRouteCheck.current = now;

      console.log(`🔄 检测到路由变化 (${source}):`, {
        currentPath,
        timestamp: new Date().toISOString()
      });

      setTimeout(() => {
        const isVisible = checkVisibility();
        if (!isVisible && finalConfig.forceVisible) {
          console.log('🔧 路由变化后导航不可见，强制设置可见性...');
          forceVisible();
        }
      }, 150); // 增加延迟确保DOM更新完成
    };

    // 监听popstate事件（浏览器前进后退）
    const popstateHandler = () => handleRouteChange('popstate');
    window.addEventListener('popstate', popstateHandler);

    // 🔧 保存原始方法的引用
    const originalPushState = history.pushState.bind(history);
    const originalReplaceState = history.replaceState.bind(history);

    // 监听pushstate和replacestate（程序化导航）
    history.pushState = function(state, title, url) {
      const oldPath = window.location.pathname;
      originalPushState(state, title, url);
      const newPath = window.location.pathname;

      // 只有路径真正变化时才触发
      if (oldPath !== newPath) {
        handleRouteChange('pushState');
      }
    };

    history.replaceState = function(state, title, url) {
      const oldPath = window.location.pathname;
      originalReplaceState(state, title, url);
      const newPath = window.location.pathname;

      // 只有路径真正变化时才触发
      if (oldPath !== newPath) {
        handleRouteChange('replaceState');
      }
    };

    routeListenersRegistered.current = true;

    return () => {
      console.log('🧹 清理路由监听器...');
      window.removeEventListener('popstate', popstateHandler);
      history.pushState = originalPushState;
      history.replaceState = originalReplaceState;
      routeListenersRegistered.current = false;
    };
  }, [checkVisibility, forceVisible, finalConfig.forceVisible]);

  return {
    ...stateRef.current,
    forceVisible,
    checkVisibility,
    ensureVisible
  };
};

/**
 * 底部导航强制可见性组件
 */
export const BottomNavVisibilityEnsurer: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { isVisible, ensureVisible } = useBottomNavVisibility({
    forceVisible: true,
    enableAutoFix: true
  });

  useEffect(() => {
    ensureVisible();
  }, [ensureVisible]);

  return (
    <div 
      className="bottom-nav-visibility-ensurer"
      data-visible={isVisible}
      style={{ position: 'relative', zIndex: 9999 }}
    >
      {children}
    </div>
  );
};

/**
 * 便捷函数：立即强制底部导航可见
 */
export const forceBottomNavVisible = (): void => {
  const element = document.querySelector('.bottom-navigation') as HTMLElement;
  
  if (!element) {
    console.warn('⚠️ 底部导航元素未找到');
    return;
  }

  const criticalStyles = {
    'display': 'block',
    'visibility': 'visible',
    'opacity': '1',
    'position': 'fixed',
    'bottom': '0',
    'z-index': '9999'
  };

  Object.entries(criticalStyles).forEach(([property, value]) => {
    element.style.setProperty(property, value, 'important');
  });

  console.log('✅ 底部导航强制可见完成');
};
