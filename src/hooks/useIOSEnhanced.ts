/**
 * iOS增强功能Hook
 * 整合iOS原生功能，提供统一的iOS体验
 */

import { useState, useEffect, useCallback } from 'react';
import { Capacitor } from '@capacitor/core';
import { Network, ConnectionStatus } from '@capacitor/network';
import { Device, DeviceInfo } from '@capacitor/device';
import { StatusBar, Style } from '@capacitor/status-bar';
import { Haptics, ImpactStyle, NotificationType } from '@capacitor/haptics';
import { apiClient } from '../services/core/ApiClient';

export interface IOSEnhancedState {
  // 设备信息
  deviceInfo: DeviceInfo | null;
  isIOSDevice: boolean;
  isNative: boolean;
  
  // 网络状态
  networkStatus: ConnectionStatus | null;
  isOnline: boolean;
  connectionType: string;
  
  // 状态栏
  statusBarStyle: 'light' | 'dark';
  
  // 性能监控
  performanceMetrics: {
    memoryUsage?: number;
    networkLatency?: number;
    lastUpdated: string;
  };
}

export interface IOSEnhancedActions {
  // 触觉反馈
  triggerHapticFeedback: (style?: ImpactStyle) => Promise<void>;
  triggerNotificationFeedback: (type?: NotificationType) => Promise<void>;
  
  // 状态栏管理
  setStatusBarStyle: (style: 'light' | 'dark') => Promise<void>;
  hideStatusBar: () => Promise<void>;
  showStatusBar: () => Promise<void>;
  
  // 网络诊断
  refreshNetworkStatus: () => Promise<void>;
  getNetworkDiagnostics: () => any;
  
  // 性能监控
  startPerformanceMonitoring: () => void;
  getPerformanceMetrics: () => any;
}

/**
 * iOS增强功能Hook
 */
export function useIOSEnhanced(): IOSEnhancedState & IOSEnhancedActions {
  const [deviceInfo, setDeviceInfo] = useState<DeviceInfo | null>(null);
  const [networkStatus, setNetworkStatus] = useState<ConnectionStatus | null>(null);
  const [statusBarStyle, setStatusBarStyleState] = useState<'light' | 'dark'>('light');
  const [performanceMetrics, setPerformanceMetrics] = useState({
    lastUpdated: new Date().toISOString()
  });

  const isIOSDevice = Capacitor.getPlatform() === 'ios';
  const isNative = Capacitor.isNativePlatform();
  const isOnline = networkStatus?.connected ?? true;
  const connectionType = networkStatus?.connectionType ?? 'unknown';

  /**
   * 初始化iOS功能
   */
  useEffect(() => {
    if (!isNative) return;

    const initializeIOS = async () => {
      try {
        // 获取设备信息
        const info = await Device.getInfo();
        setDeviceInfo(info);

        // 获取网络状态
        const status = await Network.getStatus();
        setNetworkStatus(status);

        // 监听网络状态变化
        const networkListener = Network.addListener('networkStatusChange', (status) => {
          setNetworkStatus(status);
          console.log('📶 iOS网络状态变化:', status);
        });

        // iOS状态栏初始化
        if (isIOSDevice) {
          await StatusBar.setStyle({ style: Style.Default });
          await StatusBar.show();
        }

        console.log('✅ iOS增强功能初始化完成');

        // 清理函数
        return () => {
          networkListener.remove();
        };
      } catch (error) {
        console.warn('⚠️ iOS增强功能初始化失败:', error);
      }
    };

    initializeIOS();
  }, [isNative, isIOSDevice]);

  /**
   * 触觉反馈
   */
  const triggerHapticFeedback = useCallback(async (style: ImpactStyle = ImpactStyle.Light) => {
    if (!isIOSDevice || !isNative) return;

    try {
      await Haptics.impact({ style });
    } catch (error) {
      console.warn('⚠️ iOS触觉反馈失败:', error);
    }
  }, [isIOSDevice, isNative]);

  /**
   * 通知反馈
   */
  const triggerNotificationFeedback = useCallback(async (type: NotificationType = NotificationType.Success) => {
    if (!isIOSDevice || !isNative) return;

    try {
      await Haptics.notification({ type });
    } catch (error) {
      console.warn('⚠️ iOS通知反馈失败:', error);
    }
  }, [isIOSDevice, isNative]);

  /**
   * 设置状态栏样式
   */
  const setStatusBarStyle = useCallback(async (style: 'light' | 'dark') => {
    if (!isIOSDevice || !isNative) return;

    try {
      const statusBarStyle = style === 'light' ? Style.Light : Style.Dark;
      await StatusBar.setStyle({ style: statusBarStyle });
      setStatusBarStyleState(style);
      
      console.log(`✅ iOS状态栏样式设置为: ${style}`);
    } catch (error) {
      console.warn('⚠️ 设置iOS状态栏样式失败:', error);
    }
  }, [isIOSDevice, isNative]);

  /**
   * 隐藏状态栏
   */
  const hideStatusBar = useCallback(async () => {
    if (!isIOSDevice || !isNative) return;

    try {
      await StatusBar.hide();
    } catch (error) {
      console.warn('⚠️ 隐藏iOS状态栏失败:', error);
    }
  }, [isIOSDevice, isNative]);

  /**
   * 显示状态栏
   */
  const showStatusBar = useCallback(async () => {
    if (!isIOSDevice || !isNative) return;

    try {
      await StatusBar.show();
    } catch (error) {
      console.warn('⚠️ 显示iOS状态栏失败:', error);
    }
  }, [isIOSDevice, isNative]);

  /**
   * 刷新网络状态
   */
  const refreshNetworkStatus = useCallback(async () => {
    if (!isNative) return;

    try {
      const status = await Network.getStatus();
      setNetworkStatus(status);
    } catch (error) {
      console.warn('⚠️ 刷新网络状态失败:', error);
    }
  }, [isNative]);

  /**
   * 获取网络诊断信息
   */
  const getNetworkDiagnostics = useCallback(() => {
    return {
      networkStatus,
      isOnline,
      connectionType,
      deviceInfo,
      timestamp: new Date().toISOString()
    };
  }, [networkStatus, isOnline, connectionType, deviceInfo]);

  /**
   * 开始性能监控
   */
  const startPerformanceMonitoring = useCallback(() => {
    if (!isIOSDevice) return;

    const updateMetrics = () => {
      const metrics: any = {
        lastUpdated: new Date().toISOString()
      };

      // 内存使用情况（如果可用）
      if ('memory' in performance) {
        const memoryInfo = (performance as any).memory;
        metrics.memoryUsage = Math.round(memoryInfo.usedJSHeapSize / 1024 / 1024);
      }

      setPerformanceMetrics(metrics);
    };

    updateMetrics();
    const interval = setInterval(updateMetrics, 30000); // 每30秒更新一次

    return () => clearInterval(interval);
  }, [isIOSDevice]);

  /**
   * 获取性能指标
   */
  const getPerformanceMetrics = useCallback(() => {
    return {
      ...performanceMetrics,
      deviceInfo,
      networkStatus,
      apiClientMetrics: apiClient.getIOSNetworkDiagnostics()
    };
  }, [performanceMetrics, deviceInfo, networkStatus]);

  return {
    // 状态
    deviceInfo,
    isIOSDevice,
    isNative,
    networkStatus,
    isOnline,
    connectionType,
    statusBarStyle,
    performanceMetrics,
    
    // 操作
    triggerHapticFeedback,
    triggerNotificationFeedback,
    setStatusBarStyle,
    hideStatusBar,
    showStatusBar,
    refreshNetworkStatus,
    getNetworkDiagnostics,
    startPerformanceMonitoring,
    getPerformanceMetrics
  };
}
