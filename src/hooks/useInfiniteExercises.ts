/**
 * useInfiniteExercises Hook
 * 管理运动页面的无限滚动数据加载和缓存
 * 
 * @fileoverview 运动无限滚动数据管理Hook
 * <AUTHOR> Team
 * @since 1.0.0
 */

import { useState, useCallback, useEffect } from 'react';
import { Exercise } from '../utils/exerciseDataMapper';
import { ExerciseFilters, exerciseService } from '../services/exerciseService';


/**
 * useInfiniteExercises配置选项
 */
export interface UseInfiniteExercisesOptions {
  filters?: ExerciseFilters;
  pageSize?: number;
  enabled?: boolean;
  enablePreload?: boolean;
}

/**
 * useInfiniteExercises返回值
 */
export interface UseInfiniteExercisesReturn {
  // 数据状态
  exercises: Exercise[];
  loading: boolean;
  loadingMore: boolean;
  error: string | null;
  hasMore: boolean;
  
  // 分页信息
  currentPage: number;
  totalExercises: number;
  
  // 操作方法
  loadMore: () => Promise<void>;
  refresh: () => Promise<void>;
  mutate: (newData: Exercise[]) => Promise<void>;
  
  // 缓存信息
  isFromCache: boolean;
  cacheTimestamp: number | null;
  
  // 筛选管理
  updateFilters: (newFilters: ExerciseFilters) => void;
  currentFilters: ExerciseFilters;
  
  // 工具方法
  toggleFavorite: (exerciseId: string) => Promise<void>;
  preloadNextPage: () => Promise<void>;
}

/**
 * 分页数据响应接口
 */
interface ExercisePageResponse {
  exercises: Exercise[];
  hasMore: boolean;
  total: number;
  currentPage: number;
}

/**
 * 运动无限滚动Hook
 * 
 * @param options 配置选项
 * @returns 无限滚动状态和方法
 */
export const useInfiniteExercises = (
  options: UseInfiniteExercisesOptions = {}
): UseInfiniteExercisesReturn => {
  const {
    filters = {},

    enabled = true,
    enablePreload = true
  } = options;

  // 状态管理
  const [currentPage, setCurrentPage] = useState(0);
  const [allExercises, setAllExercises] = useState<Exercise[]>([]);
  const [hasMore, setHasMore] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);
  const [currentFilters, setCurrentFilters] = useState<ExerciseFilters>(filters);
  const [totalExercises, setTotalExercises] = useState(0);



  // 🔧 数据获取函数
  const fetchExercises = useCallback(async (
    pageToFetch: number, 
    filtersToUse: ExerciseFilters = currentFilters
  ): Promise<ExercisePageResponse> => {
    console.log(`[useInfiniteExercises] 获取运动数据 - 页面: ${pageToFetch}`, filtersToUse);
    
    try {
      const result = await exerciseService.getExercises(filtersToUse, pageToFetch);
      
      const response: ExercisePageResponse = {
        exercises: result.exercises,
        hasMore: result.hasMore,
        total: result.total,
        currentPage: pageToFetch
      };
      
      console.log(`[useInfiniteExercises] 获取成功 - ${result.exercises.length}个运动`);
      return response;
    } catch (error) {
      console.error('[useInfiniteExercises] 获取失败:', error);
      throw error;
    }
  }, [currentFilters]);

  // 🔧 数据获取和状态管理（简化版本，不使用useDataCache）
  const [data, setData] = useState<ExercisePageResponse | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isFromCache, setIsFromCache] = useState(false);
  const [cacheTimestamp, setCacheTimestamp] = useState<number | null>(null);

  // 刷新数据函数
  const refresh = useCallback(async () => {
    setLoading(true);
    setError(null);
    try {
      const result = await fetchExercises(currentPage, currentFilters);
      setData(result);
      setIsFromCache(false);
      setCacheTimestamp(Date.now());
    } catch (err) {
      setError(err instanceof Error ? err.message : String(err));
    } finally {
      setLoading(false);
    }
  }, [fetchExercises, currentPage, currentFilters]);

  // 初始数据加载
  useEffect(() => {
    if (enabled) {
      refresh();
    }
  }, [enabled, refresh]);

  // 🔧 合并分页数据
  useEffect(() => {
    if (data) {
      setAllExercises(prevExercises => {
        if (currentPage === 0) {
          // 第一页或筛选条件改变时，替换所有数据
          return data.exercises;
        } else {
          // 后续页面，追加数据（去重处理）
          const newExercises = data.exercises.filter(
            newExercise => !prevExercises.some(existing => existing.id === newExercise.id)
          );
          return [...prevExercises, ...newExercises];
        }
      });
      
      setHasMore(data.hasMore);
      setTotalExercises(data.total);

      console.log(`[useInfiniteExercises] 数据合并完成 - 总计: ${data.total}, 当前: ${allExercises.length + data.exercises.length}`);
    }
  }, [data, currentPage]);

  // 🔧 加载更多数据
  const loadMore = useCallback(async () => {
    if (loadingMore || !hasMore || loading) {
      console.log('[useInfiniteExercises] 跳过loadMore:', { loadingMore, hasMore, loading });
      return;
    }

    console.log('[useInfiniteExercises] 开始加载更多数据');
    setLoadingMore(true);

    try {
      const nextPage = currentPage + 1;
      setCurrentPage(nextPage);
    } catch (error) {
      console.error('[useInfiniteExercises] 加载更多失败:', error);
    } finally {
      setLoadingMore(false);
    }
  }, [currentPage, hasMore, loading, loadingMore]);

  // 🔧 预加载下一页
  const preloadNextPage = useCallback(async () => {
    if (!enablePreload || !hasMore || loadingMore) return;
    
    const nextPage = currentPage + 1;
    console.log(`[useInfiniteExercises] 预加载第${nextPage}页`);
    
    try {
      // 异步预加载，不影响当前状态
      await fetchExercises(nextPage, currentFilters);
      console.log(`[useInfiniteExercises] 预加载第${nextPage}页完成`);
    } catch (error) {
      console.warn('[useInfiniteExercises] 预加载失败:', error);
    }
  }, [currentPage, hasMore, loadingMore, enablePreload, fetchExercises, currentFilters]);

  // 🔧 更新筛选条件
  const updateFilters = useCallback((newFilters: ExerciseFilters) => {
    console.log('[useInfiniteExercises] 更新筛选条件:', newFilters);
    
    // 重置分页状态
    setCurrentPage(0);
    setAllExercises([]);
    setHasMore(true);
    setCurrentFilters(newFilters);
    
    // 刷新数据
    refresh();
  }, [refresh]);

  // 🔧 乐观更新数据
  const mutate = useCallback(async (newData: Exercise[]) => {
    console.log('[useInfiniteExercises] 乐观更新数据:', newData.length);
    setAllExercises(newData);
    
    // 同步更新缓存
    try {
      const updatedResponse: ExercisePageResponse = {
        exercises: newData,
        hasMore,
        total: newData.length,
        currentPage: 0
      };
      
      const currentCacheKey = `exercise:${JSON.stringify(currentFilters)}:page-0`;
      
      const cacheManager = await exerciseService.getCacheManagerInstance();
      await cacheManager.set(
        currentCacheKey,
        updatedResponse,
        { ttl: 10 * 60 * 1000, tags: ['exercise'], priority: 'high', persistent: true }
      );
    } catch (error) {
      console.warn('[useInfiniteExercises] 缓存更新失败:', error);
    }
  }, [hasMore, currentFilters]);

  // 🔧 切换收藏状态
  const toggleFavorite = useCallback(async (exerciseId: string) => {
    console.log('[useInfiniteExercises] 切换收藏状态:', exerciseId);
    
    // 乐观更新UI
    const optimisticExercises = allExercises.map(exercise => {
      if (exercise.id === exerciseId) {
        return {
          ...exercise,
          is_favorite: !exercise.is_favorite
        };
      }
      return exercise;
    });
    
    setAllExercises(optimisticExercises);
    
    try {
      // TODO: 实现收藏API调用
      // await exerciseService.toggleFavorite(exerciseId);
      console.log('[useInfiniteExercises] 收藏状态更新成功');
    } catch (error) {
      console.error('[useInfiniteExercises] 收藏状态更新失败:', error);
      // 回滚UI状态
      setAllExercises(allExercises);
    }
  }, [allExercises]);

  // 🔧 筛选条件变化时重置数据
  useEffect(() => {
    if (JSON.stringify(filters) !== JSON.stringify(currentFilters)) {
      updateFilters(filters);
    }
  }, [filters, currentFilters, updateFilters]);

  return {
    // 数据状态
    exercises: allExercises,
    loading,
    loadingMore,
    error,
    hasMore,
    
    // 分页信息
    currentPage,
    totalExercises,
    
    // 操作方法
    loadMore,
    refresh,
    mutate,
    
    // 缓存信息
    isFromCache,
    cacheTimestamp,
    
    // 筛选管理
    updateFilters,
    currentFilters,
    
    // 工具方法
    toggleFavorite,
    preloadNextPage
  };
};

export default useInfiniteExercises;