/**
 * FitMaster 数据缓存Hook
 * 统一的数据获取和缓存管理接口，支持网络请求、缓存策略和状态管理
 */

import { useState, useEffect, useCallback, useRef } from 'react'
import { CacheManager } from '../services/cache/CacheManager'
import { CacheOptions, CacheKeyBuilder } from '../services/cache/interfaces'
import GlobalCacheManager from '../services/cache/GlobalCacheManager'

// ===== Hook配置接口 =====

export interface UseDataCacheOptions<T> {
  /** 缓存键 */
  key: string
  /** 数据获取函数 */
  fetcher: () => Promise<T>
  /** 缓存选项 */
  cacheOptions?: CacheOptions
  /** 是否启用自动加载 */
  enabled?: boolean
  /** 挂载时是否重新获取 */
  refetchOnMount?: boolean
  /** 网络重连时是否重新获取 */
  refetchOnReconnect?: boolean
  /** 窗口聚焦时是否重新获取 */
  refetchOnWindowFocus?: boolean
  /** 重试配置 */
  retry?: {
    attempts?: number
    delay?: number
    backoff?: 'linear' | 'exponential'
  }
  /** 成功回调 */
  onSuccess?: (data: T) => void
  /** 错误回调 */
  onError?: (error: Error) => void
  /** 乐观更新函数 */
  optimisticUpdate?: (oldData: T | null, newData: Partial<T>) => T
}

// ===== Hook返回值接口 =====

export interface UseDataCacheReturn<T> {
  /** 当前数据 */
  data: T | null
  /** 是否正在加载（首次） */
  loading: boolean
  /** 是否正在重新获取（后续） */
  refetching: boolean
  /** 错误信息 */
  error: Error | null
  /** 是否来自缓存 */
  isFromCache: boolean
  /** 缓存时间戳 */
  cacheTimestamp: number | null
  /** 手动刷新数据 */
  refresh: () => Promise<void>
  /** 乐观更新数据 */
  mutate: (data: T | ((prev: T | null) => T)) => Promise<void>
  /** 清除缓存 */
  clear: () => Promise<void>
  /** 预加载数据 */
  preload: () => Promise<void>
  /** 重新验证数据 */
  revalidate: () => Promise<void>
}

// ===== Hook状态接口 =====

interface DataCacheState<T> {
  data: T | null
  loading: boolean
  refetching: boolean
  error: Error | null
  isFromCache: boolean
  cacheTimestamp: number | null
}

// ===== 全局缓存管理器实例 =====

let globalCacheManager: CacheManager | null = null

const getCacheManager = async (): Promise<CacheManager> => {
  if (!globalCacheManager) {
    globalCacheManager = await GlobalCacheManager.getInstance()
  }
  return globalCacheManager
}

// ===== 重试工具函数 =====

const sleep = (ms: number): Promise<void> => 
  new Promise(resolve => setTimeout(resolve, ms))

const calculateDelay = (attempt: number, baseDelay: number, backoff: 'linear' | 'exponential'): number => {
  switch (backoff) {
    case 'exponential':
      return baseDelay * Math.pow(2, attempt - 1)
    case 'linear':
    default:
      return baseDelay * attempt
  }
}

// ===== 主要Hook实现 =====

export function useDataCache<T>(options: UseDataCacheOptions<T>): UseDataCacheReturn<T> {
  const {
    key,
    fetcher,
    cacheOptions = {},
    enabled = true,
    refetchOnMount = false,
    refetchOnReconnect = true,
    refetchOnWindowFocus = false,
    retry = { attempts: 3, delay: 1000, backoff: 'exponential' },
    onSuccess,
    onError,
    optimisticUpdate
  } = options

  // ===== 状态管理 =====

  const [state, setState] = useState<DataCacheState<T>>({
    data: null,
    loading: false,
    refetching: false,
    error: null,
    isFromCache: false,
    cacheTimestamp: null
  })

  // ===== Refs =====

  const [cacheManager, setCacheManager] = useState<CacheManager | null>(null)
  const mountedRef = useRef(true)
  const retryTimeoutRef = useRef<number | null>(null)
  const lastFetchTimeRef = useRef<number>(0)

  // ===== 初始化缓存管理器 =====

  useEffect(() => {
    const initCacheManager = async () => {
      try {
        const manager = await getCacheManager()
        setCacheManager(manager)
      } catch (error) {
        console.error('[useDataCache] 缓存管理器初始化失败:', error)
      }
    }
    initCacheManager()
  }, [])

  // ===== 核心数据获取函数 =====

  const fetchData = useCallback(async (
    isRefresh = false,
    skipCache = false
  ): Promise<T | null> => {
    const fetchId = Date.now()
    lastFetchTimeRef.current = fetchId

    try {
      // 1. 如果不跳过缓存，先尝试从缓存获取
      if (!skipCache && cacheManager) {
        const cached = await cacheManager.get<T>(key)
        if (cached) {
          console.log(`[useDataCache] 缓存命中: ${key}`)
          setState(prev => ({
            ...prev,
            data: cached,
            loading: false,
            refetching: false,
            error: null,
            isFromCache: true,
            cacheTimestamp: Date.now()
          }))
          onSuccess?.(cached)
          return cached
        }
      }

      // 2. 从网络获取数据
      console.log(`[useDataCache] 网络请求: ${key}`, { isRefresh, skipCache })
      
      let fetchError: Error | null = null
      let result: T | null = null

      // 重试机制
      for (let attempt = 1; attempt <= (retry.attempts || 3); attempt++) {
        try {
          // 检查是否还是最新的请求
          if (lastFetchTimeRef.current !== fetchId) {
            console.log(`[useDataCache] 请求已过期，取消: ${key}`)
            return null
          }

          result = await fetcher()
          fetchError = null
          break

        } catch (error) {
          fetchError = error as Error
          console.warn(`[useDataCache] 请求失败 (尝试 ${attempt}/${retry.attempts}): ${key}`, {
            error: error instanceof Error ? error.message : String(error),
            stack: error instanceof Error ? error.stack : undefined,
            key,
            attempt,
            maxAttempts: retry.attempts
          })

          // 如果不是最后一次尝试，等待后重试
          if (attempt < (retry.attempts || 3)) {
            const delay = calculateDelay(attempt, retry.delay || 1000, retry.backoff || 'exponential')
            await sleep(delay)
          }
        }
      }

      // 3. 处理最终结果
      if (fetchError) {
        throw fetchError
      }

      if (result === null) {
        throw new Error(`Fetcher returned null for key: ${key}`)
      }

      // 4. 缓存新数据
      if (cacheManager) {
        await cacheManager.set(key, result, {
          ttl: 5 * 60 * 1000, // 默认5分钟
          persistent: true,
          ...cacheOptions
        })
      }

      // 5. 更新状态
      if (mountedRef.current && lastFetchTimeRef.current === fetchId) {
        setState(prev => ({
          ...prev,
          data: result,
          loading: false,
          refetching: false,
          error: null,
          isFromCache: false,
          cacheTimestamp: Date.now()
        }))
        onSuccess?.(result!)
      }

      return result

    } catch (error) {
      const err = error as Error
      console.error(`[useDataCache] 获取数据失败: ${key}`, {
        error: err.message,
        stack: err.stack,
        key,
        timestamp: new Date().toISOString()
      })

      if (mountedRef.current && lastFetchTimeRef.current === fetchId) {
        setState(prev => ({
          ...prev,
          loading: false,
          refetching: false,
          error: err
        }))
        onError?.(err)
      }

      return null
    }
  }, [key, fetcher, cacheOptions, cacheManager, retry, onSuccess, onError])

  // ===== 公共API方法 =====

  const refresh = useCallback(async (): Promise<void> => {
    setState(prev => ({
      ...prev,
      refetching: true,
      error: null
    }))
    await fetchData(true, true)
  }, [fetchData])

  const mutate = useCallback(async (
    data: T | ((prev: T | null) => T)
  ): Promise<void> => {
    try {
      const newData = typeof data === 'function' ? (data as (prev: T | null) => T)(state.data) : data as T

      // 乐观更新
      if (optimisticUpdate && state.data) {
        const optimisticData = optimisticUpdate(state.data, newData as Partial<T>)
        setState(prev => ({
          ...prev,
          data: optimisticData,
          isFromCache: false
        }))
      } else {
        setState(prev => ({
          ...prev,
          data: newData,
          isFromCache: false
        }))
      }

      // 更新缓存
      if (cacheManager) {
        await cacheManager.set(key, newData, cacheOptions)
      }

    } catch (error) {
      console.error(`[useDataCache] 数据更新失败: ${key}`, error)
      const err = error as Error
      setState(prev => ({ ...prev, error: err }))
      onError?.(err)
    }
  }, [key, state.data, cacheManager, cacheOptions, optimisticUpdate, onError])

  const clear = useCallback(async (): Promise<void> => {
    try {
      if (cacheManager) {
        await cacheManager.delete(key)
      }
      setState({
        data: null,
        loading: false,
        refetching: false,
        error: null,
        isFromCache: false,
        cacheTimestamp: null
      })
    } catch (error) {
      console.error(`[useDataCache] 清除缓存失败: ${key}`, error)
    }
  }, [key, cacheManager])

  const preload = useCallback(async (): Promise<void> => {
    if (!state.data && !state.loading) {
      setState(prev => ({ ...prev, loading: true }))
      await fetchData(false, false)
    }
  }, [state.data, state.loading, fetchData])

  const revalidate = useCallback(async (): Promise<void> => {
    await fetchData(true, false)
  }, [fetchData])

  // ===== 效果管理 =====

  // 初始化数据加载
  useEffect(() => {
    if (!enabled) return

    const shouldFetch = !state.data || refetchOnMount
    if (shouldFetch) {
      setState(prev => ({ ...prev, loading: true, error: null }))
      fetchData(refetchOnMount, false)
    }
  }, [enabled, refetchOnMount]) // 依赖key会导致无限循环

  // 网络重连监听
  useEffect(() => {
    if (!refetchOnReconnect) return

    const handleOnline = () => {
      if (state.error) {
        console.log(`[useDataCache] 网络重连，重新获取数据: ${key}`)
        refresh()
      }
    }

    window.addEventListener('online', handleOnline)
    return () => window.removeEventListener('online', handleOnline)
  }, [refetchOnReconnect, state.error, key, refresh])

  // 窗口聚焦监听
  useEffect(() => {
    if (!refetchOnWindowFocus) return

    const handleFocus = () => {
      // 避免频繁重新获取，设置最小间隔
      const timeSinceLastFetch = Date.now() - lastFetchTimeRef.current
      if (timeSinceLastFetch > 60000) { // 1分钟
        console.log(`[useDataCache] 窗口聚焦，重新验证数据: ${key}`)
        revalidate()
      }
    }

    window.addEventListener('focus', handleFocus)
    return () => window.removeEventListener('focus', handleFocus)
  }, [refetchOnWindowFocus, key, revalidate])

  // 清理资源
  useEffect(() => {
    mountedRef.current = true
    return () => {
      mountedRef.current = false
      if (retryTimeoutRef.current) {
        clearTimeout(retryTimeoutRef.current)
      }
    }
  }, [])

  // ===== 返回Hook结果 =====

  return {
    data: state.data,
    loading: state.loading,
    refetching: state.refetching,
    error: state.error,
    isFromCache: state.isFromCache,
    cacheTimestamp: state.cacheTimestamp,
    refresh,
    mutate,
    clear,
    preload,
    revalidate
  }
}

// ===== 辅助Hook：批量数据缓存 =====

export function useMultiDataCache<T>(
  configs: Array<Omit<UseDataCacheOptions<T>, 'enabled'>>
): UseDataCacheReturn<T>[] {
  return configs.map(config => 
    useDataCache({ ...config, enabled: true })
  )
}

// ===== 辅助Hook：缓存键构建器 =====

export function useCacheKey(): {
  buildKey: (parts: {
    namespace: string
    resource: string
    params?: Record<string, any>
    version?: string
  }) => string
} {
  const buildKey = useCallback((parts: {
    namespace: string
    resource: string
    params?: Record<string, any>
    version?: string
  }) => {
    return CacheKeyBuilder.create()
      .namespace(parts.namespace)
      .resource(parts.resource)
      .params(parts.params || {})
      .version(parts.version || '1.0')
      .build()
  }, [])

  return { buildKey }
}

// ===== 辅助Hook：缓存管理器访问 =====

export function useCacheManager(): CacheManager | null {
  const [cacheManager, setCacheManager] = useState<CacheManager | null>(null)

  useEffect(() => {
    const initCacheManager = async () => {
      try {
        const manager = await getCacheManager()
        setCacheManager(manager)
      } catch (error) {
        console.error('[useCacheManager] 缓存管理器初始化失败:', error)
      }
    }
    initCacheManager()
  }, [])

  return cacheManager
}