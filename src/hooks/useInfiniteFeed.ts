/**
 * 无限分页Feed数据管理Hook
 * 基于CachedContent实现智能分页和缓存管理
 */

import { useState, useCallback, useMemo } from 'react'
import { FeedPost, FeedPostsResponse, FeedFilterType } from '../types/feed.types'
import { CommunityService } from '../services/communityService'
import { authService } from '../services/authService'
import { useDataCache, useCacheKey } from './useDataCache'

interface UseInfiniteFeedOptions {
  filter?: FeedFilterType
  pageSize?: number
  enabled?: boolean
}

interface UseInfiniteFeedReturn {
  // 数据状态
  posts: FeedPost[]
  loading: boolean
  error: Error | null
  hasMore: boolean
  
  // 分页状态
  currentPage: number
  totalPages: number
  
  // 操作方法
  loadMore: () => Promise<void>
  refresh: () => Promise<void>
  mutate: (posts: FeedPost[]) => Promise<void>
  clear: () => Promise<void>
  
  // 缓存信息
  isFromCache: boolean
  cacheTimestamp: number | null
}

export const useInfiniteFeed = ({
  filter = 'all',
  pageSize = 20,
  enabled = true
}: UseInfiniteFeedOptions = {}): UseInfiniteFeedReturn => {
  const [loadedPages, setLoadedPages] = useState<number[]>([0])
  const { buildKey } = useCacheKey()

  // 为每一页创建独立的缓存查询
  const pageQueries = loadedPages.map(page => {
    const cacheKey = buildKey({
      namespace: 'feed',
      resource: 'posts',
      params: { filter, page },
      version: '1.0'
    })

    return useDataCache<FeedPostsResponse>({
      key: cacheKey,
      fetcher: async () => {
        // 确保认证
        if (!authService.isAuthenticated()) {
          await authService.loginWithTestUser()
        }
        
        return CommunityService.getPosts({
          skip: page * pageSize,
          limit: pageSize
        })
      },
      cacheOptions: {
        ttl: 5 * 60 * 1000,     // 5分钟
        tags: ['feed', 'posts', `filter:${filter}`],
        priority: 'high',
        persistent: true
      },
      enabled: enabled && page >= 0,
      refetchOnMount: page === 0, // 只有第一页在挂载时刷新
      onSuccess: (data) => {
        console.log(`[useInfiniteFeed] 页面 ${page} 加载成功:`, {
          postsCount: data.posts.length,
          hasMore: data.hasMore
        })
      },
      onError: (error) => {
        console.error(`[useInfiniteFeed] 页面 ${page} 加载失败:`, {
          error: error instanceof Error ? error.message : String(error),
          stack: error instanceof Error ? error.stack : undefined,
          page,
          filter,
          pageSize,
          timestamp: new Date().toISOString()
        })
      }
    })
  })

  // 合并所有页面的数据
  const allPosts = useMemo(() => {
    return pageQueries
      .filter(query => query.data?.posts)
      .flatMap(query => query.data!.posts)
  }, [pageQueries])

  // 获取第一页的查询状态作为主要状态
  const firstPageQuery = pageQueries[0]
  const lastPageQuery = pageQueries[pageQueries.length - 1]

  // 计算综合状态
  const loading = firstPageQuery?.loading && allPosts.length === 0
  const error = pageQueries.find(q => q.error)?.error || null
  const hasMore = lastPageQuery?.data?.hasMore ?? true
  const isFromCache = firstPageQuery?.isFromCache ?? false
  const cacheTimestamp = firstPageQuery?.cacheTimestamp ?? null

  // 加载更多
  const loadMore = useCallback(async () => {
    if (!hasMore || loading) {
      console.log('[useInfiniteFeed] 跳过loadMore:', { hasMore, loading })
      return
    }

    const nextPage = Math.max(...loadedPages) + 1
    console.log('[useInfiniteFeed] 加载下一页:', nextPage)
    
    setLoadedPages(prev => [...prev, nextPage])
  }, [hasMore, loading, loadedPages])

  // 刷新所有数据
  const refresh = useCallback(async () => {
    console.log('[useInfiniteFeed] 刷新所有页面')
    
    // 重置为第一页
    setLoadedPages([0])
    
    // 刷新第一页
    if (firstPageQuery?.refresh) {
      await firstPageQuery.refresh()
    }
  }, [firstPageQuery])

  // 乐观更新所有posts
  const mutate = useCallback(async (newPosts: FeedPost[]) => {
    // 这里需要智能地将新posts分配到对应的页面缓存中
    // 为简化实现，我们刷新第一页
    if (firstPageQuery?.mutate) {
      // 计算第一页应该包含的posts
      const firstPagePosts = newPosts.slice(0, pageSize)
      const firstPageData: FeedPostsResponse = {
        posts: firstPagePosts,
        total: newPosts.length,
        page: 1,
        limit: pageSize,
        hasMore: newPosts.length > pageSize
      }
      
      await firstPageQuery.mutate(firstPageData)
    }
  }, [firstPageQuery, pageSize])

  // 清除所有缓存
  const clear = useCallback(async () => {
    console.log('[useInfiniteFeed] 清除所有缓存')
    
    // 清除所有页面的缓存
    await Promise.all(
      pageQueries
        .filter(query => query.clear)
        .map(query => query.clear())
    )
    
    // 重置状态
    setLoadedPages([0])
  }, [pageQueries])

  return {
    // 数据状态
    posts: allPosts,
    loading,
    error,
    hasMore,
    
    // 分页状态
    currentPage: loadedPages.length - 1,
    totalPages: loadedPages.length,
    
    // 操作方法
    loadMore,
    refresh,
    mutate,
    clear,
    
    // 缓存信息
    isFromCache,
    cacheTimestamp
  }
}

/**
 * Hook: 带筛选的无限Feed
 */
export const useFilteredInfiniteFeed = (filter: FeedFilterType) => {
  const feedData = useInfiniteFeed({ filter })
  
  // 客户端筛选逻辑（如果需要）
  const filteredPosts = useMemo(() => {
    switch (filter) {
      case 'following':
        // 这里可以添加客户端筛选逻辑
        return feedData.posts.filter(post => post.user.isFollowing)
      case 'all':
      default:
        return feedData.posts
    }
  }, [feedData.posts, filter])

  return {
    ...feedData,
    posts: filteredPosts
  }
}