/**
 * 认证状态管理Hook
 * 
 * @fileoverview 统一的认证状态检查和管理，用于社交操作前的认证验证
 * <AUTHOR> Team
 * @since 1.0.0
 */

import { useState, useCallback, useEffect } from 'react';
import { authService } from '../services/authService';
import { apiClient } from '../services/core/ApiClient';
import type { AuthUser } from '../services/authService';

export interface AuthState {
  isAuthenticated: boolean;
  user: AuthUser | null;
  isLoading: boolean;
  error: string | null;
}

export interface AuthActions {
  checkAuthStatus: () => Promise<boolean>;
  refreshAuth: () => Promise<boolean>;
  showLoginModal: () => void;
  clearError: () => void;
}

export const useAuthState = (): AuthState & AuthActions => {
  const [state, setState] = useState<AuthState>({
    isAuthenticated: false,
    user: null,
    isLoading: true,
    error: null
  });

  // 检查认证状态
  const checkAuthStatus = useCallback(async (): Promise<boolean> => {
    try {
      setState(prev => ({ ...prev, isLoading: true, error: null }));

      // 检查AuthService状态
      const isAuthed = authService.isAuthenticated();
      const user = authService.getCurrentUser();
      const accessToken = apiClient.getAccessToken();

      console.log('【认证状态检查】', {
        authServiceAuthenticated: isAuthed,
        hasUser: !!user,
        hasAccessToken: !!accessToken,
        userId: user?.id,
        userName: user?.nickName
      });

      if (isAuthed && user && accessToken) {
        setState({
          isAuthenticated: true,
          user,
          isLoading: false,
          error: null
        });
        return true;
      }

      // 如果AuthService认为已认证，但ApiClient没有token，需要同步
      if (isAuthed && user && !accessToken) {
        console.warn('【认证状态】AuthService已认证但ApiClient无token，尝试刷新');
        await authService.refreshTokenIfNeeded();
        
        const newAccessToken = apiClient.getAccessToken();
        if (newAccessToken) {
          setState({
            isAuthenticated: true,
            user,
            isLoading: false,
            error: null
          });
          return true;
        }
      }

      // 未认证状态
      setState({
        isAuthenticated: false,
        user: null,
        isLoading: false,
        error: null
      });
      return false;

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '认证检查失败';
      console.error('【认证状态检查】失败:', error);
      
      setState({
        isAuthenticated: false,
        user: null,
        isLoading: false,
        error: errorMessage
      });
      return false;
    }
  }, []);

  // 刷新认证状态
  const refreshAuth = useCallback(async (): Promise<boolean> => {
    try {
      setState(prev => ({ ...prev, isLoading: true, error: null }));

      // 如果没有用户信息，尝试测试用户登录
      if (!authService.isAuthenticated()) {
        console.log('【认证刷新】用户未登录，尝试测试用户登录');
        await authService.loginWithTestUser();
      } else {
        // 如果已登录，刷新token
        console.log('【认证刷新】用户已登录，刷新token');
        await authService.refreshTokenIfNeeded();
      }

      return await checkAuthStatus();
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '认证刷新失败';
      console.error('【认证刷新】失败:', error);
      
      setState(prev => ({ 
        ...prev, 
        isLoading: false, 
        error: errorMessage 
      }));
      return false;
    }
  }, [checkAuthStatus]);

  // 显示登录模态框（暂时使用console提示）
  const showLoginModal = useCallback(() => {
    console.warn('【认证提示】需要登录后才能进行此操作');
    // TODO: 实现登录模态框
    // 暂时自动尝试测试用户登录
    refreshAuth();
  }, [refreshAuth]);

  // 清除错误状态
  const clearError = useCallback(() => {
    setState(prev => ({ ...prev, error: null }));
  }, []);

  // 初始化时检查认证状态
  useEffect(() => {
    checkAuthStatus();
  }, [checkAuthStatus]);

  return {
    ...state,
    checkAuthStatus,
    refreshAuth,
    showLoginModal,
    clearError
  };
};