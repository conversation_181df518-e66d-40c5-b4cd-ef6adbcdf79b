/**
 * Capacitor原生功能Hook
 * 符合FitMaster开发规则的原生功能集成
 */

import { useState, useEffect, useCallback } from 'react';
import { Capacitor } from '@capacitor/core';
import { Preferences } from '@capacitor/preferences';
import { Network } from '@capacitor/network';
import { Device } from '@capacitor/device';
import { StatusBar, Style } from '@capacitor/status-bar';
import { Keyboard } from '@capacitor/keyboard';
import { App } from '@capacitor/app';

// 设备信息类型
interface DeviceInfo {
  platform: string;
  model: string;
  osVersion: string;
  isNative: boolean;
}

// 网络状态类型
interface NetworkStatus {
  connected: boolean;
  connectionType: string;
}

// Capacitor功能Hook返回类型
interface CapacitorFeatures {
  // 设备信息
  deviceInfo: DeviceInfo | null;
  isNative: boolean;
  platform: string;
  getPlatform: () => string;

  // 网络状态
  networkStatus: NetworkStatus;

  // 存储功能
  getItem: (key: string) => Promise<string | null>;
  setItem: (key: string, value: string) => Promise<void>;
  removeItem: (key: string) => Promise<void>;

  // iOS特定功能
  setStatusBarStyle: (style: 'light' | 'dark') => Promise<void>;
  hideKeyboard: () => Promise<void>;

  // 应用状态
  appState: 'active' | 'background';
  
  // 初始化状态
  isReady: boolean;
}

/**
 * Capacitor原生功能Hook
 * 符合FitMaster开发规则的原生功能集成
 */
export function useCapacitorFeatures(): CapacitorFeatures {
  const [deviceInfo, setDeviceInfo] = useState<DeviceInfo | null>(null);
  const [networkStatus, setNetworkStatus] = useState<NetworkStatus>({
    connected: true,
    connectionType: 'unknown'
  });
  const [appState, setAppState] = useState<'active' | 'background'>('active');
  const [isReady, setIsReady] = useState(false);

  const isNative = Capacitor.isNativePlatform();
  const platform = Capacitor.getPlatform();

  // 获取设备信息
  useEffect(() => {
    const getDeviceInfo = async () => {
      try {
        if (isNative) {
          const info = await Device.getInfo();
          setDeviceInfo({
            platform: info.platform,
            model: info.model,
            osVersion: info.osVersion,
            isNative: true
          });
        } else {
          setDeviceInfo({
            platform: 'web',
            model: 'Browser',
            osVersion: 'N/A',
            isNative: false
          });
        }
      } catch (error) {
        console.warn('获取设备信息失败:', error);
        setDeviceInfo({
          platform: 'unknown',
          model: 'Unknown',
          osVersion: 'Unknown',
          isNative: false
        });
      }
    };

    getDeviceInfo();
  }, [isNative]);

  // 网络状态监听
  useEffect(() => {
    let networkListener: any;

    const setupNetworkListener = async () => {
      try {
        if (isNative) {
          // 获取当前网络状态
          const status = await Network.getStatus();
          setNetworkStatus({
            connected: status.connected,
            connectionType: status.connectionType
          });

          // 监听网络状态变化
          networkListener = await Network.addListener('networkStatusChange', (status) => {
            setNetworkStatus({
              connected: status.connected,
              connectionType: status.connectionType
            });
          });
        } else {
          // Web端使用navigator.onLine
          const updateNetworkStatus = () => {
            setNetworkStatus({
              connected: navigator.onLine,
              connectionType: navigator.onLine ? 'wifi' : 'none'
            });
          };

          updateNetworkStatus();
          window.addEventListener('online', updateNetworkStatus);
          window.addEventListener('offline', updateNetworkStatus);

          return () => {
            window.removeEventListener('online', updateNetworkStatus);
            window.removeEventListener('offline', updateNetworkStatus);
          };
        }
      } catch (error) {
        console.warn('设置网络监听失败:', error);
      }
    };

    setupNetworkListener();

    return () => {
      if (networkListener) {
        networkListener.remove();
      }
    };
  }, [isNative]);

  // 应用状态监听（仅原生）
  useEffect(() => {
    let appStateListener: any;

    if (isNative) {
      const setupAppStateListener = async () => {
        try {
          appStateListener = await App.addListener('appStateChange', ({ isActive }) => {
            setAppState(isActive ? 'active' : 'background');
          });
        } catch (error) {
          console.warn('设置应用状态监听失败:', error);
        }
      };

      setupAppStateListener();
    }

    return () => {
      if (appStateListener) {
        appStateListener.remove();
      }
    };
  }, [isNative]);

  // 存储功能
  const getItem = useCallback(async (key: string): Promise<string | null> => {
    try {
      if (isNative) {
        const result = await Preferences.get({ key });
        return result.value;
      } else {
        return localStorage.getItem(key);
      }
    } catch (error) {
      console.warn(`读取存储项 ${key} 失败:`, error);
      return null;
    }
  }, [isNative]);

  const setItem = useCallback(async (key: string, value: string): Promise<void> => {
    try {
      if (isNative) {
        await Preferences.set({ key, value });
      } else {
        localStorage.setItem(key, value);
      }
    } catch (error) {
      console.warn(`设置存储项 ${key} 失败:`, error);
    }
  }, [isNative]);

  const removeItem = useCallback(async (key: string): Promise<void> => {
    try {
      if (isNative) {
        await Preferences.remove({ key });
      } else {
        localStorage.removeItem(key);
      }
    } catch (error) {
      console.warn(`删除存储项 ${key} 失败:`, error);
    }
  }, [isNative]);

  // iOS状态栏样式设置
  const setStatusBarStyle = useCallback(async (style: 'light' | 'dark'): Promise<void> => {
    try {
      if (isNative && platform === 'ios') {
        // Capacitor StatusBar API的正确用法：
        // Style.Light = 深色文字（适配浅色背景）
        // Style.Dark = 浅色文字（适配深色背景）
        const statusBarStyle = style === 'light' ? Style.Light : Style.Dark;
        await StatusBar.setStyle({ style: statusBarStyle });
      }
    } catch (error) {
      console.warn('设置状态栏样式失败:', error);
    }
  }, [isNative, platform]);

  // 隐藏键盘
  const hideKeyboard = useCallback(async (): Promise<void> => {
    try {
      if (isNative) {
        await Keyboard.hide();
      }
    } catch (error) {
      console.warn('隐藏键盘失败:', error);
    }
  }, [isNative]);

  // 获取平台信息
  const getPlatform = useCallback((): string => {
    return platform;
  }, [platform]);

  // 标记为就绪
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsReady(true);
    }, 100);

    return () => clearTimeout(timer);
  }, []);

  return {
    deviceInfo,
    isNative,
    platform,
    getPlatform,
    networkStatus,
    getItem,
    setItem,
    removeItem,
    setStatusBarStyle,
    hideKeyboard,
    appState,
    isReady
  };
} 