/**
 * iOS Safe Area就绪检测Hook
 * 解决Capacitor WebView中env()函数初始化延迟问题
 */

import { useState, useEffect, useCallback } from 'react';
import { Capacitor } from '@capacitor/core';

interface SafeAreaInsets {
  top: number;
  bottom: number;
  left: number;
  right: number;
}

interface SafeAreaState {
  isReady: boolean;
  insets: SafeAreaInsets;
  initializationTime: number;
  checkCount: number;
}

/**
 * 检测Safe Area是否已就绪的Hook
 * @param maxWaitTime 最大等待时间（毫秒），默认2000ms
 * @param checkInterval 检查间隔（毫秒），默认50ms
 */
export const useSafeAreaReady = (
  maxWaitTime: number = 2000,
  checkInterval: number = 50
): SafeAreaState => {
  const [state, setState] = useState<SafeAreaState>({
    isReady: false,
    insets: { top: 0, bottom: 0, left: 0, right: 0 },
    initializationTime: 0,
    checkCount: 0
  });

  const isIOSDevice = Capacitor.getPlatform() === 'ios';
  const isNative = Capacitor.isNativePlatform();

  /**
   * 检测Safe Area值是否可用
   */
  const checkSafeAreaAvailability = useCallback((): SafeAreaInsets | null => {
    try {
      // 方法1：使用测试元素检测env()函数
      const testElements = {
        top: document.createElement('div'),
        bottom: document.createElement('div'),
        left: document.createElement('div'),
        right: document.createElement('div')
      };

      // 设置测试元素样式
      testElements.top.style.cssText = 'position:fixed;top:env(safe-area-inset-top);visibility:hidden;pointer-events:none;';
      testElements.bottom.style.cssText = 'position:fixed;bottom:env(safe-area-inset-bottom);visibility:hidden;pointer-events:none;';
      testElements.left.style.cssText = 'position:fixed;left:env(safe-area-inset-left);visibility:hidden;pointer-events:none;';
      testElements.right.style.cssText = 'position:fixed;right:env(safe-area-inset-right);visibility:hidden;pointer-events:none;';

      // 添加到DOM
      Object.values(testElements).forEach(el => document.body.appendChild(el));

      // 获取计算后的样式
      const computedStyles = {
        top: getComputedStyle(testElements.top).top,
        bottom: getComputedStyle(testElements.bottom).bottom,
        left: getComputedStyle(testElements.left).left,
        right: getComputedStyle(testElements.right).right
      };

      // 清理测试元素
      Object.values(testElements).forEach(el => document.body.removeChild(el));

      // 解析像素值
      const parsePixelValue = (value: string): number => {
        if (!value || value === 'auto' || value === '0px') return 0;
        const match = value.match(/^(\d+(?:\.\d+)?)px$/);
        return match ? parseFloat(match[1]) : 0;
      };

      const insets: SafeAreaInsets = {
        top: parsePixelValue(computedStyles.top),
        bottom: parsePixelValue(computedStyles.bottom),
        left: parsePixelValue(computedStyles.left),
        right: parsePixelValue(computedStyles.right)
      };

      // 检查是否有有效的Safe Area值
      const hasValidInsets = insets.top > 0 || insets.bottom > 0 || insets.left > 0 || insets.right > 0;

      return hasValidInsets ? insets : null;
    } catch (error) {
      console.warn('⚠️ Safe Area检测失败:', error);
      return null;
    }
  }, []);

  /**
   * 获取fallback Safe Area值（基于设备类型的预估值）
   */
  const getFallbackInsets = useCallback((): SafeAreaInsets => {
    if (!isIOSDevice || !isNative) {
      return { top: 0, bottom: 0, left: 0, right: 0 };
    }

    // iOS设备的典型Safe Area值
    return {
      top: 47,    // iPhone刘海屏状态栏高度
      bottom: 34, // iPhone Home Indicator高度
      left: 0,
      right: 0
    };
  }, [isIOSDevice, isNative]);

  useEffect(() => {
    if (!isIOSDevice || !isNative) {
      // 非iOS设备直接标记为就绪
      setState({
        isReady: true,
        insets: { top: 0, bottom: 0, left: 0, right: 0 },
        initializationTime: 0,
        checkCount: 0
      });
      return;
    }

    const startTime = Date.now();
    let checkCount = 0;
    let timeoutId: NodeJS.Timeout;

    console.log('🔍 开始监控iOS Safe Area初始化...');

    const monitor = setInterval(() => {
      checkCount++;
      const currentTime = Date.now() - startTime;

      const insets = checkSafeAreaAvailability();

      console.log(`📊 Safe Area检查 #${checkCount} (${currentTime}ms):`, {
        insets,
        isReady: !!insets
      });

      if (insets) {
        // Safe Area值已可用
        clearInterval(monitor);
        clearTimeout(timeoutId);

        setState({
          isReady: true,
          insets,
          initializationTime: currentTime,
          checkCount
        });

        console.log(`✅ Safe Area初始化完成，用时${currentTime}ms，检查${checkCount}次`);
        console.log('📐 最终Safe Area值:', insets);
      } else {
        // 更新检查次数
        setState(prev => ({ ...prev, checkCount }));
      }
    }, checkInterval);

    // 设置超时处理
    timeoutId = setTimeout(() => {
      clearInterval(monitor);
      
      const fallbackInsets = getFallbackInsets();
      
      setState({
        isReady: true,
        insets: fallbackInsets,
        initializationTime: maxWaitTime,
        checkCount
      });

      console.warn(`⚠️ Safe Area初始化超时(${maxWaitTime}ms)，使用fallback值:`, fallbackInsets);
    }, maxWaitTime);

    return () => {
      clearInterval(monitor);
      clearTimeout(timeoutId);
    };
  }, [isIOSDevice, isNative, maxWaitTime, checkInterval, checkSafeAreaAvailability, getFallbackInsets]);

  return state;
};

/**
 * 简化版Hook，只返回是否就绪状态
 */
export const useIsSafeAreaReady = (maxWaitTime?: number): boolean => {
  const { isReady } = useSafeAreaReady(maxWaitTime);
  return isReady;
};

/**
 * 获取Safe Area insets值的Hook
 */
export const useSafeAreaInsets = (): SafeAreaInsets => {
  const { insets } = useSafeAreaReady();
  return insets;
};
