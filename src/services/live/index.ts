/**
 * 视频转动态卡片服务模块统一导出
 */

// 视频转卡片服务
export * from './LiveService';
export { liveService } from './LiveService';

// 视频转卡片服务配置
export interface LiveServiceConfig {
  enableCache: boolean;
  cacheTimeout: number;
  enableiOSFeedback: boolean;
  enableVideoProcessing: boolean;
  maxFileSize: number;
  supportedFormats: string[];
  defaultCardAspectRatio: '3:4' | '4:3' | '1:1' | '16:9';
}

// 默认配置
export const DEFAULT_LIVE_CONFIG: LiveServiceConfig = {
  enableCache: true,
  cacheTimeout: 5 * 60 * 1000, // 5分钟
  enableiOSFeedback: true,
  enableVideoProcessing: true,
  maxFileSize: 100 * 1024 * 1024, // 100MB
  supportedFormats: ['mp4', 'webm', 'mov', 'avi', 'livephoto', 'gif'],
  defaultCardAspectRatio: '3:4'
};

// 视频转卡片服务管理器
export class LiveServiceManager {
  private static instance: LiveServiceManager;
  private config: LiveServiceConfig;

  private constructor(config: LiveServiceConfig = DEFAULT_LIVE_CONFIG) {
    this.config = config;
  }

  public static getInstance(config?: LiveServiceConfig): LiveServiceManager {
    if (!LiveServiceManager.instance) {
      LiveServiceManager.instance = new LiveServiceManager(config);
    }
    return LiveServiceManager.instance;
  }

  public getConfig(): LiveServiceConfig {
    return this.config;
  }

  public updateConfig(newConfig: Partial<LiveServiceConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }

  /**
   * 验证文件格式
   */
  public isFormatSupported(format: string): boolean {
    return this.config.supportedFormats.includes(format.toLowerCase());
  }

  /**
   * 验证文件大小
   */
  public isFileSizeValid(size: number): boolean {
    return size <= this.config.maxFileSize;
  }

  /**
   * 验证卡片配置
   */
  public validateCardConfig(aspectRatio: string): boolean {
    const validRatios = ['3:4', '4:3', '1:1', '16:9'];
    return validRatios.includes(aspectRatio);
  }

  /**
   * 清除所有视频卡片缓存
   */
  public async clearAllCaches(): Promise<void> {
    try {
      const { liveService } = await import('./LiveService');
      await liveService.clearAllCache();
      console.log('🗑️ [LiveServiceManager] 所有视频卡片缓存已清除');
    } catch (error) {
      console.warn('⚠️ [LiveServiceManager] 清除缓存失败:', error);
    }
  }
}

// 导出默认管理器实例
export const liveServiceManager = LiveServiceManager.getInstance();
