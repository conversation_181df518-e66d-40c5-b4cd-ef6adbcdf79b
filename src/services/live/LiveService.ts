/**
 * 视频转动态卡片服务模块
 * 提供视频转换为动态卡片功能，专为iOS端优化
 * 支持多种输出格式和智能缓存策略
 */

import { ApiClient } from '../core/ApiClient';
import { CacheManager, CacheKeyBuilder } from '../cache';
import GlobalCacheManager from '../cache/GlobalCacheManager';
import {
  VideoToCardRequest,
  CardTemplate,
  CardGenerationResult,
  CardStyleType,
  FileUploadRequest,
  FileUploadResult,
  ChartConfig,
  ChartData
} from '../../models/api/live/Live';

// 视频转卡片操作结果
export interface VideoCardOperationResult<T = any> {
  success: boolean;
  data?: T;
  error?: {
    code: string;
    message: string;
    retryable: boolean;
  };
}

// iOS反馈配置
export interface iOSVideoCardFeedback {
  hapticStyle?: 'light' | 'medium' | 'heavy';
  statusBarStyle?: 'light' | 'dark';
  notificationTitle?: string;
  notificationBody?: string;
  showProgressIndicator?: boolean;
}

/**
 * 视频转动态卡片服务类
 */
export class LiveService {
  private cacheManager: CacheManager | null = null;

  // 缓存配置
  private readonly CACHE_CONFIG = {
    CARD_TEMPLATES: { ttl: 24 * 60 * 60 * 1000, persistent: true }, // 24小时
    GENERATED_CARDS: { ttl: 7 * 24 * 60 * 60 * 1000, persistent: true }, // 7天
    PROCESSING_STATUS: { ttl: 60 * 60 * 1000, persistent: true }, // 1小时
    USER_CARDS: { ttl: 60 * 60 * 1000, persistent: true }, // 1小时
  };

  constructor(private apiClient: ApiClient) {}

  /**
   * 获取缓存管理器实例
   */
  private async getCacheManager(): Promise<CacheManager> {
    if (!this.cacheManager) {
      this.cacheManager = await GlobalCacheManager.getInstance();
    }
    return this.cacheManager;
  }

  /**
   * 生成缓存键
   */
  private generateCacheKey(type: string, params: any = {}): string {
    return CacheKeyBuilder.create()
      .namespace('video-card')
      .resource(type)
      .params(params)
      .build();
  }

  /**
   * 提供iOS原生反馈
   */
  private async provideiOSFeedback(config: iOSVideoCardFeedback): Promise<void> {
    try {
      if (!this.apiClient.isNative || this.apiClient.platform !== 'ios') {
        return;
      }

      // 触觉反馈
      if (config.hapticStyle) {
        const { Haptics, ImpactStyle } = await import('@capacitor/haptics');
        const style = config.hapticStyle === 'light' ? ImpactStyle.Light :
                     config.hapticStyle === 'medium' ? ImpactStyle.Medium : ImpactStyle.Heavy;
        await Haptics.impact({ style });
      }

      // 状态栏样式
      if (config.statusBarStyle) {
        const { StatusBar, Style } = await import('@capacitor/status-bar');
        const style = config.statusBarStyle === 'light' ? Style.Light : Style.Dark;
        await StatusBar.setStyle({ style });
      }

      // 本地通知
      if (config.notificationTitle && config.notificationBody) {
        const { LocalNotifications } = await import('@capacitor/local-notifications');
        await LocalNotifications.schedule({
          notifications: [{
            title: config.notificationTitle,
            body: config.notificationBody,
            id: Date.now(),
            schedule: { at: new Date(Date.now() + 100) }
          }]
        });
      }

      // 显示进度指示器（iOS专用）
      if (config.showProgressIndicator) {
        // 可以集成iOS原生进度指示器
        console.log('📱 [LiveService] 显示iOS进度指示器');
      }

    } catch (error) {
      console.warn('⚠️ [LiveService] iOS反馈失败:', error);
    }
  }

  /**
   * 视频转动态卡片
   */
  public async convertVideoToCard(
    request: VideoToCardRequest
  ): Promise<VideoCardOperationResult<CardGenerationResult>> {
    try {
      console.log('[LiveService] 开始视频转卡片:', {
        source: request.video_source,
        format: request.processing_options.output_format,
        quality: request.processing_options.quality
      });

      // iOS触觉反馈 - 开始处理
      await this.provideiOSFeedback({
        hapticStyle: 'medium',
        notificationTitle: '开始处理视频',
        notificationBody: '正在将视频转换为动态卡片...',
        showProgressIndicator: true
      });

      const response = await this.apiClient.post<CardGenerationResult>(
        '/api/v1/video-cards/convert',
        request
      );

      // 缓存生成结果
      const cacheKey = this.generateCacheKey('generated', { id: response.id });
      const cacheManager = await this.getCacheManager();
      await cacheManager.set(cacheKey, response, this.CACHE_CONFIG.GENERATED_CARDS);

      // iOS触觉反馈 - 处理完成
      await this.provideiOSFeedback({
        hapticStyle: 'heavy',
        notificationTitle: '卡片生成完成',
        notificationBody: '您的动态卡片已准备就绪！'
      });

      console.log('✅ [LiveService] 视频转卡片成功');

      return {
        success: true,
        data: response
      };

    } catch (error) {
      console.error('❌ [LiveService] 视频转卡片失败:', error);
      return {
        success: false,
        error: {
          code: 'VIDEO_TO_CARD_FAILED',
          message: error instanceof Error ? error.message : '视频转卡片失败',
          retryable: true
        }
      };
    }
  }

  /**
   * 获取卡片模板列表
   */
  public async getCardTemplates(
    styleType?: CardStyleType,
    forceRefresh: boolean = false
  ): Promise<VideoCardOperationResult<CardTemplate[]>> {
    const cacheKey = this.generateCacheKey('templates', { styleType });

    try {
      console.log('[LiveService] 获取卡片模板:', { styleType, forceRefresh });

      // 检查缓存
      if (!forceRefresh) {
        const cacheManager = await this.getCacheManager();
        const cachedTemplates = await cacheManager.get<CardTemplate[]>(cacheKey);
        if (cachedTemplates) {
          console.log('🎯 [LiveService] 卡片模板缓存命中');
          return {
            success: true,
            data: cachedTemplates
          };
        }
      }

      // 构建查询参数
      const queryParams: any = {};
      if (styleType) queryParams.style_type = styleType;

      // 发送API请求
      const response = await this.apiClient.get<CardTemplate[]>(
        '/api/v1/video-cards/templates',
        queryParams
      );

      // 缓存结果
      const cacheManager = await this.getCacheManager();
      await cacheManager.set(cacheKey, response, this.CACHE_CONFIG.CARD_TEMPLATES);

      console.log('✅ [LiveService] 卡片模板获取成功');

      return {
        success: true,
        data: response
      };

    } catch (error) {
      console.error('❌ [LiveService] 获取卡片模板失败:', error);
      return {
        success: false,
        error: {
          code: 'GET_CARD_TEMPLATES_FAILED',
          message: error instanceof Error ? error.message : '获取卡片模板失败',
          retryable: true
        }
      };
    }
  }

  /**
   * 获取卡片生成状态
   */
  public async getCardGenerationStatus(
    cardId: string,
    forceRefresh: boolean = false
  ): Promise<VideoCardOperationResult<CardGenerationResult>> {
    const cacheKey = this.generateCacheKey('status', { cardId });

    try {
      console.log('[LiveService] 获取卡片生成状态:', { cardId, forceRefresh });

      // 检查缓存
      if (!forceRefresh) {
        const cacheManager = await this.getCacheManager();
        const cachedStatus = await cacheManager.get<CardGenerationResult>(cacheKey);
        if (cachedStatus) {
          console.log('🎯 [LiveService] 卡片状态缓存命中');
          return {
            success: true,
            data: cachedStatus
          };
        }
      }

      // 从API获取
      const response = await this.apiClient.get<CardGenerationResult>(
        `/api/v1/video-cards/status/${cardId}`
      );

      // 缓存结果
      const cacheManager = await this.getCacheManager();
      await cacheManager.set(cacheKey, response, this.CACHE_CONFIG.PROCESSING_STATUS);

      console.log('✅ [LiveService] 卡片生成状态获取成功');

      return {
        success: true,
        data: response
      };

    } catch (error) {
      console.error('❌ [LiveService] 获取卡片生成状态失败:', error);
      return {
        success: false,
        error: {
          code: 'GET_CARD_STATUS_FAILED',
          message: error instanceof Error ? error.message : '获取卡片生成状态失败',
          retryable: true
        }
      };
    }
  }

  /**
   * 下载生成的卡片
   */
  public async downloadCard(
    cardId: string
  ): Promise<VideoCardOperationResult<string>> {
    try {
      console.log('[LiveService] 下载卡片:', { cardId });

      const response = await this.apiClient.get<{ download_url: string }>(
        `/api/v1/video-cards/download/${cardId}`
      );

      // iOS触觉反馈
      await this.provideiOSFeedback({
        hapticStyle: 'light',
        notificationTitle: '卡片下载完成',
        notificationBody: '动态卡片已保存到相册'
      });

      console.log('✅ [LiveService] 卡片下载成功');

      return {
        success: true,
        data: response.download_url
      };

    } catch (error) {
      console.error('❌ [LiveService] 下载卡片失败:', error);
      return {
        success: false,
        error: {
          code: 'DOWNLOAD_CARD_FAILED',
          message: error instanceof Error ? error.message : '下载卡片失败',
          retryable: true
        }
      };
    }
  }

  /**
   * 获取用户的卡片历史
   */
  public async getUserCards(
    userId: number,
    page: number = 0,
    forceRefresh: boolean = false
  ): Promise<VideoCardOperationResult<CardGenerationResult[]>> {
    const cacheKey = this.generateCacheKey('user-cards', { userId, page });

    try {
      console.log('[LiveService] 获取用户卡片历史:', { userId, page, forceRefresh });

      // 检查缓存
      if (!forceRefresh) {
        const cacheManager = await this.getCacheManager();
        const cachedCards = await cacheManager.get<CardGenerationResult[]>(cacheKey);
        if (cachedCards) {
          console.log('🎯 [LiveService] 用户卡片缓存命中');
          return {
            success: true,
            data: cachedCards
          };
        }
      }

      // 构建查询参数
      const queryParams = {
        skip: String(page * 20),
        limit: '20'
      };

      // 发送API请求
      const response = await this.apiClient.get<CardGenerationResult[]>(
        `/api/v1/video-cards/user/${userId}`,
        queryParams
      );

      // 缓存结果
      const cacheManager = await this.getCacheManager();
      await cacheManager.set(cacheKey, response, this.CACHE_CONFIG.USER_CARDS);

      console.log('✅ [LiveService] 用户卡片历史获取成功');

      return {
        success: true,
        data: response
      };

    } catch (error) {
      console.error('❌ [LiveService] 获取用户卡片历史失败:', error);
      return {
        success: false,
        error: {
          code: 'GET_USER_CARDS_FAILED',
          message: error instanceof Error ? error.message : '获取用户卡片历史失败',
          retryable: true
        }
      };
    }
  }

  /**
   * 删除生成的卡片
   */
  public async deleteCard(
    cardId: string
  ): Promise<VideoCardOperationResult<void>> {
    try {
      console.log('[LiveService] 删除卡片:', { cardId });

      await this.apiClient.delete(`/api/v1/video-cards/${cardId}`);

      // 失效相关缓存
      await this.invalidateCardCache(cardId);

      // iOS触觉反馈
      await this.provideiOSFeedback({
        hapticStyle: 'light',
        notificationTitle: '卡片已删除',
        notificationBody: '动态卡片已从历史中移除'
      });

      console.log('✅ [LiveService] 卡片删除成功');

      return {
        success: true
      };

    } catch (error) {
      console.error('❌ [LiveService] 删除卡片失败:', error);
      return {
        success: false,
        error: {
          code: 'DELETE_CARD_FAILED',
          message: error instanceof Error ? error.message : '删除卡片失败',
          retryable: true
        }
      };
    }
  }

  /**
   * 上传文件
   */
  public async uploadFile(
    request: FileUploadRequest
  ): Promise<VideoCardOperationResult<FileUploadResult>> {
    try {
      console.log('[LiveService] 上传文件:', {
        fileName: request.file_name,
        fileType: request.file_type
      });

      const response = await this.apiClient.post<FileUploadResult>(
        '/api/v1/media/upload',
        request
      );

      // iOS触觉反馈
      await this.provideiOSFeedback({
        hapticStyle: 'medium',
        notificationTitle: '文件上传完成',
        notificationBody: `${request.file_name}上传成功`
      });

      console.log('✅ [LiveService] 文件上传成功');

      return {
        success: true,
        data: response
      };

    } catch (error) {
      console.error('❌ [LiveService] 上传文件失败:', error);
      return {
        success: false,
        error: {
          code: 'UPLOAD_FILE_FAILED',
          message: error instanceof Error ? error.message : '上传文件失败',
          retryable: true
        }
      };
    }
  }

  /**
   * 获取图表数据
   */
  public async getChartData(
    config: ChartConfig,
    forceRefresh: boolean = false
  ): Promise<VideoCardOperationResult<ChartData>> {
    const cacheKey = this.generateCacheKey('chart-data', {
      dataSource: config.data_source,
      dateRange: config.data_config.date_range
    });

    try {
      console.log('[LiveService] 获取图表数据:', {
        dataSource: config.data_source,
        forceRefresh
      });

      // 检查缓存
      if (!forceRefresh) {
        const cacheManager = await this.getCacheManager();
        const cachedData = await cacheManager.get<ChartData>(cacheKey);
        if (cachedData) {
          console.log('🎯 [LiveService] 图表数据缓存命中');
          return {
            success: true,
            data: cachedData
          };
        }
      }

      // 从API获取
      const response = await this.apiClient.post<ChartData>(
        '/api/v1/charts/data',
        config.data_config
      );

      // 缓存结果
      const cacheManager = await this.getCacheManager();
      await cacheManager.set(cacheKey, response, { ttl: 10 * 60 * 1000, persistent: true });

      console.log('✅ [LiveService] 图表数据获取成功');

      return {
        success: true,
        data: response
      };

    } catch (error) {
      console.error('❌ [LiveService] 获取图表数据失败:', error);
      return {
        success: false,
        error: {
          code: 'GET_CHART_DATA_FAILED',
          message: error instanceof Error ? error.message : '获取图表数据失败',
          retryable: true
        }
      };
    }
  }

  /**
   * 失效卡片相关缓存
   */
  private async invalidateCardCache(cardId: string): Promise<void> {
    try {
      const cacheManager = await this.getCacheManager();

      // 失效特定卡片缓存
      const cardKey = this.generateCacheKey('generated', { id: cardId });
      await cacheManager.delete(cardKey);

      // 失效用户卡片列表缓存
      await cacheManager.invalidate(['video-card:user-cards']);

      console.log('🗑️ [LiveService] 卡片缓存失效完成');
    } catch (error) {
      console.warn('⚠️ [LiveService] 缓存失效失败:', error);
    }
  }

  /**
   * 清除所有视频卡片缓存
   */
  public async clearAllCache(): Promise<void> {
    try {
      const cacheManager = await this.getCacheManager();
      await cacheManager.clear('video-card:*');
      console.log('🗑️ [LiveService] 所有视频卡片缓存已清除');
    } catch (error) {
      console.warn('⚠️ [LiveService] 清除缓存失败:', error);
    }
  }
}

// 导出默认实例
export const liveService = new LiveService(new (await import('../core/ApiClient')).ApiClient());
