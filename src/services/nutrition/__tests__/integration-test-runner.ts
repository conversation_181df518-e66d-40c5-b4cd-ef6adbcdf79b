/**
 * 营养模块集成测试运行器
 * 用于验证营养服务模块的整体功能和性能
 */

import { FoodService } from '../FoodService';
// import { FoodbaseService } from '../FoodbaseService';
import { MealService } from '../MealService';
// import { NutrientService } from '../NutrientService';
import { ApiClient } from '../../core/ApiClient';

// 测试配置
interface TestConfig {
  enableMockData: boolean;
  enablePerformanceTest: boolean;
  enableiOSTest: boolean;
  testTimeout: number;
}

const DEFAULT_TEST_CONFIG: TestConfig = {
  enableMockData: true,
  enablePerformanceTest: true,
  enableiOSTest: true,
  testTimeout: 30000 // 30秒
};

// 测试结果
interface TestResult {
  testName: string;
  success: boolean;
  duration: number;
  error?: string;
  details?: any;
}

// 测试套件结果
interface TestSuiteResult {
  suiteName: string;
  totalTests: number;
  passedTests: number;
  failedTests: number;
  totalDuration: number;
  results: TestResult[];
}

/**
 * 营养模块集成测试运行器
 */
export class NutritionIntegrationTestRunner {
  private config: TestConfig;
  private foodService: FoodService;
  // private foodbaseService: FoodbaseService;
  private mealService: MealService;
  // private nutrientService: NutrientService;

  constructor(config: TestConfig = DEFAULT_TEST_CONFIG) {
    this.config = config;
    
    // 初始化服务（使用模拟或真实API客户端）
    const apiClient = this.createApiClient();
    this.foodService = new FoodService(apiClient);
    // this.foodbaseService = new FoodbaseService(apiClient);
    this.mealService = new MealService(apiClient);
    // this.nutrientService = new NutrientService(apiClient);
  }

  /**
   * 创建API客户端（模拟或真实）
   */
  private createApiClient(): ApiClient {
    if (this.config.enableMockData) {
      // 返回模拟的API客户端
      return {
        get: async (url: string, params?: any) => this.mockApiResponse(url, 'GET', params),
        post: async (url: string, data?: any) => this.mockApiResponse(url, 'POST', data),
        put: async (url: string, data?: any) => this.mockApiResponse(url, 'PUT', data),
        delete: async (url: string) => this.mockApiResponse(url, 'DELETE'),
        isNative: true,
        platform: 'ios'
      } as any;
    } else {
      // 返回真实的API客户端
      return new ApiClient();
    }
  }

  /**
   * 模拟API响应
   */
  private async mockApiResponse(url: string, method: string, _data?: any): Promise<any> {
    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, Math.random() * 100 + 50));

    // 根据URL返回模拟数据
    if (url.includes('/api/v1/food/')) {
      if (method === 'GET' && url.includes('/api/v1/food/')) {
        return {
          foods: [
            {
              id: 1,
              name: '苹果',
              category: '水果',
              nutrition: {
                food_id: 1,
                serving_size: 100,
                serving_unit: 'g',
                calories: 52,
                protein: 0.3,
                carbohydrates: 14,
                fat: 0.2
              }
            }
          ],
          total: 1,
          skip: 0,
          limit: 20,
          has_more: false
        };
      }
    }

    if (url.includes('/api/v1/meal/')) {
      return {
        id: 1,
        meal_type: 'breakfast',
        meal_date: '2024-08-01',
        total_calories: 300,
        total_protein: 15,
        total_carbohydrates: 45,
        total_fat: 10
      };
    }

    // 默认返回成功响应
    return { success: true, data: {} };
  }

  /**
   * 运行单个测试
   */
  private async runTest(testName: string, testFunction: () => Promise<any>): Promise<TestResult> {
    const startTime = Date.now();
    
    try {
      console.log(`🧪 运行测试: ${testName}`);
      
      const result = await Promise.race([
        testFunction(),
        new Promise((_, reject) => 
          setTimeout(() => reject(new Error('Test timeout')), this.config.testTimeout)
        )
      ]);

      const duration = Date.now() - startTime;
      console.log(`✅ 测试通过: ${testName} (${duration}ms)`);

      return {
        testName,
        success: true,
        duration,
        details: result
      };

    } catch (error) {
      const duration = Date.now() - startTime;
      console.error(`❌ 测试失败: ${testName} (${duration}ms)`, error);

      return {
        testName,
        success: false,
        duration,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  /**
   * 运行FoodService测试套件
   */
  private async runFoodServiceTests(): Promise<TestSuiteResult> {
    console.log('\n🍎 开始FoodService测试套件');
    
    const tests = [
      {
        name: '食物搜索功能',
        test: () => this.foodService.searchFoods({ name: '苹果' })
      },
      {
        name: '食物详情获取',
        test: () => this.foodService.getFoodDetail(1)
      },
      {
        name: '食物收藏功能',
        test: () => this.foodService.updateFoodFavorite(1, true)
      },
      {
        name: '食物评分功能',
        test: () => this.foodService.rateFoodItem(1, 5)
      },
      {
        name: '批量食物查询',
        test: () => this.foodService.getBatchFoods([1, 2, 3])
      }
    ];

    const results: TestResult[] = [];
    const startTime = Date.now();

    for (const test of tests) {
      const result = await this.runTest(test.name, test.test);
      results.push(result);
    }

    const totalDuration = Date.now() - startTime;
    const passedTests = results.filter(r => r.success).length;
    const failedTests = results.filter(r => !r.success).length;

    return {
      suiteName: 'FoodService',
      totalTests: tests.length,
      passedTests,
      failedTests,
      totalDuration,
      results
    };
  }

  /**
   * 运行MealService测试套件
   */
  private async runMealServiceTests(): Promise<TestSuiteResult> {
    console.log('\n🍽️ 开始MealService测试套件');
    
    const tests = [
      {
        name: '创建餐食记录',
        test: () => this.mealService.createMealRecord({
          meal_type: 'breakfast',
          meal_date: '2024-08-01',
          items: [{ food_id: 1, quantity: 100, unit: 'g' }]
        })
      },
      {
        name: '获取餐食记录',
        test: () => this.mealService.getMealRecords()
      },
      {
        name: '每日营养摘要',
        test: () => this.mealService.getDailyNutritionSummary('2024-08-01')
      },
      {
        name: '营养目标管理',
        test: () => this.mealService.getNutritionGoals()
      }
    ];

    const results: TestResult[] = [];
    const startTime = Date.now();

    for (const test of tests) {
      const result = await this.runTest(test.name, test.test);
      results.push(result);
    }

    const totalDuration = Date.now() - startTime;
    const passedTests = results.filter(r => r.success).length;
    const failedTests = results.filter(r => !r.success).length;

    return {
      suiteName: 'MealService',
      totalTests: tests.length,
      passedTests,
      failedTests,
      totalDuration,
      results
    };
  }

  /**
   * 运行性能测试
   */
  private async runPerformanceTests(): Promise<TestSuiteResult> {
    if (!this.config.enablePerformanceTest) {
      return {
        suiteName: 'Performance',
        totalTests: 0,
        passedTests: 0,
        failedTests: 0,
        totalDuration: 0,
        results: []
      };
    }

    console.log('\n⚡ 开始性能测试套件');
    
    const tests = [
      {
        name: '食物搜索响应时间',
        test: async () => {
          const startTime = Date.now();
          await this.foodService.searchFoods({ name: '苹果' });
          const duration = Date.now() - startTime;
          
          if (duration > 3000) {
            throw new Error(`响应时间过长: ${duration}ms`);
          }
          
          return { duration };
        }
      },
      {
        name: '缓存性能测试',
        test: async () => {
          // 第一次调用
          const start1 = Date.now();
          await this.foodService.searchFoods({ name: '苹果' });
          const firstCall = Date.now() - start1;

          // 第二次调用（应该使用缓存）
          const start2 = Date.now();
          await this.foodService.searchFoods({ name: '苹果' });
          const secondCall = Date.now() - start2;

          return {
            firstCall,
            secondCall,
            improvement: firstCall - secondCall
          };
        }
      }
    ];

    const results: TestResult[] = [];
    const startTime = Date.now();

    for (const test of tests) {
      const result = await this.runTest(test.name, test.test);
      results.push(result);
    }

    const totalDuration = Date.now() - startTime;
    const passedTests = results.filter(r => r.success).length;
    const failedTests = results.filter(r => !r.success).length;

    return {
      suiteName: 'Performance',
      totalTests: tests.length,
      passedTests,
      failedTests,
      totalDuration,
      results
    };
  }

  /**
   * 运行所有测试
   */
  public async runAllTests(): Promise<TestSuiteResult[]> {
    console.log('🚀 开始营养模块集成测试');
    console.log(`配置: ${JSON.stringify(this.config, null, 2)}`);

    const suiteResults: TestSuiteResult[] = [];

    try {
      // 运行各个测试套件
      suiteResults.push(await this.runFoodServiceTests());
      suiteResults.push(await this.runMealServiceTests());
      suiteResults.push(await this.runPerformanceTests());

      // 打印总结报告
      this.printSummaryReport(suiteResults);

      return suiteResults;

    } catch (error) {
      console.error('❌ 测试运行失败:', error);
      throw error;
    }
  }

  /**
   * 打印总结报告
   */
  private printSummaryReport(suiteResults: TestSuiteResult[]): void {
    console.log('\n📊 测试总结报告');
    console.log('='.repeat(50));

    let totalTests = 0;
    let totalPassed = 0;
    let totalFailed = 0;
    let totalDuration = 0;

    suiteResults.forEach(suite => {
      totalTests += suite.totalTests;
      totalPassed += suite.passedTests;
      totalFailed += suite.failedTests;
      totalDuration += suite.totalDuration;

      console.log(`\n${suite.suiteName}:`);
      console.log(`  总测试数: ${suite.totalTests}`);
      console.log(`  通过: ${suite.passedTests}`);
      console.log(`  失败: ${suite.failedTests}`);
      console.log(`  耗时: ${suite.totalDuration}ms`);
      console.log(`  成功率: ${((suite.passedTests / suite.totalTests) * 100).toFixed(1)}%`);
    });

    console.log('\n总体统计:');
    console.log(`  总测试数: ${totalTests}`);
    console.log(`  通过: ${totalPassed}`);
    console.log(`  失败: ${totalFailed}`);
    console.log(`  总耗时: ${totalDuration}ms`);
    console.log(`  整体成功率: ${((totalPassed / totalTests) * 100).toFixed(1)}%`);

    if (totalFailed === 0) {
      console.log('\n🎉 所有测试通过！');
    } else {
      console.log(`\n⚠️ 有 ${totalFailed} 个测试失败，请检查详细日志`);
    }
  }
}

// 导出测试运行器
export default NutritionIntegrationTestRunner;
