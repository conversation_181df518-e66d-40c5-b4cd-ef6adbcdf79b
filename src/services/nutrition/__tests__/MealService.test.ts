/**
 * MealService 集成测试
 * 测试餐食服务的核心功能，包括餐食记录、计划、摘要等
 */

import { MealService } from '../MealService';
import { ApiClient } from '../../core/ApiClient';

// 模拟ApiClient
jest.mock('../../core/ApiClient');
const MockedApiClient = ApiClient as jest.MockedClass<typeof ApiClient>;

// 模拟Capacitor插件
jest.mock('@capacitor/haptics', () => ({
  Haptics: {
    impact: jest.fn()
  },
  ImpactStyle: {
    Light: 'LIGHT',
    Medium: 'MEDIUM',
    Heavy: 'HEAVY'
  }
}));

jest.mock('@capacitor/local-notifications', () => ({
  LocalNotifications: {
    schedule: jest.fn()
  }
}));

describe('MealService 集成测试', () => {
  let mealService: MealService;
  let mockApiClient: jest.Mocked<ApiClient>;

  beforeEach(() => {
    jest.clearAllMocks();
    
    mockApiClient = {
      get: jest.fn(),
      post: jest.fn(),
      put: jest.fn(),
      delete: jest.fn(),
      isNative: true,
      platform: 'ios'
    } as any;

    MockedApiClient.mockImplementation(() => mockApiClient);
    mealService = new MealService(mockApiClient);
  });

  describe('餐食记录管理', () => {
    const mockMealRecord = {
      id: 1,
      user_id: 1,
      meal_type: 'breakfast',
      meal_date: '2024-08-01',
      meal_time: '08:00',
      items: [
        {
          id: 1,
          meal_id: 1,
          food_id: 1,
          food: {
            id: 1,
            name: '苹果',
            category: '水果'
          },
          quantity: 150,
          unit: 'g',
          weight_grams: 150,
          calories: 78,
          protein: 0.45,
          carbohydrates: 21,
          fat: 0.3
        }
      ],
      total_calories: 78,
      total_protein: 0.45,
      total_carbohydrates: 21,
      total_fat: 0.3,
      created_at: '2024-08-01T08:00:00Z',
      updated_at: '2024-08-01T08:00:00Z'
    };

    test('创建餐食记录', async () => {
      mockApiClient.post.mockResolvedValue(mockMealRecord);

      const mealRequest = {
        meal_type: 'breakfast' as const,
        meal_date: '2024-08-01',
        meal_time: '08:00',
        items: [
          {
            food_id: 1,
            quantity: 150,
            unit: 'g'
          }
        ]
      };

      const result = await mealService.createMealRecord(mealRequest);

      expect(result.success).toBe(true);
      expect(result.data?.meal_type).toBe('breakfast');
      expect(result.data?.items.length).toBe(1);
      expect(mockApiClient.post).toHaveBeenCalledWith(
        '/api/v1/meal/records',
        mealRequest
      );
    });

    test('获取餐食记录列表', async () => {
      const mockMealRecords = [mockMealRecord];
      mockApiClient.get.mockResolvedValue(mockMealRecords);

      const result = await mealService.getMealRecords({
        start_date: '2024-08-01',
        end_date: '2024-08-07'
      });

      expect(result.success).toBe(true);
      expect(result.data?.meals.length).toBe(1);
      expect(mockApiClient.get).toHaveBeenCalledWith(
        '/api/v1/meal/records',
        expect.objectContaining({
          skip: '0',
          limit: '20',
          start_date: '2024-08-01',
          end_date: '2024-08-07'
        })
      );
    });

    test('获取餐食记录详情', async () => {
      mockApiClient.get.mockResolvedValue(mockMealRecord);

      const result = await mealService.getMealRecord(1);

      expect(result.success).toBe(true);
      expect(result.data?.id).toBe(1);
      expect(result.data?.meal_type).toBe('breakfast');
      expect(mockApiClient.get).toHaveBeenCalledWith('/api/v1/meal/records/1');
    });

    test('更新餐食记录', async () => {
      const updatedMealRecord = {
        ...mockMealRecord,
        meal_name: '健康早餐',
        notes: '营养丰富'
      };
      mockApiClient.put.mockResolvedValue(updatedMealRecord);

      const updateRequest = {
        meal_name: '健康早餐',
        notes: '营养丰富'
      };

      const result = await mealService.updateMealRecord(1, updateRequest);

      expect(result.success).toBe(true);
      expect(result.data?.meal_name).toBe('健康早餐');
      expect(mockApiClient.put).toHaveBeenCalledWith(
        '/api/v1/meal/records/1',
        updateRequest
      );
    });

    test('删除餐食记录', async () => {
      mockApiClient.delete.mockResolvedValue({});

      const result = await mealService.deleteMealRecord(1);

      expect(result.success).toBe(true);
      expect(mockApiClient.delete).toHaveBeenCalledWith('/api/v1/meals/1');
    });

    test('复制餐食记录', async () => {
      mockApiClient.post.mockResolvedValue({
        ...mockMealRecord,
        id: 2,
        meal_date: '2024-08-02'
      });

      const copyRequest = {
        source_meal_id: 1,
        target_date: '2024-08-02',
        target_meal_type: 'lunch' as const
      };

      const result = await mealService.copyMealRecord(copyRequest);

      expect(result.success).toBe(true);
      expect(result.data?.meal_date).toBe('2024-08-02');
      expect(mockApiClient.post).toHaveBeenCalledWith(
        '/api/v1/meals/1/copy',
        {
          target_date: copyRequest.target_date,
          target_meal_type: copyRequest.target_meal_type
        }
      );
    });
  });

  describe('每日营养摘要', () => {
    const mockDailySummary = {
      date: '2024-08-01',
      user_id: 1,
      meals: [mockMealRecord],
      total_calories: 1800,
      total_protein: 120,
      total_carbohydrates: 200,
      total_fat: 60,
      total_fiber: 25,
      total_sugar: 50,
      total_sodium: 2000,
      target_calories: 2000,
      target_protein: 150,
      target_carbohydrates: 250,
      target_fat: 70,
      calories_remaining: 200,
      protein_remaining: 30,
      carbs_remaining: 50,
      fat_remaining: 10,
      water_intake: 2000,
      meal_count: 3,
      completion_percentage: 90
    };

    test('获取每日营养摘要', async () => {
      mockApiClient.get.mockResolvedValue(mockDailySummary);

      const result = await mealService.getDailyNutritionSummary('2024-08-01');

      expect(result.success).toBe(true);
      expect(result.data?.date).toBe('2024-08-01');
      expect(result.data?.total_calories).toBe(1800);
      expect(result.data?.completion_percentage).toBe(90);
      expect(mockApiClient.get).toHaveBeenCalledWith(
        '/api/v1/meal/daily-summary/2024-08-01'
      );
    });

    test('营养摘要缓存测试', async () => {
      mockApiClient.get.mockResolvedValue(mockDailySummary);

      // 第一次调用
      await mealService.getDailyNutritionSummary('2024-08-01');
      
      // 第二次调用应该使用缓存
      await mealService.getDailyNutritionSummary('2024-08-01');

      // API应该只被调用一次（第二次使用缓存）
      expect(mockApiClient.get).toHaveBeenCalledTimes(1);
    });
  });

  describe('营养目标管理', () => {
    const mockNutritionGoals = [
      {
        id: 1,
        user_id: 1,
        goal_type: 'weight_loss',
        target_calories: 1800,
        target_protein: 120,
        target_carbohydrates: 180,
        target_fat: 60,
        start_date: '2024-08-01',
        is_active: true,
        created_at: '2024-08-01T00:00:00Z',
        updated_at: '2024-08-01T00:00:00Z'
      }
    ];

    test('获取营养目标', async () => {
      mockApiClient.get.mockResolvedValue(mockNutritionGoals);

      const result = await mealService.getNutritionGoals();

      expect(result.success).toBe(true);
      expect(result.data?.length).toBe(1);
      expect(result.data?.[0].goal_type).toBe('weight_loss');
      expect(mockApiClient.get).toHaveBeenCalledWith('/api/v1/nutrition/goals');
    });

    test('设置营养目标', async () => {
      const newGoal = {
        goal_type: 'muscle_gain' as const,
        target_calories: 2500,
        target_protein: 180,
        target_carbohydrates: 300,
        target_fat: 80,
        start_date: '2024-08-01',
        is_active: true
      };

      mockApiClient.post.mockResolvedValue({
        id: 2,
        user_id: 1,
        ...newGoal,
        created_at: '2024-08-01T00:00:00Z',
        updated_at: '2024-08-01T00:00:00Z'
      });

      const result = await mealService.setNutritionGoal(newGoal);

      expect(result.success).toBe(true);
      expect(result.data?.goal_type).toBe('muscle_gain');
      expect(result.data?.target_calories).toBe(2500);
      expect(mockApiClient.post).toHaveBeenCalledWith(
        '/api/v1/nutrition/goals',
        newGoal
      );
    });
  });

  describe('错误处理测试', () => {
    test('网络错误处理', async () => {
      mockApiClient.post.mockRejectedValue(new Error('Network Error'));

      const result = await mealService.createMealRecord({
        meal_type: 'breakfast',
        meal_date: '2024-08-01',
        items: []
      });

      expect(result.success).toBe(false);
      expect(result.error?.code).toBe('MEAL_CREATE_FAILED');
      expect(result.error?.retryable).toBe(true);
    });

    test('API错误响应处理', async () => {
      mockApiClient.get.mockRejectedValue({
        status: 404,
        message: 'Meal not found'
      });

      const result = await mealService.getMealRecord(999);

      expect(result.success).toBe(false);
      expect(result.error?.code).toBe('MEAL_RECORD_DETAIL_FAILED');
    });
  });

  describe('iOS原生功能测试', () => {
    test('创建餐食记录触觉反馈', async () => {
      const { Haptics } = await import('@capacitor/haptics');
      mockApiClient.post.mockResolvedValue(mockMealRecord);

      await mealService.createMealRecord({
        meal_type: 'breakfast',
        meal_date: '2024-08-01',
        items: [{ food_id: 1, quantity: 100, unit: 'g' }]
      });

      expect(Haptics.impact).toHaveBeenCalled();
    });

    test('设置营养目标本地通知', async () => {
      const { LocalNotifications } = await import('@capacitor/local-notifications');
      mockApiClient.post.mockResolvedValue({
        id: 1,
        goal_type: 'weight_loss',
        target_calories: 1800
      });

      await mealService.setNutritionGoal({
        goal_type: 'weight_loss',
        target_calories: 1800,
        target_protein: 120,
        target_carbohydrates: 180,
        target_fat: 60
      });

      expect(LocalNotifications.schedule).toHaveBeenCalled();
    });
  });

  describe('参数类型安全测试', () => {
    test('iOS参数字符串转换', async () => {
      mockApiClient.get.mockResolvedValue([]);

      await mealService.getMealRecords({
        min_calories: 100,
        max_calories: 500,
        has_image: true,
        is_template: false
      });

      expect(mockApiClient.get).toHaveBeenCalledWith(
        '/api/v1/meal/records',
        expect.objectContaining({
          min_calories: '100',
          max_calories: '500',
          has_image: 'true',
          is_template: 'false'
        })
      );
    });
  });

  describe('缓存管理测试', () => {
    test('缓存清除功能', async () => {
      await mealService.clearAllCache();
      expect(true).toBe(true); // 确保方法不抛出错误
    });
  });
});

// 模拟餐食记录数据
const mockMealRecord = {
  id: 1,
  user_id: 1,
  meal_type: 'breakfast' as const,
  meal_date: '2024-08-01',
  meal_time: '08:00',
  items: [
    {
      id: 1,
      meal_id: 1,
      food_id: 1,
      food: {
        id: 1,
        name: '苹果',
        category: '水果',
        nutrition: {
          food_id: 1,
          serving_size: 100,
          serving_unit: 'g',
          calories: 52,
          protein: 0.3,
          carbohydrates: 14,
          fat: 0.2
        }
      },
      quantity: 150,
      unit: 'g',
      weight_grams: 150,
      calories: 78,
      protein: 0.45,
      carbohydrates: 21,
      fat: 0.3,
      created_at: '2024-08-01T08:00:00Z',
      updated_at: '2024-08-01T08:00:00Z'
    }
  ],
  total_calories: 78,
  total_protein: 0.45,
  total_carbohydrates: 21,
  total_fat: 0.3,
  created_at: '2024-08-01T08:00:00Z',
  updated_at: '2024-08-01T08:00:00Z'
};
