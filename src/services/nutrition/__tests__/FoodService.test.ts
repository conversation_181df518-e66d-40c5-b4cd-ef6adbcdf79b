/**
 * FoodService 集成测试
 * 测试食物服务的核心功能，包括搜索、详情、收藏、评分等
 */

import { FoodService } from '../FoodService';
import { ApiClient } from '../../core/ApiClient';

// 模拟ApiClient
jest.mock('../../core/ApiClient');
const MockedApiClient = ApiClient as jest.MockedClass<typeof ApiClient>;

// 模拟Capacitor插件
jest.mock('@capacitor/haptics', () => ({
  Haptics: {
    impact: jest.fn()
  },
  ImpactStyle: {
    Light: 'LIGHT',
    Medium: 'MEDIUM',
    Heavy: 'HEAVY'
  }
}));

jest.mock('@capacitor/status-bar', () => ({
  StatusBar: {
    setStyle: jest.fn()
  },
  Style: {
    Light: 'LIGHT',
    Dark: 'DARK'
  }
}));

jest.mock('@capacitor/local-notifications', () => ({
  LocalNotifications: {
    schedule: jest.fn()
  }
}));

describe('FoodService 集成测试', () => {
  let foodService: FoodService;
  let mockApiClient: jest.Mocked<ApiClient>;

  beforeEach(() => {
    // 重置所有模拟
    jest.clearAllMocks();
    
    // 创建模拟的ApiClient实例
    mockApiClient = {
      get: jest.fn(),
      post: jest.fn(),
      put: jest.fn(),
      delete: jest.fn(),
      isNative: true,
      platform: 'ios'
    } as any;

    MockedApiClient.mockImplementation(() => mockApiClient);
    
    // 创建FoodService实例
    foodService = new FoodService(mockApiClient);
  });

  describe('食物搜索功能', () => {
    const mockSearchResult = {
      foods: [
        {
          id: 1,
          name: '苹果',
          category: '水果',
          nutrition: {
            food_id: 1,
            serving_size: 100,
            serving_unit: 'g',
            calories: 52,
            protein: 0.3,
            carbohydrates: 14,
            fat: 0.2
          }
        }
      ],
      total: 1,
      skip: 0,
      limit: 20,
      has_more: false
    };

    test('基础搜索功能', async () => {
      mockApiClient.get.mockResolvedValue(mockSearchResult);

      const result = await foodService.searchFoods({ name: '苹果' });

      expect(result.success).toBe(true);
      expect(result.data?.foods).toBeDefined();
      expect(result.data?.foods.length).toBe(1);
      expect(result.data?.foods[0].name).toBe('苹果');
      expect(mockApiClient.get).toHaveBeenCalledWith(
        '/api/v1/food/',
        expect.objectContaining({
          skip: '0',
          limit: '20',
          name: '苹果'
        })
      );
    });

    test('分页搜索功能', async () => {
      mockApiClient.get.mockResolvedValue({
        ...mockSearchResult,
        skip: 20,
        has_more: true
      });

      const result = await foodService.searchFoods({}, 1);

      expect(result.success).toBe(true);
      expect(result.data?.currentPage).toBe(1);
      expect(mockApiClient.get).toHaveBeenCalledWith(
        '/api/v1/food/',
        expect.objectContaining({
          skip: '20',
          limit: '20'
        })
      );
    });

    test('筛选条件测试', async () => {
      mockApiClient.get.mockResolvedValue(mockSearchResult);

      const filters = {
        category: '水果',
        max_calories: 100,
        min_protein: 0.1
      };

      const result = await foodService.searchFoods(filters);

      expect(result.success).toBe(true);
      expect(mockApiClient.get).toHaveBeenCalledWith(
        '/api/v1/food/',
        expect.objectContaining({
          category: '水果',
          max_calories: 100,
          min_protein: 0.1
        })
      );
    });

    test('网络错误处理', async () => {
      mockApiClient.get.mockRejectedValue(new Error('Network Error'));

      const result = await foodService.searchFoods({ name: '苹果' });

      expect(result.success).toBe(false);
      expect(result.error?.code).toBe('FOOD_SEARCH_FAILED');
      expect(result.error?.retryable).toBe(true);
    });
  });

  describe('食物详情功能', () => {
    const mockFoodDetail = {
      id: 1,
      name: '苹果',
      category: '水果',
      nutrition: {
        food_id: 1,
        serving_size: 100,
        serving_unit: 'g',
        calories: 52,
        protein: 0.3,
        carbohydrates: 14,
        fat: 0.2
      },
      is_favorite: false,
      user_rating: 0,
      avg_rating: 4.2
    };

    test('获取食物详情', async () => {
      mockApiClient.get.mockResolvedValue(mockFoodDetail);

      const result = await foodService.getFoodDetail(1);

      expect(result.success).toBe(true);
      expect(result.data?.id).toBe(1);
      expect(result.data?.name).toBe('苹果');
      expect(mockApiClient.get).toHaveBeenCalledWith('/api/v1/food/1');
    });

    test('食物详情错误处理', async () => {
      mockApiClient.get.mockRejectedValue(new Error('Food not found'));

      const result = await foodService.getFoodDetail(999);

      expect(result.success).toBe(false);
      expect(result.error?.code).toBe('FOOD_DETAIL_FAILED');
    });
  });

  describe('食物收藏功能', () => {
    test('添加食物收藏', async () => {
      mockApiClient.post.mockResolvedValue({});

      const result = await foodService.updateFoodFavorite(1, true);

      expect(result.success).toBe(true);
      expect(mockApiClient.post).toHaveBeenCalledWith(
        '/api/v1/food/1/favorite',
        { is_favorite: 'true' }
      );
    });

    test('取消食物收藏', async () => {
      mockApiClient.post.mockResolvedValue({});

      const result = await foodService.updateFoodFavorite(1, false);

      expect(result.success).toBe(true);
      expect(mockApiClient.post).toHaveBeenCalledWith(
        '/api/v1/food/1/favorite',
        { is_favorite: 'false' }
      );
    });

    test('获取收藏食物列表', async () => {
      const mockFavorites = [
        {
          id: 1,
          user_id: 1,
          food_id: 1,
          food: {
            id: 1,
            name: '苹果',
            category: '水果'
          },
          created_at: '2024-08-01T00:00:00Z'
        }
      ];

      mockApiClient.get.mockResolvedValue(mockFavorites);

      const result = await foodService.getFavoriteFoods();

      expect(result.success).toBe(true);
      expect(result.data?.foods.length).toBe(1);
      expect(mockApiClient.get).toHaveBeenCalledWith(
        '/api/v1/food/favorites',
        expect.objectContaining({
          skip: '0',
          limit: '20'
        })
      );
    });
  });

  describe('食物评分功能', () => {
    test('评分食物', async () => {
      const mockRatingResponse = {
        success: true,
        food_id: 1,
        user_rating: 5,
        avg_rating: 4.3,
        rating_count: 10
      };

      mockApiClient.post.mockResolvedValue(mockRatingResponse);

      const result = await foodService.rateFoodItem(1, 5, '很好吃');

      expect(result.success).toBe(true);
      expect(result.data?.user_rating).toBe(5);
      expect(mockApiClient.post).toHaveBeenCalledWith(
        '/api/v1/food/1/rating',
        {
          food_id: 1,
          rating: 5,
          comment: '很好吃'
        }
      );
    });
  });

  describe('食物图片识别功能', () => {
    test('分析食物图片', async () => {
      const mockAnalysisResponse = {
        success: true,
        data: {
          recognized_foods: [
            {
              food: {
                id: 1,
                name: '苹果',
                category: '水果'
              },
              confidence: 0.95,
              estimated_weight: 150,
              estimated_calories: 78
            }
          ],
          confidence: 0.95,
          meal_type: 'lunch',
          meal_date: '2024-08-01'
        }
      };

      mockApiClient.post.mockResolvedValue(mockAnalysisResponse);

      const mockImageData = 'data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQ...';
      const result = await foodService.analyzeFoodImage(mockImageData, 'lunch', '2024-08-01');

      expect(result.success).toBe(true);
      expect(result.data?.data.recognized_foods.length).toBe(1);
      expect(result.data?.data.confidence).toBe(0.95);
      expect(mockApiClient.post).toHaveBeenCalledWith(
        '/api/v1/food_recognition/analyze',
        {
          image: mockImageData,
          meal_type: 'lunch',
          meal_date: '2024-08-01',
          optimize: true
        }
      );
    });
  });

  describe('批量操作功能', () => {
    test('批量获取食物信息', async () => {
      const mockBatchResponse = {
        foods: [
          { id: 1, name: '苹果', category: '水果' },
          { id: 2, name: '香蕉', category: '水果' }
        ],
        not_found_ids: []
      };

      mockApiClient.post.mockResolvedValue(mockBatchResponse);

      const result = await foodService.getBatchFoods([1, 2], true);

      expect(result.success).toBe(true);
      expect(result.data?.foods.length).toBe(2);
      expect(mockApiClient.post).toHaveBeenCalledWith(
        '/api/v1/food/batch',
        {
          food_ids: [1, 2],
          include_nutrition: true,
          include_favorites: true
        }
      );
    });
  });

  describe('iOS原生功能测试', () => {
    test('触觉反馈功能', async () => {
      const { Haptics } = await import('@capacitor/haptics');
      mockApiClient.post.mockResolvedValue({});

      await foodService.updateFoodFavorite(1, true);

      expect(Haptics.impact).toHaveBeenCalled();
    });

    test('本地通知功能', async () => {
      const { LocalNotifications } = await import('@capacitor/local-notifications');
      const mockAnalysisResponse = {
        success: true,
        data: {
          recognized_foods: [{ food: { id: 1, name: '苹果' } }],
          confidence: 0.95,
          meal_type: 'lunch',
          meal_date: '2024-08-01'
        }
      };

      mockApiClient.post.mockResolvedValue(mockAnalysisResponse);

      await foodService.analyzeFoodImage('mock_image', 'lunch');

      expect(LocalNotifications.schedule).toHaveBeenCalled();
    });
  });

  describe('缓存功能测试', () => {
    test('缓存清除功能', async () => {
      await foodService.clearAllCache();
      // 这里应该验证缓存被清除，但由于缓存是内部实现，我们主要确保方法不抛出错误
      expect(true).toBe(true);
    });
  });
});
