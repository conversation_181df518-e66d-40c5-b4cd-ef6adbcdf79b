/**
 * 食物数据库服务模块
 * 提供食物数据库管理功能，包含分类、类型、搜索等基础数据服务
 * 支持iOS原生优化和智能缓存策略
 */

import { ApiClient } from '../core/ApiClient';
import { CacheManager, CacheKeyBuilder } from '../cache';
import GlobalCacheManager from '../cache/GlobalCacheManager';
import {
  ApiFoodCategory,
  ApiFoodType,
  ApiFoodItem
} from '../../models/api/nutrition/Food';

// 食物数据库操作结果
export interface FoodbaseOperationResult<T = any> {
  success: boolean;
  data?: T;
  error?: {
    code: string;
    message: string;
    retryable: boolean;
  };
}

/**
 * 食物数据库服务类
 */
export class FoodbaseService {
  private cacheManager: CacheManager | null = null;
  
  // 缓存配置
  private readonly CACHE_CONFIG = {
    CATEGORIES: { ttl: 24 * 60 * 60 * 1000, persistent: true }, // 24小时
    TYPES: { ttl: 24 * 60 * 60 * 1000, persistent: true }, // 24小时
    POPULAR_FOODS: { ttl: 60 * 60 * 1000, persistent: true }, // 1小时
    SEARCH_SUGGESTIONS: { ttl: 30 * 60 * 1000, persistent: true }, // 30分钟
  };

  constructor(private apiClient: ApiClient) {}

  /**
   * 获取缓存管理器实例
   */
  private async getCacheManager(): Promise<CacheManager> {
    if (!this.cacheManager) {
      this.cacheManager = await GlobalCacheManager.getInstance();
    }
    return this.cacheManager;
  }

  /**
   * 生成缓存键
   */
  private generateCacheKey(type: string, params: any = {}): string {
    return CacheKeyBuilder.create()
      .namespace('foodbase')
      .resource(type)
      .params(params)
      .build();
  }

  /**
   * 获取食物分类列表
   */
  public async getFoodCategories(
    forceRefresh: boolean = false
  ): Promise<FoodbaseOperationResult<ApiFoodCategory[]>> {
    const cacheKey = this.generateCacheKey('categories');
    
    try {
      console.log('[FoodbaseService] 获取食物分类列表:', { forceRefresh });

      // 检查缓存
      if (!forceRefresh) {
        const cacheManager = await this.getCacheManager();
        const cachedCategories = await cacheManager.get<ApiFoodCategory[]>(cacheKey);
        if (cachedCategories) {
          console.log('🎯 [FoodbaseService] 分类列表缓存命中');
          return {
            success: true,
            data: cachedCategories
          };
        }
      }

      // 从API获取
      const response = await this.apiClient.get<ApiFoodCategory[]>(
        '/api/v1/food/categories'
      );

      // 缓存结果
      const cacheManager = await this.getCacheManager();
      await cacheManager.set(cacheKey, response, this.CACHE_CONFIG.CATEGORIES);

      console.log('✅ [FoodbaseService] 食物分类列表获取成功');

      return {
        success: true,
        data: response
      };

    } catch (error) {
      console.error('❌ [FoodbaseService] 获取食物分类列表失败:', error);
      return {
        success: false,
        error: {
          code: 'FOOD_CATEGORIES_FAILED',
          message: error instanceof Error ? error.message : '获取食物分类失败',
          retryable: true
        }
      };
    }
  }

  /**
   * 获取食物类型列表
   */
  public async getFoodTypes(
    categoryId?: number,
    forceRefresh: boolean = false
  ): Promise<FoodbaseOperationResult<ApiFoodType[]>> {
    const cacheKey = this.generateCacheKey('types', { categoryId });
    
    try {
      console.log('[FoodbaseService] 获取食物类型列表:', { categoryId, forceRefresh });

      // 检查缓存
      if (!forceRefresh) {
        const cacheManager = await this.getCacheManager();
        const cachedTypes = await cacheManager.get<ApiFoodType[]>(cacheKey);
        if (cachedTypes) {
          console.log('🎯 [FoodbaseService] 类型列表缓存命中');
          return {
            success: true,
            data: cachedTypes
          };
        }
      }

      // 构建查询参数
      const queryParams: any = {};
      if (categoryId) {
        queryParams.category_id = String(categoryId);
      }

      // 从API获取
      const response = await this.apiClient.get<ApiFoodType[]>(
        '/api/v1/food/types',
        queryParams
      );

      // 缓存结果
      const cacheManager = await this.getCacheManager();
      await cacheManager.set(cacheKey, response, this.CACHE_CONFIG.TYPES);

      console.log('✅ [FoodbaseService] 食物类型列表获取成功');

      return {
        success: true,
        data: response
      };

    } catch (error) {
      console.error('❌ [FoodbaseService] 获取食物类型列表失败:', error);
      return {
        success: false,
        error: {
          code: 'FOOD_TYPES_FAILED',
          message: error instanceof Error ? error.message : '获取食物类型失败',
          retryable: true
        }
      };
    }
  }

  /**
   * 获取热门食物
   */
  public async getPopularFoods(
    category?: string,
    limit: number = 20,
    forceRefresh: boolean = false
  ): Promise<FoodbaseOperationResult<ApiFoodItem[]>> {
    const cacheKey = this.generateCacheKey('popular', { category, limit });
    
    try {
      console.log('[FoodbaseService] 获取热门食物:', { category, limit, forceRefresh });

      // 检查缓存
      if (!forceRefresh) {
        const cacheManager = await this.getCacheManager();
        const cachedPopular = await cacheManager.get<ApiFoodItem[]>(cacheKey);
        if (cachedPopular) {
          console.log('🎯 [FoodbaseService] 热门食物缓存命中');
          return {
            success: true,
            data: cachedPopular
          };
        }
      }

      // 构建查询参数
      const queryParams: any = {
        limit: String(limit),
        sort_by: 'popularity',
        sort_order: 'desc'
      };

      if (category) {
        queryParams.category = category;
      }

      // 从API获取
      const response = await this.apiClient.get<ApiFoodItem[]>(
        '/api/v1/food/popular',
        queryParams
      );

      // 缓存结果
      const cacheManager = await this.getCacheManager();
      await cacheManager.set(cacheKey, response, this.CACHE_CONFIG.POPULAR_FOODS);

      console.log('✅ [FoodbaseService] 热门食物获取成功');

      return {
        success: true,
        data: response
      };

    } catch (error) {
      console.error('❌ [FoodbaseService] 获取热门食物失败:', error);
      return {
        success: false,
        error: {
          code: 'POPULAR_FOODS_FAILED',
          message: error instanceof Error ? error.message : '获取热门食物失败',
          retryable: true
        }
      };
    }
  }

  /**
   * 获取搜索建议
   */
  public async getSearchSuggestions(
    query: string,
    limit: number = 10
  ): Promise<FoodbaseOperationResult<string[]>> {
    const cacheKey = this.generateCacheKey('suggestions', { query: query.toLowerCase(), limit });
    
    try {
      console.log('[FoodbaseService] 获取搜索建议:', { query, limit });

      // 检查缓存
      const cacheManager = await this.getCacheManager();
      const cachedSuggestions = await cacheManager.get<string[]>(cacheKey);
      if (cachedSuggestions) {
        console.log('🎯 [FoodbaseService] 搜索建议缓存命中');
        return {
          success: true,
          data: cachedSuggestions
        };
      }

      // 构建查询参数
      const queryParams = {
        q: query,
        limit: String(limit)
      };

      // 从API获取
      const response = await this.apiClient.get<string[]>(
        '/api/v1/food/suggestions',
        queryParams
      );

      // 缓存结果
      await cacheManager.set(cacheKey, response, this.CACHE_CONFIG.SEARCH_SUGGESTIONS);

      console.log('✅ [FoodbaseService] 搜索建议获取成功');

      return {
        success: true,
        data: response
      };

    } catch (error) {
      console.error('❌ [FoodbaseService] 获取搜索建议失败:', error);
      return {
        success: false,
        error: {
          code: 'SEARCH_SUGGESTIONS_FAILED',
          message: error instanceof Error ? error.message : '获取搜索建议失败',
          retryable: true
        }
      };
    }
  }

  /**
   * 根据条形码查找食物
   */
  public async getFoodByBarcode(
    barcode: string
  ): Promise<FoodbaseOperationResult<ApiFoodItem | null>> {
    try {
      console.log('[FoodbaseService] 根据条形码查找食物:', { barcode });

      const response = await this.apiClient.get<ApiFoodItem>(
        `/api/v1/food/barcode/${barcode}`
      );

      console.log('✅ [FoodbaseService] 条形码查找成功');

      return {
        success: true,
        data: response
      };

    } catch (error) {
      console.error('❌ [FoodbaseService] 条形码查找失败:', error);
      return {
        success: false,
        error: {
          code: 'BARCODE_SEARCH_FAILED',
          message: error instanceof Error ? error.message : '条形码查找失败',
          retryable: true
        }
      };
    }
  }

  /**
   * 清除所有缓存
   */
  public async clearAllCache(): Promise<void> {
    try {
      const cacheManager = await this.getCacheManager();
      await cacheManager.clear('foodbase:*');
      console.log('🗑️ [FoodbaseService] 所有缓存已清除');
    } catch (error) {
      console.warn('⚠️ [FoodbaseService] 清除缓存失败:', error);
    }
  }
}

// 导出默认实例
export const foodbaseService = new FoodbaseService(new (await import('../core/ApiClient')).ApiClient());
