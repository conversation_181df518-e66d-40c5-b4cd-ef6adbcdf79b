/**
 * 餐食服务模块
 * 提供完整的餐食管理功能，包含餐食计划、记录、模板、营养目标等
 * 支持iOS原生优化和智能缓存策略
 */

import { ApiClient } from '../core/ApiClient';
import { CacheManager, CacheKeyBuilder } from '../cache';
import GlobalCacheManager from '../cache/GlobalCacheManager';
import {
  ApiMealRecord,
  ApiNutritionGoal,
  ApiDailyNutritionSummary,
  ApiMealCreateRequest,
  ApiMealUpdateRequest,
  ApiMealCopyRequest,
  ApiMealSearchParams
} from '../../models/api/nutrition/Meal';

// 餐食操作结果
export interface MealOperationResult<T = any> {
  success: boolean;
  data?: T;
  error?: {
    code: string;
    message: string;
    retryable: boolean;
  };
}

// 餐食分页结果
export interface MealPage {
  meals: ApiMealRecord[];
  hasMore: boolean;
  total: number;
  currentPage: number;
}

// iOS反馈配置
export interface iOSMealFeedback {
  hapticStyle?: 'light' | 'medium' | 'heavy';
  statusBarStyle?: 'light' | 'dark';
  notificationTitle?: string;
  notificationBody?: string;
}

/**
 * 餐食服务类
 */
export class MealService {
  private cacheManager: CacheManager | null = null;
  private readonly pageSize = 20;
  
  // 缓存配置
  private readonly CACHE_CONFIG = {
    MEAL_RECORDS: { ttl: 15 * 60 * 1000, persistent: true }, // 15分钟
    MEAL_PLANS: { ttl: 30 * 60 * 1000, persistent: true }, // 30分钟
    MEAL_TEMPLATES: { ttl: 60 * 60 * 1000, persistent: true }, // 1小时
    NUTRITION_GOALS: { ttl: 24 * 60 * 60 * 1000, persistent: true }, // 24小时
    DAILY_SUMMARY: { ttl: 10 * 60 * 1000, persistent: true }, // 10分钟
    MEAL_STATS: { ttl: 60 * 60 * 1000, persistent: true }, // 1小时
  };

  constructor(private apiClient: ApiClient) {}

  /**
   * 获取缓存管理器实例
   */
  private async getCacheManager(): Promise<CacheManager> {
    if (!this.cacheManager) {
      this.cacheManager = await GlobalCacheManager.getInstance();
    }
    return this.cacheManager;
  }

  /**
   * 生成缓存键
   */
  private generateCacheKey(type: string, params: any = {}): string {
    return CacheKeyBuilder.create()
      .namespace('meal')
      .resource(type)
      .params(params)
      .build();
  }

  /**
   * 提供iOS原生反馈
   */
  private async provideiOSFeedback(config: iOSMealFeedback): Promise<void> {
    try {
      if (!this.apiClient.isNative || this.apiClient.platform !== 'ios') {
        return;
      }

      // 触觉反馈
      if (config.hapticStyle) {
        const { Haptics, ImpactStyle } = await import('@capacitor/haptics');
        const style = config.hapticStyle === 'light' ? ImpactStyle.Light :
                     config.hapticStyle === 'medium' ? ImpactStyle.Medium : ImpactStyle.Heavy;
        await Haptics.impact({ style });
      }

      // 状态栏样式
      if (config.statusBarStyle) {
        const { StatusBar, Style } = await import('@capacitor/status-bar');
        const style = config.statusBarStyle === 'light' ? Style.Light : Style.Dark;
        await StatusBar.setStyle({ style });
      }

      // 本地通知
      if (config.notificationTitle && config.notificationBody) {
        const { LocalNotifications } = await import('@capacitor/local-notifications');
        await LocalNotifications.schedule({
          notifications: [{
            title: config.notificationTitle,
            body: config.notificationBody,
            id: Date.now(),
            schedule: { at: new Date(Date.now() + 100) }
          }]
        });
      }

    } catch (error) {
      console.warn('⚠️ [MealService] iOS反馈失败:', error);
    }
  }

  /**
   * 创建餐食记录
   */
  public async createMealRecord(
    request: ApiMealCreateRequest
  ): Promise<MealOperationResult<ApiMealRecord>> {
    try {
      console.log('[MealService] 创建餐食记录:', request);

      const response = await this.apiClient.post<ApiMealRecord>(
        '/api/v1/meal/records',
        request
      );

      // iOS触觉反馈 - 餐食创建成功
      await this.provideiOSFeedback({
        hapticStyle: 'medium',
        notificationTitle: '餐食记录已保存',
        notificationBody: `${request.meal_type}记录已成功保存`
      });

      // 失效相关缓存
      await this.invalidateMealCache(request.meal_date);

      console.log('✅ [MealService] 餐食记录创建成功');

      return {
        success: true,
        data: response
      };

    } catch (error) {
      console.error('❌ [MealService] 创建餐食记录失败:', error);
      return {
        success: false,
        error: {
          code: 'MEAL_CREATE_FAILED',
          message: error instanceof Error ? error.message : '创建餐食记录失败',
          retryable: true
        }
      };
    }
  }

  /**
   * 获取餐食记录列表
   */
  public async getMealRecords(
    params: ApiMealSearchParams = {},
    page: number = 0,
    forceRefresh: boolean = false
  ): Promise<MealOperationResult<MealPage>> {
    const cacheKey = this.generateCacheKey('records', { params, page });
    
    try {
      console.log('[MealService] 获取餐食记录:', { params, page, forceRefresh });

      // 检查缓存
      if (!forceRefresh) {
        const cacheManager = await this.getCacheManager();
        const cachedRecords = await cacheManager.get<MealPage>(cacheKey);
        if (cachedRecords) {
          console.log('🎯 [MealService] 餐食记录缓存命中');
          return {
            success: true,
            data: cachedRecords
          };
        }
      }

      // 构建查询参数（iOS安全：确保所有参数为字符串）
      const queryParams: any = {
        skip: String(page * this.pageSize),
        limit: String(this.pageSize),
        ...params
      };

      // 确保数字参数转换为字符串
      if (params.min_calories) queryParams.min_calories = String(params.min_calories);
      if (params.max_calories) queryParams.max_calories = String(params.max_calories);
      if (params.has_image !== undefined) queryParams.has_image = String(params.has_image);
      if (params.is_template !== undefined) queryParams.is_template = String(params.is_template);

      // 发送API请求
      const response = await this.apiClient.get<ApiMealRecord[]>(
        '/api/v1/meal/records',
        queryParams
      );

      // 构建分页结果
      const result: MealPage = {
        meals: response,
        hasMore: response.length === this.pageSize,
        total: response.length,
        currentPage: page
      };

      // 缓存结果
      const cacheManager = await this.getCacheManager();
      await cacheManager.set(cacheKey, result, this.CACHE_CONFIG.MEAL_RECORDS);

      console.log('✅ [MealService] 餐食记录获取成功');

      return {
        success: true,
        data: result
      };

    } catch (error) {
      console.error('❌ [MealService] 获取餐食记录失败:', error);
      return {
        success: false,
        error: {
          code: 'MEAL_RECORDS_FAILED',
          message: error instanceof Error ? error.message : '获取餐食记录失败',
          retryable: true
        }
      };
    }
  }

  /**
   * 获取餐食记录详情
   */
  public async getMealRecord(
    mealId: number,
    forceRefresh: boolean = false
  ): Promise<MealOperationResult<ApiMealRecord>> {
    const cacheKey = this.generateCacheKey('record-detail', { mealId });
    
    try {
      console.log('[MealService] 获取餐食记录详情:', { mealId, forceRefresh });

      // 检查缓存
      if (!forceRefresh) {
        const cacheManager = await this.getCacheManager();
        const cachedRecord = await cacheManager.get<ApiMealRecord>(cacheKey);
        if (cachedRecord) {
          console.log('🎯 [MealService] 餐食记录详情缓存命中');
          return {
            success: true,
            data: cachedRecord
          };
        }
      }

      // 从API获取
      const response = await this.apiClient.get<ApiMealRecord>(
        `/api/v1/meal/records/${mealId}`
      );

      // 缓存结果
      const cacheManager = await this.getCacheManager();
      await cacheManager.set(cacheKey, response, this.CACHE_CONFIG.MEAL_RECORDS);

      console.log('✅ [MealService] 餐食记录详情获取成功');

      return {
        success: true,
        data: response
      };

    } catch (error) {
      console.error('❌ [MealService] 获取餐食记录详情失败:', error);
      return {
        success: false,
        error: {
          code: 'MEAL_RECORD_DETAIL_FAILED',
          message: error instanceof Error ? error.message : '获取餐食记录详情失败',
          retryable: true
        }
      };
    }
  }

  /**
   * 更新餐食记录
   */
  public async updateMealRecord(
    mealId: number,
    request: ApiMealUpdateRequest
  ): Promise<MealOperationResult<ApiMealRecord>> {
    try {
      console.log('[MealService] 更新餐食记录:', { mealId, request });

      const response = await this.apiClient.put<ApiMealRecord>(
        `/api/v1/meal/records/${mealId}`,
        request
      );

      // iOS触觉反馈
      await this.provideiOSFeedback({
        hapticStyle: 'light',
        notificationTitle: '餐食记录已更新',
        notificationBody: '餐食信息已成功更新'
      });

      // 失效相关缓存
      await this.invalidateMealRecordCache(mealId);

      console.log('✅ [MealService] 餐食记录更新成功');

      return {
        success: true,
        data: response
      };

    } catch (error) {
      console.error('❌ [MealService] 更新餐食记录失败:', error);
      return {
        success: false,
        error: {
          code: 'MEAL_UPDATE_FAILED',
          message: error instanceof Error ? error.message : '更新餐食记录失败',
          retryable: true
        }
      };
    }
  }

  /**
   * 获取每日营养摘要
   */
  public async getDailyNutritionSummary(
    date: string,
    forceRefresh: boolean = false
  ): Promise<MealOperationResult<ApiDailyNutritionSummary>> {
    const cacheKey = this.generateCacheKey('daily-summary', { date });

    try {
      console.log('[MealService] 获取每日营养摘要:', { date, forceRefresh });

      // 检查缓存
      if (!forceRefresh) {
        const cacheManager = await this.getCacheManager();
        const cachedSummary = await cacheManager.get<ApiDailyNutritionSummary>(cacheKey);
        if (cachedSummary) {
          console.log('🎯 [MealService] 每日营养摘要缓存命中');
          return {
            success: true,
            data: cachedSummary
          };
        }
      }

      // 从API获取
      const response = await this.apiClient.get<ApiDailyNutritionSummary>(
        `/api/v1/meal/daily-summary/${date}`
      );

      // 缓存结果
      const cacheManager = await this.getCacheManager();
      await cacheManager.set(cacheKey, response, this.CACHE_CONFIG.DAILY_SUMMARY);

      console.log('✅ [MealService] 每日营养摘要获取成功');

      return {
        success: true,
        data: response
      };

    } catch (error) {
      console.error('❌ [MealService] 获取每日营养摘要失败:', error);
      return {
        success: false,
        error: {
          code: 'DAILY_SUMMARY_FAILED',
          message: error instanceof Error ? error.message : '获取每日营养摘要失败',
          retryable: true
        }
      };
    }
  }

  /**
   * 删除餐食记录
   */
  public async deleteMealRecord(
    mealId: number
  ): Promise<MealOperationResult<void>> {
    try {
      console.log('[MealService] 删除餐食记录:', { mealId });

      await this.apiClient.delete(`/api/v1/meals/${mealId}`);

      // iOS触觉反馈
      await this.provideiOSFeedback({
        hapticStyle: 'light',
        notificationTitle: '餐食已删除',
        notificationBody: '餐食记录已成功删除'
      });

      // 失效相关缓存
      await this.invalidateMealRecordCache(mealId);

      console.log('✅ [MealService] 餐食记录删除成功');

      return {
        success: true
      };

    } catch (error) {
      console.error('❌ [MealService] 删除餐食记录失败:', error);
      return {
        success: false,
        error: {
          code: 'DELETE_MEAL_RECORD_FAILED',
          message: error instanceof Error ? error.message : '删除餐食记录失败',
          retryable: true
        }
      };
    }
  }

  /**
   * 复制餐食记录
   */
  public async copyMealRecord(
    request: ApiMealCopyRequest
  ): Promise<MealOperationResult<ApiMealRecord>> {
    try {
      console.log('[MealService] 复制餐食记录:', request);

      const response = await this.apiClient.post<ApiMealRecord>(
        `/api/v1/meals/${request.source_meal_id}/copy`,
        {
          target_date: request.target_date,
          target_meal_type: request.target_meal_type
        }
      );

      // iOS触觉反馈
      await this.provideiOSFeedback({
        hapticStyle: 'medium',
        notificationTitle: '餐食已复制',
        notificationBody: '餐食记录复制成功'
      });

      // 失效相关缓存
      await this.invalidateMealCache(request.target_date);

      console.log('✅ [MealService] 餐食记录复制成功');

      return {
        success: true,
        data: response
      };

    } catch (error) {
      console.error('❌ [MealService] 复制餐食记录失败:', error);
      return {
        success: false,
        error: {
          code: 'COPY_MEAL_RECORD_FAILED',
          message: error instanceof Error ? error.message : '复制餐食记录失败',
          retryable: true
        }
      };
    }
  }

  /**
   * 获取营养目标
   */
  public async getNutritionGoals(): Promise<MealOperationResult<ApiNutritionGoal[]>> {
    try {
      console.log('[MealService] 获取营养目标');

      const response = await this.apiClient.get<ApiNutritionGoal[]>('/api/v1/nutrition/goals');

      console.log('✅ [MealService] 营养目标获取成功');

      return {
        success: true,
        data: response
      };

    } catch (error) {
      console.error('❌ [MealService] 获取营养目标失败:', error);
      return {
        success: false,
        error: {
          code: 'GET_NUTRITION_GOALS_FAILED',
          message: error instanceof Error ? error.message : '获取营养目标失败',
          retryable: true
        }
      };
    }
  }

  /**
   * 设置营养目标
   */
  public async setNutritionGoal(
    goal: Partial<ApiNutritionGoal>
  ): Promise<MealOperationResult<ApiNutritionGoal>> {
    try {
      console.log('[MealService] 设置营养目标:', goal);

      const response = await this.apiClient.post<ApiNutritionGoal>(
        '/api/v1/nutrition/goals',
        goal
      );

      // iOS触觉反馈
      await this.provideiOSFeedback({
        hapticStyle: 'medium',
        notificationTitle: '营养目标已设置',
        notificationBody: '营养目标设置成功'
      });

      console.log('✅ [MealService] 营养目标设置成功');

      return {
        success: true,
        data: response
      };

    } catch (error) {
      console.error('❌ [MealService] 设置营养目标失败:', error);
      return {
        success: false,
        error: {
          code: 'SET_NUTRITION_GOAL_FAILED',
          message: error instanceof Error ? error.message : '设置营养目标失败',
          retryable: true
        }
      };
    }
  }

  /**
   * 失效餐食相关缓存
   */
  private async invalidateMealCache(date?: string): Promise<void> {
    try {
      const cacheManager = await this.getCacheManager();

      // 失效餐食记录缓存
      await cacheManager.invalidate(['meal:records']);

      // 失效特定日期的每日摘要缓存
      if (date) {
        const summaryKey = this.generateCacheKey('daily-summary', { date });
        await cacheManager.delete(summaryKey);
      }

      console.log('🗑️ [MealService] 餐食缓存失效完成');
    } catch (error) {
      console.warn('⚠️ [MealService] 缓存失效失败:', error);
    }
  }

  /**
   * 失效特定餐食记录缓存
   */
  private async invalidateMealRecordCache(mealId: number): Promise<void> {
    try {
      const cacheManager = await this.getCacheManager();

      // 失效餐食记录详情缓存
      const detailKey = this.generateCacheKey('record-detail', { mealId });
      await cacheManager.delete(detailKey);

      // 失效餐食记录列表缓存
      await cacheManager.invalidate(['meal:records']);

      console.log('🗑️ [MealService] 餐食记录缓存失效完成');
    } catch (error) {
      console.warn('⚠️ [MealService] 缓存失效失败:', error);
    }
  }

  /**
   * 清除所有餐食缓存
   */
  public async clearAllCache(): Promise<void> {
    try {
      const cacheManager = await this.getCacheManager();
      await cacheManager.clear('meal:*');
      console.log('🗑️ [MealService] 所有餐食缓存已清除');
    } catch (error) {
      console.warn('⚠️ [MealService] 清除缓存失败:', error);
    }
  }
}

// 导出默认实例
export const mealService = new MealService(new (await import('../core/ApiClient')).ApiClient());
