/**
 * 营养服务模块统一导出
 */

// 食物服务
export * from './FoodService';
export { foodService } from './FoodService';

// 食物数据库服务
export * from './FoodbaseService';
export { foodbaseService } from './FoodbaseService';

// 餐食服务
export * from './MealService';
export { mealService } from './MealService';

// 营养分析服务
export * from './NutrientService';
export { nutrientService } from './NutrientService';

// 营养模块配置
export interface NutritionServiceConfig {
  enableCache: boolean;
  cacheTimeout: number;
  enableiOSFeedback: boolean;
  enableAnalytics: boolean;
}

// 默认配置
export const DEFAULT_NUTRITION_CONFIG: NutritionServiceConfig = {
  enableCache: true,
  cacheTimeout: 30 * 60 * 1000, // 30分钟
  enableiOSFeedback: true,
  enableAnalytics: true
};

// 营养服务管理器
export class NutritionServiceManager {
  private static instance: NutritionServiceManager;
  private config: NutritionServiceConfig;

  private constructor(config: NutritionServiceConfig = DEFAULT_NUTRITION_CONFIG) {
    this.config = config;
  }

  public static getInstance(config?: NutritionServiceConfig): NutritionServiceManager {
    if (!NutritionServiceManager.instance) {
      NutritionServiceManager.instance = new NutritionServiceManager(config);
    }
    return NutritionServiceManager.instance;
  }

  public getConfig(): NutritionServiceConfig {
    return this.config;
  }

  public updateConfig(newConfig: Partial<NutritionServiceConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }

  /**
   * 清除所有营养服务缓存
   */
  public async clearAllCaches(): Promise<void> {
    try {
      const { foodService } = await import('./FoodService');
      const { foodbaseService } = await import('./FoodbaseService');
      const { mealService } = await import('./MealService');
      const { nutrientService } = await import('./NutrientService');

      await Promise.all([
        foodService.clearAllCache(),
        foodbaseService.clearAllCache(),
        mealService.clearAllCache(),
        nutrientService.clearAllCache()
      ]);
      console.log('🗑️ [NutritionServiceManager] 所有营养服务缓存已清除');
    } catch (error) {
      console.warn('⚠️ [NutritionServiceManager] 清除缓存失败:', error);
    }
  }

  /**
   * 获取营养服务状态
   */
  public getNutritionServiceStatus(): {
    servicesLoaded: boolean;
    cacheEnabled: boolean;
    iOSOptimized: boolean;
  } {
    return {
      servicesLoaded: true,
      cacheEnabled: this.config.enableCache,
      iOSOptimized: this.config.enableiOSFeedback
    };
  }
}

// 导出默认管理器实例
export const nutritionServiceManager = NutritionServiceManager.getInstance();
