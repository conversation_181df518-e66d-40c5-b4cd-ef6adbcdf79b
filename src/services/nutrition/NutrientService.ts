/**
 * 营养分析服务模块
 * 提供完整的营养素分析功能，包含营养计算、趋势分析、报告生成等
 * 支持iOS原生优化和智能缓存策略
 */

import { ApiClient } from '../core/ApiClient';
import { CacheManager, CacheKeyBuilder } from '../cache';
import GlobalCacheManager from '../cache/GlobalCacheManager';
import {
  ApiNutrientInfo,
  ApiNutritionAnalysis,
  ApiNutritionScore,
  ApiNutritionTrend,
  ApiNutrientDeficiency,
  ApiNutritionReport,
  ApiNutritionReportRequest,
  ApiNutritionExportRequest,
  ApiNutritionAIAnalysisRequest,
  ApiNutritionAIAnalysisResponse,
  NutrientType
} from '../../models/api/nutrition/Nutrient';

// 营养分析操作结果
export interface NutrientOperationResult<T = any> {
  success: boolean;
  data?: T;
  error?: {
    code: string;
    message: string;
    retryable: boolean;
  };
}

// 营养分析参数
export interface NutritionAnalysisParams {
  startDate: string;
  endDate: string;
  periodType: 'day' | 'week' | 'month';
  includeDeficiencies?: boolean;
  includeRecommendations?: boolean;
  includeTrends?: boolean;
}

// iOS反馈配置
export interface iOSNutrientFeedback {
  hapticStyle?: 'light' | 'medium' | 'heavy';
  statusBarStyle?: 'light' | 'dark';
  notificationTitle?: string;
  notificationBody?: string;
}

/**
 * 营养分析服务类
 */
export class NutrientService {
  private cacheManager: CacheManager | null = null;
  
  // 缓存配置
  private readonly CACHE_CONFIG = {
    NUTRIENT_INFO: { ttl: 24 * 60 * 60 * 1000, persistent: true }, // 24小时
    NUTRITION_ANALYSIS: { ttl: 30 * 60 * 1000, persistent: true }, // 30分钟
    NUTRITION_SCORE: { ttl: 60 * 60 * 1000, persistent: true }, // 1小时
    NUTRITION_TRENDS: { ttl: 2 * 60 * 60 * 1000, persistent: true }, // 2小时
    NUTRITION_REPORTS: { ttl: 24 * 60 * 60 * 1000, persistent: true }, // 24小时
    AI_ANALYSIS: { ttl: 60 * 60 * 1000, persistent: true }, // 1小时
  };

  constructor(private apiClient: ApiClient) {}

  /**
   * 获取缓存管理器实例
   */
  private async getCacheManager(): Promise<CacheManager> {
    if (!this.cacheManager) {
      this.cacheManager = await GlobalCacheManager.getInstance();
    }
    return this.cacheManager;
  }

  /**
   * 生成缓存键
   */
  private generateCacheKey(type: string, params: any = {}): string {
    return CacheKeyBuilder.create()
      .namespace('nutrient')
      .resource(type)
      .params(params)
      .build();
  }

  /**
   * 提供iOS原生反馈
   */
  private async provideiOSFeedback(config: iOSNutrientFeedback): Promise<void> {
    try {
      if (!this.apiClient.isNative || this.apiClient.platform !== 'ios') {
        return;
      }

      // 触觉反馈
      if (config.hapticStyle) {
        const { Haptics, ImpactStyle } = await import('@capacitor/haptics');
        const style = config.hapticStyle === 'light' ? ImpactStyle.Light :
                     config.hapticStyle === 'medium' ? ImpactStyle.Medium : ImpactStyle.Heavy;
        await Haptics.impact({ style });
      }

      // 状态栏样式
      if (config.statusBarStyle) {
        const { StatusBar, Style } = await import('@capacitor/status-bar');
        const style = config.statusBarStyle === 'light' ? Style.Light : Style.Dark;
        await StatusBar.setStyle({ style });
      }

      // 本地通知
      if (config.notificationTitle && config.notificationBody) {
        const { LocalNotifications } = await import('@capacitor/local-notifications');
        await LocalNotifications.schedule({
          notifications: [{
            title: config.notificationTitle,
            body: config.notificationBody,
            id: Date.now(),
            schedule: { at: new Date(Date.now() + 100) }
          }]
        });
      }

    } catch (error) {
      console.warn('⚠️ [NutrientService] iOS反馈失败:', error);
    }
  }

  /**
   * 获取营养素信息
   */
  public async getNutrientInfo(
    nutrientType?: NutrientType,
    forceRefresh: boolean = false
  ): Promise<NutrientOperationResult<ApiNutrientInfo[]>> {
    const cacheKey = this.generateCacheKey('info', { nutrientType });
    
    try {
      console.log('[NutrientService] 获取营养素信息:', { nutrientType, forceRefresh });

      // 检查缓存
      if (!forceRefresh) {
        const cacheManager = await this.getCacheManager();
        const cachedInfo = await cacheManager.get<ApiNutrientInfo[]>(cacheKey);
        if (cachedInfo) {
          console.log('🎯 [NutrientService] 营养素信息缓存命中');
          return {
            success: true,
            data: cachedInfo
          };
        }
      }

      // 构建查询参数
      const queryParams: any = {};
      if (nutrientType) {
        queryParams.type = nutrientType;
      }

      // 从API获取
      const response = await this.apiClient.get<ApiNutrientInfo[]>(
        '/api/v1/nutrition/nutrients/info',
        queryParams
      );

      // 缓存结果
      const cacheManager = await this.getCacheManager();
      await cacheManager.set(cacheKey, response, this.CACHE_CONFIG.NUTRIENT_INFO);

      console.log('✅ [NutrientService] 营养素信息获取成功');

      return {
        success: true,
        data: response
      };

    } catch (error) {
      console.error('❌ [NutrientService] 获取营养素信息失败:', error);
      return {
        success: false,
        error: {
          code: 'NUTRIENT_INFO_FAILED',
          message: error instanceof Error ? error.message : '获取营养素信息失败',
          retryable: true
        }
      };
    }
  }

  /**
   * 生成营养分析报告
   */
  public async generateNutritionAnalysis(
    params: NutritionAnalysisParams,
    forceRefresh: boolean = false
  ): Promise<NutrientOperationResult<ApiNutritionAnalysis>> {
    const cacheKey = this.generateCacheKey('analysis', params);
    
    try {
      console.log('[NutrientService] 生成营养分析报告:', { params, forceRefresh });

      // 检查缓存
      if (!forceRefresh) {
        const cacheManager = await this.getCacheManager();
        const cachedAnalysis = await cacheManager.get<ApiNutritionAnalysis>(cacheKey);
        if (cachedAnalysis) {
          console.log('🎯 [NutrientService] 营养分析缓存命中');
          return {
            success: true,
            data: cachedAnalysis
          };
        }
      }

      // 构建请求参数（iOS安全：确保所有参数为字符串）
      const requestParams = {
        start_date: params.startDate,
        end_date: params.endDate,
        period_type: params.periodType,
        include_deficiencies: String(params.includeDeficiencies || false),
        include_recommendations: String(params.includeRecommendations || false),
        include_trends: String(params.includeTrends || false)
      };

      // 发送API请求
      const response = await this.apiClient.post<ApiNutritionAnalysis>(
        '/api/v1/nutrition/analysis',
        requestParams
      );

      // iOS触觉反馈 - 分析完成
      await this.provideiOSFeedback({
        hapticStyle: 'medium',
        notificationTitle: '营养分析完成',
        notificationBody: `营养充足性评分：${response.nutrient_adequacy_score}分`
      });

      // 缓存结果
      const cacheManager = await this.getCacheManager();
      await cacheManager.set(cacheKey, response, this.CACHE_CONFIG.NUTRITION_ANALYSIS);

      console.log('✅ [NutrientService] 营养分析报告生成成功');

      return {
        success: true,
        data: response
      };

    } catch (error) {
      console.error('❌ [NutrientService] 生成营养分析报告失败:', error);
      return {
        success: false,
        error: {
          code: 'NUTRITION_ANALYSIS_FAILED',
          message: error instanceof Error ? error.message : '生成营养分析报告失败',
          retryable: true
        }
      };
    }
  }

  /**
   * 获取营养评分
   */
  public async getNutritionScore(
    startDate: string,
    endDate: string,
    forceRefresh: boolean = false
  ): Promise<NutrientOperationResult<ApiNutritionScore>> {
    const cacheKey = this.generateCacheKey('score', { startDate, endDate });
    
    try {
      console.log('[NutrientService] 获取营养评分:', { startDate, endDate, forceRefresh });

      // 检查缓存
      if (!forceRefresh) {
        const cacheManager = await this.getCacheManager();
        const cachedScore = await cacheManager.get<ApiNutritionScore>(cacheKey);
        if (cachedScore) {
          console.log('🎯 [NutrientService] 营养评分缓存命中');
          return {
            success: true,
            data: cachedScore
          };
        }
      }

      // 构建查询参数
      const queryParams = {
        start_date: startDate,
        end_date: endDate
      };

      // 从API获取
      const response = await this.apiClient.get<ApiNutritionScore>(
        '/api/v1/nutrition/score',
        queryParams
      );

      // 缓存结果
      const cacheManager = await this.getCacheManager();
      await cacheManager.set(cacheKey, response, this.CACHE_CONFIG.NUTRITION_SCORE);

      console.log('✅ [NutrientService] 营养评分获取成功');

      return {
        success: true,
        data: response
      };

    } catch (error) {
      console.error('❌ [NutrientService] 获取营养评分失败:', error);
      return {
        success: false,
        error: {
          code: 'NUTRITION_SCORE_FAILED',
          message: error instanceof Error ? error.message : '获取营养评分失败',
          retryable: true
        }
      };
    }
  }

  /**
   * 获取营养趋势数据
   */
  public async getNutritionTrends(
    nutrientType: NutrientType,
    period: 'week' | 'month' | 'quarter' | 'year',
    forceRefresh: boolean = false
  ): Promise<NutrientOperationResult<ApiNutritionTrend>> {
    const cacheKey = this.generateCacheKey('trends', { nutrientType, period });

    try {
      console.log('[NutrientService] 获取营养趋势数据:', { nutrientType, period, forceRefresh });

      // 检查缓存
      if (!forceRefresh) {
        const cacheManager = await this.getCacheManager();
        const cachedTrends = await cacheManager.get<ApiNutritionTrend>(cacheKey);
        if (cachedTrends) {
          console.log('🎯 [NutrientService] 营养趋势缓存命中');
          return {
            success: true,
            data: cachedTrends
          };
        }
      }

      // 构建查询参数
      const queryParams = {
        nutrient_type: nutrientType,
        period: period
      };

      // 从API获取
      const response = await this.apiClient.get<ApiNutritionTrend>(
        '/api/v1/nutrition/trends',
        queryParams
      );

      // 缓存结果
      const cacheManager = await this.getCacheManager();
      await cacheManager.set(cacheKey, response, this.CACHE_CONFIG.NUTRITION_TRENDS);

      console.log('✅ [NutrientService] 营养趋势数据获取成功');

      return {
        success: true,
        data: response
      };

    } catch (error) {
      console.error('❌ [NutrientService] 获取营养趋势数据失败:', error);
      return {
        success: false,
        error: {
          code: 'NUTRITION_TRENDS_FAILED',
          message: error instanceof Error ? error.message : '获取营养趋势数据失败',
          retryable: true
        }
      };
    }
  }

  /**
   * 获取营养缺陷分析
   */
  public async getNutrientDeficiencies(
    startDate: string,
    endDate: string
  ): Promise<NutrientOperationResult<ApiNutrientDeficiency[]>> {
    try {
      console.log('[NutrientService] 获取营养缺陷分析:', { startDate, endDate });

      // 构建查询参数
      const queryParams = {
        start_date: startDate,
        end_date: endDate
      };

      // 从API获取
      const response = await this.apiClient.get<ApiNutrientDeficiency[]>(
        '/api/v1/nutrition/deficiencies',
        queryParams
      );

      console.log('✅ [NutrientService] 营养缺陷分析获取成功');

      return {
        success: true,
        data: response
      };

    } catch (error) {
      console.error('❌ [NutrientService] 获取营养缺陷分析失败:', error);
      return {
        success: false,
        error: {
          code: 'NUTRIENT_DEFICIENCIES_FAILED',
          message: error instanceof Error ? error.message : '获取营养缺陷分析失败',
          retryable: true
        }
      };
    }
  }

  /**
   * 生成营养报告
   */
  public async generateNutritionReport(
    request: ApiNutritionReportRequest
  ): Promise<NutrientOperationResult<ApiNutritionReport>> {
    const cacheKey = this.generateCacheKey('report', request);

    try {
      console.log('[NutrientService] 生成营养报告:', request);

      // 检查缓存
      const cacheManager = await this.getCacheManager();
      const cachedReport = await cacheManager.get<ApiNutritionReport>(cacheKey);
      if (cachedReport) {
        console.log('🎯 [NutrientService] 营养报告缓存命中');
        return {
          success: true,
          data: cachedReport
        };
      }

      // 构建请求参数（iOS安全：确保所有参数为字符串）
      const requestParams = {
        ...request,
        include_trends: String(request.include_trends || false),
        include_recommendations: String(request.include_recommendations || false),
        include_comparisons: String(request.include_comparisons || false)
      };

      // 发送API请求
      const response = await this.apiClient.post<ApiNutritionReport>(
        '/api/v1/nutrition/reports',
        requestParams
      );

      // iOS触觉反馈 - 报告生成完成
      await this.provideiOSFeedback({
        hapticStyle: 'heavy',
        notificationTitle: '营养报告已生成',
        notificationBody: `${request.report_type}报告已准备就绪`
      });

      // 缓存结果
      await cacheManager.set(cacheKey, response, this.CACHE_CONFIG.NUTRITION_REPORTS);

      console.log('✅ [NutrientService] 营养报告生成成功');

      return {
        success: true,
        data: response
      };

    } catch (error) {
      console.error('❌ [NutrientService] 生成营养报告失败:', error);
      return {
        success: false,
        error: {
          code: 'NUTRITION_REPORT_FAILED',
          message: error instanceof Error ? error.message : '生成营养报告失败',
          retryable: true
        }
      };
    }
  }

  /**
   * AI营养分析
   */
  public async performAIAnalysis(
    request: ApiNutritionAIAnalysisRequest
  ): Promise<NutrientOperationResult<ApiNutritionAIAnalysisResponse>> {
    const cacheKey = this.generateCacheKey('ai-analysis', request);

    try {
      console.log('[NutrientService] 执行AI营养分析:', request);

      // 检查缓存
      const cacheManager = await this.getCacheManager();
      const cachedAnalysis = await cacheManager.get<ApiNutritionAIAnalysisResponse>(cacheKey);
      if (cachedAnalysis) {
        console.log('🎯 [NutrientService] AI分析缓存命中');
        return {
          success: true,
          data: cachedAnalysis
        };
      }

      // 发送API请求
      const response = await this.apiClient.post<ApiNutritionAIAnalysisResponse>(
        '/api/v1/nutrition/ai-analysis',
        request
      );

      // iOS触觉反馈 - AI分析完成
      await this.provideiOSFeedback({
        hapticStyle: 'heavy',
        notificationTitle: 'AI营养分析完成',
        notificationBody: `置信度：${Math.round(response.confidence_score * 100)}%`
      });

      // 缓存结果
      await cacheManager.set(cacheKey, response, this.CACHE_CONFIG.AI_ANALYSIS);

      console.log('✅ [NutrientService] AI营养分析完成');

      return {
        success: true,
        data: response
      };

    } catch (error) {
      console.error('❌ [NutrientService] AI营养分析失败:', error);
      return {
        success: false,
        error: {
          code: 'AI_ANALYSIS_FAILED',
          message: error instanceof Error ? error.message : 'AI营养分析失败',
          retryable: true
        }
      };
    }
  }

  /**
   * 导出营养数据
   */
  public async exportNutritionData(
    request: ApiNutritionExportRequest
  ): Promise<NutrientOperationResult<string>> {
    try {
      console.log('[NutrientService] 导出营养数据:', request);

      // 构建请求参数（iOS安全：确保所有参数为字符串）
      const requestParams = {
        ...request,
        include_meals: String(request.include_meals || false),
        include_analysis: String(request.include_analysis || false),
        include_trends: String(request.include_trends || false)
      };

      // 发送API请求
      const response = await this.apiClient.post<{ download_url: string }>(
        '/api/v1/nutrition/export',
        requestParams
      );

      // iOS触觉反馈
      await this.provideiOSFeedback({
        hapticStyle: 'medium',
        notificationTitle: '数据导出完成',
        notificationBody: '营养数据已准备下载'
      });

      console.log('✅ [NutrientService] 营养数据导出成功');

      return {
        success: true,
        data: response.download_url
      };

    } catch (error) {
      console.error('❌ [NutrientService] 导出营养数据失败:', error);
      return {
        success: false,
        error: {
          code: 'NUTRITION_EXPORT_FAILED',
          message: error instanceof Error ? error.message : '导出营养数据失败',
          retryable: true
        }
      };
    }
  }

  /**
   * 清除所有营养分析缓存
   */
  public async clearAllCache(): Promise<void> {
    try {
      const cacheManager = await this.getCacheManager();
      await cacheManager.clear('nutrient:*');
      console.log('🗑️ [NutrientService] 所有营养分析缓存已清除');
    } catch (error) {
      console.warn('⚠️ [NutrientService] 清除缓存失败:', error);
    }
  }
}

// 导出默认实例
export const nutrientService = new NutrientService(new (await import('../core/ApiClient')).ApiClient());
