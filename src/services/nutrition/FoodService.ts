/**
 * 食物服务模块
 * 提供完整的食物数据管理，包含搜索、收藏、评分、图片识别等功能
 * 支持iOS原生优化和智能缓存策略
 */

import { ApiClient } from '../core/ApiClient';
import { CacheManager, CacheKeyBuilder } from '../cache';
import GlobalCacheManager from '../cache/GlobalCacheManager';
import {
  ApiFoodDetail,
  ApiFoodSearchResult,
  ApiFoodSearchParams,
  ApiFoodAnalysisRequest,
  ApiFoodAnalysisResponse,
  ApiFoodFavorite,
  ApiFoodRatingRequest,
  ApiFoodRatingResponse,
  ApiBatchFoodRequest,
  ApiBatchFoodResponse
} from '../../models/api/nutrition/Food';

// 食物搜索筛选条件
export interface FoodFilters {
  name?: string;
  category?: string;
  food_type?: string;
  brand?: string;
  min_protein?: number;
  max_calories?: number;
  favoritesOnly?: boolean;
}

// 分页数据接口
export interface FoodPage {
  foods: ApiFoodDetail[];
  hasMore: boolean;
  total: number;
  currentPage: number;
}

// 食物操作结果
export interface FoodOperationResult<T = any> {
  success: boolean;
  data?: T;
  error?: {
    code: string;
    message: string;
    retryable: boolean;
  };
}

// iOS反馈配置
export interface iOSFoodFeedback {
  hapticStyle?: 'light' | 'medium' | 'heavy';
  statusBarStyle?: 'light' | 'dark';
  notificationTitle?: string;
  notificationBody?: string;
}

/**
 * 食物服务类
 */
export class FoodService {
  private cacheManager: CacheManager | null = null;
  private readonly pageSize = 20;
  
  // 缓存配置
  private readonly CACHE_CONFIG = {
    FOOD_LIST: { ttl: 10 * 60 * 1000, persistent: true }, // 10分钟
    FOOD_DETAIL: { ttl: 30 * 60 * 1000, persistent: true }, // 30分钟
    FOOD_SEARCH: { ttl: 5 * 60 * 1000, persistent: false }, // 5分钟
    FOOD_FAVORITES: { ttl: 15 * 60 * 1000, persistent: true }, // 15分钟
    FOOD_CATEGORIES: { ttl: 60 * 60 * 1000, persistent: true }, // 1小时
  };

  constructor(private apiClient: ApiClient) {}

  /**
   * 获取缓存管理器实例
   */
  private async getCacheManager(): Promise<CacheManager> {
    if (!this.cacheManager) {
      this.cacheManager = await GlobalCacheManager.getInstance();
    }
    return this.cacheManager;
  }

  /**
   * 生成缓存键
   */
  private generateCacheKey(type: string, params: any = {}): string {
    return CacheKeyBuilder.create()
      .namespace('food')
      .resource(type)
      .params(params)
      .build();
  }

  /**
   * 提供iOS原生反馈
   */
  private async provideiOSFeedback(config: iOSFoodFeedback): Promise<void> {
    try {
      if (!this.apiClient.isNative || this.apiClient.platform !== 'ios') {
        return;
      }

      // 触觉反馈
      if (config.hapticStyle) {
        const { Haptics, ImpactStyle } = await import('@capacitor/haptics');
        const style = config.hapticStyle === 'light' ? ImpactStyle.Light :
                     config.hapticStyle === 'medium' ? ImpactStyle.Medium : ImpactStyle.Heavy;
        await Haptics.impact({ style });
      }

      // 状态栏样式
      if (config.statusBarStyle) {
        const { StatusBar, Style } = await import('@capacitor/status-bar');
        const style = config.statusBarStyle === 'light' ? Style.Light : Style.Dark;
        await StatusBar.setStyle({ style });
      }

      // 本地通知
      if (config.notificationTitle && config.notificationBody) {
        const { LocalNotifications } = await import('@capacitor/local-notifications');
        await LocalNotifications.schedule({
          notifications: [{
            title: config.notificationTitle,
            body: config.notificationBody,
            id: Date.now(),
            schedule: { at: new Date(Date.now() + 100) }
          }]
        });
      }

    } catch (error) {
      console.warn('⚠️ [FoodService] iOS反馈失败:', error);
    }
  }

  /**
   * 搜索食物
   */
  public async searchFoods(
    filters: FoodFilters = {},
    page: number = 0,
    forceRefresh: boolean = false
  ): Promise<FoodOperationResult<FoodPage>> {
    const cacheKey = this.generateCacheKey('search', { filters, page });
    
    try {
      console.log('[FoodService] 搜索食物:', { filters, page, forceRefresh });

      // 检查缓存
      if (!forceRefresh) {
        const cacheManager = await this.getCacheManager();
        const cachedResult = await cacheManager.get<FoodPage>(cacheKey);
        if (cachedResult) {
          console.log('🎯 [FoodService] 搜索缓存命中');
          return {
            success: true,
            data: cachedResult
          };
        }
      }

      // 构建查询参数（iOS安全：确保所有参数为字符串）
      const queryParams: ApiFoodSearchParams = {
        skip: page * this.pageSize,
        limit: this.pageSize,
        sort_by: 'name',
        sort_order: 'asc'
      };

      if (filters.name) queryParams.name = filters.name;
      if (filters.category) queryParams.category = filters.category;
      if (filters.food_type) queryParams.food_type = filters.food_type;
      if (filters.brand) queryParams.brand = filters.brand;
      if (filters.min_protein) queryParams.min_protein = Number(filters.min_protein);
      if (filters.max_calories) queryParams.max_calories = Number(filters.max_calories);

      // 发送API请求
      const response = await this.apiClient.get<ApiFoodSearchResult>(
        '/api/v1/food/',
        queryParams
      );

      // 构建分页结果
      const result: FoodPage = {
        foods: response.foods.map(food => ({
          ...food,
          nutrition: {
            food_id: food.id,
            serving_size: 100,
            serving_unit: 'g',
            calories: 0,
            protein: 0,
            carbohydrates: 0,
            fat: 0
          }
        })) as ApiFoodDetail[],
        hasMore: response.has_more,
        total: response.total,
        currentPage: page
      };

      // 缓存结果
      const cacheManager = await this.getCacheManager();
      await cacheManager.set(cacheKey, result, this.CACHE_CONFIG.FOOD_SEARCH);

      console.log('✅ [FoodService] 食物搜索成功');

      return {
        success: true,
        data: result
      };

    } catch (error) {
      console.error('❌ [FoodService] 食物搜索失败:', error);
      return {
        success: false,
        error: {
          code: 'FOOD_SEARCH_FAILED',
          message: error instanceof Error ? error.message : '食物搜索失败',
          retryable: true
        }
      };
    }
  }

  /**
   * 获取食物详情
   */
  public async getFoodDetail(
    foodId: number,
    forceRefresh: boolean = false
  ): Promise<FoodOperationResult<ApiFoodDetail>> {
    const cacheKey = this.generateCacheKey('detail', { foodId });
    
    try {
      console.log('[FoodService] 获取食物详情:', { foodId, forceRefresh });

      // 检查缓存
      if (!forceRefresh) {
        const cacheManager = await this.getCacheManager();
        const cachedDetail = await cacheManager.get<ApiFoodDetail>(cacheKey);
        if (cachedDetail) {
          console.log('🎯 [FoodService] 食物详情缓存命中');
          return {
            success: true,
            data: cachedDetail
          };
        }
      }

      // 从API获取
      const response = await this.apiClient.get<ApiFoodDetail>(
        `/api/v1/food/${foodId}`
      );

      // 缓存结果
      const cacheManager = await this.getCacheManager();
      await cacheManager.set(cacheKey, response, this.CACHE_CONFIG.FOOD_DETAIL);

      console.log('✅ [FoodService] 食物详情获取成功');

      return {
        success: true,
        data: response
      };

    } catch (error) {
      console.error('❌ [FoodService] 获取食物详情失败:', error);
      return {
        success: false,
        error: {
          code: 'FOOD_DETAIL_FAILED',
          message: error instanceof Error ? error.message : '获取食物详情失败',
          retryable: true
        }
      };
    }
  }

  /**
   * 分析食物图片
   */
  public async analyzeFoodImage(
    imageData: string,
    mealType: 'breakfast' | 'lunch' | 'dinner' | 'snack',
    mealDate?: string
  ): Promise<FoodOperationResult<ApiFoodAnalysisResponse>> {
    try {
      console.log('[FoodService] 分析食物图片:', { mealType, mealDate });

      const request: ApiFoodAnalysisRequest = {
        image: imageData,
        meal_type: mealType,
        optimize: true
      };

      if (mealDate) {
        request.meal_date = mealDate;
      }

      const response = await this.apiClient.post<ApiFoodAnalysisResponse>(
        '/api/v1/food_recognition/analyze',
        request
      );

      // iOS触觉反馈 - 分析完成
      await this.provideiOSFeedback({
        hapticStyle: 'medium',
        notificationTitle: '食物识别完成',
        notificationBody: `识别到${response.data.recognized_foods.length}种食物`
      });

      console.log('✅ [FoodService] 食物图片分析成功');

      return {
        success: true,
        data: response
      };

    } catch (error) {
      console.error('❌ [FoodService] 食物图片分析失败:', error);
      return {
        success: false,
        error: {
          code: 'FOOD_ANALYSIS_FAILED',
          message: error instanceof Error ? error.message : '食物图片分析失败',
          retryable: true
        }
      };
    }
  }

  /**
   * 更新食物收藏状态（iOS优化）
   */
  public async updateFoodFavorite(
    foodId: number,
    isFavorite: boolean
  ): Promise<FoodOperationResult<void>> {
    try {
      console.log('[FoodService] 更新食物收藏状态:', { foodId, isFavorite });

      // 调用API更新收藏状态（iOS安全：布尔值转字符串）
      await this.apiClient.post(`/api/v1/food/${foodId}/favorite`, {
        is_favorite: String(isFavorite)
      });

      // iOS触觉反馈
      await this.provideiOSFeedback({
        hapticStyle: 'light',
        notificationTitle: isFavorite ? '已添加收藏' : '已取消收藏',
        notificationBody: isFavorite ? '食物已添加到收藏夹' : '食物已从收藏夹移除'
      });

      // 失效相关缓存
      await this.invalidateFoodCache(foodId);

      console.log('✅ [FoodService] 食物收藏状态更新成功');

      return {
        success: true
      };

    } catch (error) {
      console.error('❌ [FoodService] 更新食物收藏状态失败:', error);
      return {
        success: false,
        error: {
          code: 'FOOD_FAVORITE_FAILED',
          message: error instanceof Error ? error.message : '更新收藏状态失败',
          retryable: true
        }
      };
    }
  }

  /**
   * 获取用户收藏的食物
   */
  public async getFavoriteFoods(
    page: number = 0,
    forceRefresh: boolean = false
  ): Promise<FoodOperationResult<FoodPage>> {
    const cacheKey = this.generateCacheKey('favorites', { page });

    try {
      console.log('[FoodService] 获取收藏食物:', { page, forceRefresh });

      // 检查缓存
      if (!forceRefresh) {
        const cacheManager = await this.getCacheManager();
        const cachedFavorites = await cacheManager.get<FoodPage>(cacheKey);
        if (cachedFavorites) {
          console.log('🎯 [FoodService] 收藏食物缓存命中');
          return {
            success: true,
            data: cachedFavorites
          };
        }
      }

      // 从API获取
      const queryParams = {
        skip: String(page * this.pageSize),
        limit: String(this.pageSize)
      };

      const response = await this.apiClient.get<ApiFoodFavorite[]>(
        '/api/v1/food/favorites',
        queryParams
      );

      // 构建分页结果
      const result: FoodPage = {
        foods: response.map(fav => fav.food),
        hasMore: response.length === this.pageSize,
        total: response.length,
        currentPage: page
      };

      // 缓存结果
      const cacheManager = await this.getCacheManager();
      await cacheManager.set(cacheKey, result, this.CACHE_CONFIG.FOOD_FAVORITES);

      console.log('✅ [FoodService] 收藏食物获取成功');

      return {
        success: true,
        data: result
      };

    } catch (error) {
      console.error('❌ [FoodService] 获取收藏食物失败:', error);
      return {
        success: false,
        error: {
          code: 'FOOD_FAVORITES_FAILED',
          message: error instanceof Error ? error.message : '获取收藏食物失败',
          retryable: true
        }
      };
    }
  }

  /**
   * 评分食物
   */
  public async rateFoodItem(
    foodId: number,
    rating: number,
    comment?: string
  ): Promise<FoodOperationResult<ApiFoodRatingResponse>> {
    try {
      console.log('[FoodService] 评分食物:', { foodId, rating, comment });

      const request: ApiFoodRatingRequest = {
        food_id: foodId,
        rating: rating
      };

      if (comment) {
        request.comment = comment;
      }

      const response = await this.apiClient.post<ApiFoodRatingResponse>(
        `/api/v1/food/${foodId}/rating`,
        request
      );

      // iOS触觉反馈
      await this.provideiOSFeedback({
        hapticStyle: 'medium',
        notificationTitle: '评分成功',
        notificationBody: `您给这个食物评了${rating}分`
      });

      // 失效相关缓存
      await this.invalidateFoodCache(foodId);

      console.log('✅ [FoodService] 食物评分成功');

      return {
        success: true,
        data: response
      };

    } catch (error) {
      console.error('❌ [FoodService] 食物评分失败:', error);
      return {
        success: false,
        error: {
          code: 'FOOD_RATING_FAILED',
          message: error instanceof Error ? error.message : '食物评分失败',
          retryable: true
        }
      };
    }
  }

  /**
   * 批量获取食物信息
   */
  public async getBatchFoods(
    foodIds: number[],
    includeNutrition: boolean = true
  ): Promise<FoodOperationResult<ApiBatchFoodResponse>> {
    try {
      console.log('[FoodService] 批量获取食物:', { foodIds, includeNutrition });

      const request: ApiBatchFoodRequest = {
        food_ids: foodIds,
        include_nutrition: includeNutrition,
        include_favorites: true
      };

      const response = await this.apiClient.post<ApiBatchFoodResponse>(
        '/api/v1/food/batch',
        request
      );

      console.log('✅ [FoodService] 批量获取食物成功');

      return {
        success: true,
        data: response
      };

    } catch (error) {
      console.error('❌ [FoodService] 批量获取食物失败:', error);
      return {
        success: false,
        error: {
          code: 'BATCH_FOOD_FAILED',
          message: error instanceof Error ? error.message : '批量获取食物失败',
          retryable: true
        }
      };
    }
  }

  /**
   * 失效食物相关缓存
   */
  private async invalidateFoodCache(foodId: number): Promise<void> {
    try {
      const cacheManager = await this.getCacheManager();

      // 失效食物详情缓存
      const detailKey = this.generateCacheKey('detail', { foodId });
      await cacheManager.delete(detailKey);

      // 失效搜索缓存
      await cacheManager.invalidate(['food:search']);

      // 失效收藏缓存
      await cacheManager.invalidate(['food:favorites']);

      console.log('🗑️ [FoodService] 食物缓存失效完成');
    } catch (error) {
      console.warn('⚠️ [FoodService] 缓存失效失败:', error);
    }
  }

  /**
   * 清除所有食物缓存
   */
  public async clearAllCache(): Promise<void> {
    try {
      const cacheManager = await this.getCacheManager();
      await cacheManager.clear('food:*');
      console.log('🗑️ [FoodService] 所有食物缓存已清除');
    } catch (error) {
      console.warn('⚠️ [FoodService] 清除缓存失败:', error);
    }
  }

  /**
   * 获取缓存统计信息
   */
  public async getCacheStats(): Promise<any> {
    try {
      const cacheManager = await this.getCacheManager();
      return await cacheManager.getStats();
    } catch (error) {
      console.warn('⚠️ [FoodService] 获取缓存统计失败:', error);
      return null;
    }
  }
}

// 导出默认实例
export const foodService = new FoodService(new (await import('../core/ApiClient')).ApiClient());
