/**
 * Exercise数据服务模块 - 集成新缓存系统
 * 提供统一的动作数据管理，包含智能缓存机制、分页加载、筛选逻辑
 */

import { apiService } from './api';
import {
  ApiExerciseResponse,
  ApiExerciseDetailResponse,
  Exercise,
  ExerciseDetail,
  ExerciseSearchParams,
  MediaUploadResponse,
  mapApiExercisesToUIExercises,
  mapApiExerciseDetailToUIDetail
} from '../utils/exerciseDataMapper';
import {
  BODY_PART_CATEGORIES,
  EQUIPMENT_CATEGORIES,
  MUSCLE_CATEGORIES_API
} from '../constants/exerciseCategories';
import { CacheManager, CacheKeyBuilder } from './cache';
import GlobalCacheManager from './cache/GlobalCacheManager';
import { ApiClient } from './core/ApiClient';

// 配置常量
const PAGE_SIZE = 20;

// 筛选条件接口
export interface ExerciseFilters {
  bodyPartId?: number;
  equipmentId?: number;
  searchTerm?: string;
  favoritesOnly?: boolean;
}

// 分页数据接口
export interface ExercisePage {
  exercises: Exercise[];
  hasMore: boolean;
  total: number;
  currentPage: number;
}

// CachedExerciseData 接口已移除，使用新的缓存系统

class ExerciseService {
  private cacheManager: CacheManager | null = null;
  private apiClient: ApiClient;
  private readonly pageSize = PAGE_SIZE;

  // 缓存配置
  private readonly CACHE_CONFIG = {
    EXERCISES: {
      ttl: 10 * 60 * 1000,       // 10分钟
      tags: ['exercise', 'fitness'],
      priority: 'high' as const,
      persistent: true
    },
    SEARCH_RESULTS: {
      ttl: 5 * 60 * 1000,        // 5分钟
      tags: ['exercise', 'search'],
      priority: 'medium' as const,
      persistent: true
    },
    EXERCISE_DETAILS: {
      ttl: 15 * 60 * 1000,       // 15分钟
      tags: ['exercise', 'detail'],
      priority: 'high' as const,
      persistent: true
    }
  };

  constructor() {
    // 初始化API客户端
    this.apiClient = new ApiClient();

    // 异步初始化缓存管理器
    this.initializeCacheManager();

    console.log('[ExerciseService] 服务初始化完成');
  }

  /**
   * 异步初始化缓存管理器
   */
  private async initializeCacheManager(): Promise<void> {
    try {
      this.cacheManager = await GlobalCacheManager.getInstance();
      console.log('[ExerciseService] ✅ 全局缓存管理器连接成功');
    } catch (error) {
      console.error('[ExerciseService] ❌ 缓存管理器初始化失败:', error);
    }
  }

  /**
   * 获取缓存管理器（确保已初始化）
   */
  private async getCacheManager(): Promise<CacheManager> {
    if (!this.cacheManager) {
      this.cacheManager = await GlobalCacheManager.getInstance();
    }
    return this.cacheManager;
  }

  /**
   * 生成缓存键（使用新的CacheKeyBuilder）
   */
  private generateCacheKey(filters: ExerciseFilters, page: number): string {
    const { bodyPartId = 0, equipmentId = 0, searchTerm = '', favoritesOnly = false } = filters;
    
    return CacheKeyBuilder.create()
      .namespace('exercise')
      .resource(searchTerm ? 'search' : 'list')
      .params({
        bodyPartId,
        equipmentId,
        searchTerm,
        favoritesOnly,
        page
      })
      .version('2.0')  // 新版本缓存
      .build();
  }

  /**
   * 将UI筛选条件转换为API参数（扩展版）
   */
  private buildApiParams(filters: ExerciseFilters, page: number): ExerciseSearchParams {
    const params: ExerciseSearchParams = {
      skip: page * this.pageSize,
      limit: this.pageSize,
      // 设置默认值，确保API调用包含所有必需参数
      body_part_id: filters.bodyPartId ? [filters.bodyPartId] : [0],
      equipment_id: filters.equipmentId ? [filters.equipmentId] : [0]
    };

    if (filters.searchTerm && filters.searchTerm.trim()) {
      params.search = filters.searchTerm.trim();
      params.keyword = filters.searchTerm.trim(); // 兼容性字段
    }

    return params;
  }

  /**
   * 高级搜索动作（支持更多参数）
   */
  public async searchExercisesAdvanced(
    searchParams: ExerciseSearchParams,
    forceRefresh: boolean = false
  ): Promise<ExercisePage> {
    const cacheKey = CacheKeyBuilder.create()
      .namespace('exercise')
      .resource('advanced-search')
      .params(searchParams)
      .version('2.0')
      .build();

    console.log('[ExerciseService] 高级搜索动作:', { searchParams, cacheKey, forceRefresh });

    try {
      // 1. 检查缓存
      if (!forceRefresh) {
        const cacheManager = await this.getCacheManager();
        const cachedData = await cacheManager.get<ExercisePage>(cacheKey);
        if (cachedData) {
          console.log('🎯 [ExerciseService] 高级搜索缓存命中:', {
            exerciseCount: cachedData.exercises.length,
            cacheKey
          });
          return cachedData;
        }
      }

      // 2. 从API搜索
      console.log('📡 [ExerciseService] 执行高级搜索API调用');

      const apiExercises = await this.apiClient.get<ApiExerciseResponse[]>(
        '/api/v1/exercise/exercises/search',
        { params: searchParams }
      );

      // 3. 转换数据
      const exercises = mapApiExercisesToUIExercises(apiExercises);
      const hasMore = apiExercises.length === this.pageSize;

      const response: ExercisePage = {
        exercises,
        hasMore,
        total: exercises.length,
        currentPage: Math.floor((searchParams.skip || 0) / this.pageSize)
      };

      // 4. 缓存结果
      const cacheManager = await this.getCacheManager();
      await cacheManager.set(cacheKey, response, this.CACHE_CONFIG.SEARCH_RESULTS);

      console.log('✅ [ExerciseService] 高级搜索完成:', {
        resultCount: exercises.length,
        hasMore
      });

      return response;

    } catch (error) {
      console.error('❌ [ExerciseService] 高级搜索失败:', error);
      throw error;
    }
  }

  /**
   * 根据筛选条件名称获取ID
   */
  public getBodyPartId(bodyPartName: string): number {
    if (bodyPartName === '全部') return 0;
    const bodyPart = BODY_PART_CATEGORIES.find(bp => bp.name === bodyPartName);
    return bodyPart ? bodyPart.id : 0;
  }

  public getEquipmentId(equipmentName: string): number {
    if (equipmentName === '全部') return 0;
    const equipment = EQUIPMENT_CATEGORIES.find(eq => eq.name === equipmentName);
    return equipment ? equipment.id : 0;
  }

  /**
   * 获取动作数据（使用新缓存系统）
   */
  public async getExercises(
    filters: ExerciseFilters = {}, 
    page: number = 0,
    forceRefresh: boolean = false
  ): Promise<ExercisePage> {
    const cacheKey = this.generateCacheKey(filters, page);
    
    console.log('[ExerciseService] 获取动作数据:', { filters, page, cacheKey, forceRefresh });

    try {
      // 1. 检查缓存（除非强制刷新）
      if (!forceRefresh) {
        const cacheManager = await this.getCacheManager();
        const cachedData = await cacheManager.get<ExercisePage>(cacheKey);
        if (cachedData) {
          console.log('🎯 [ExerciseService] 缓存命中:', {
            exerciseCount: cachedData.exercises.length,
            hasMore: cachedData.hasMore,
            cacheKey
          });
          return cachedData;
        }
      }

      // 2. 缓存未命中，从API获取数据
      console.log('📡 [ExerciseService] 从API获取数据');
      
      const apiParams = this.buildApiParams(filters, page);
      let apiExercises: ApiExerciseResponse[];
      
      if (filters.searchTerm && filters.searchTerm.trim()) {
        // 搜索请求
        apiExercises = await apiService.searchExercises(filters.searchTerm, apiParams);
      } else {
        // 普通列表请求
        apiExercises = await apiService.getExercises(apiParams);
      }

      // 3. 转换和过滤数据
      const exercises = mapApiExercisesToUIExercises(apiExercises);
      const hasMore = apiExercises.length === this.pageSize;

      // 客户端筛选（收藏功能）
      let filteredExercises = exercises;
      if (filters.favoritesOnly) {
        filteredExercises = exercises.filter(ex => ex.is_favorite);
      }

      // 4. 构建响应数据
      const response: ExercisePage = {
        exercises: filteredExercises,
        hasMore,
        total: filteredExercises.length,
        currentPage: page
      };

      // 5. 缓存响应数据
      const cacheConfig = filters.searchTerm ?
        this.CACHE_CONFIG.SEARCH_RESULTS :
        this.CACHE_CONFIG.EXERCISES;

      const cacheManager = await this.getCacheManager();
      await cacheManager.set(cacheKey, response, cacheConfig);

      console.log('✅ [ExerciseService] 数据获取和缓存完成:', {
        receivedCount: apiExercises.length,
        filteredCount: filteredExercises.length,
        hasMore,
        cached: true
      });

      return response;

    } catch (error) {
      console.error('❌ [ExerciseService] 获取动作数据失败:', error);
      throw error;
    }
  }

  /**
   * 加载更多数据（追加到现有数据）
   */
  public async loadMore(
    filters: ExerciseFilters = {},
    currentExercises: Exercise[] = []
  ): Promise<{ exercises: Exercise[]; hasMore: boolean }> {
    const currentPage = Math.floor(currentExercises.length / this.pageSize);
    const nextPage = currentPage + 1;

    const result = await this.getExercises(filters, nextPage);
    
    // 合并数据，避免重复
    const existingIds = new Set(currentExercises.map(ex => ex.id));
    const newExercises = result.exercises.filter(ex => !existingIds.has(ex.id));
    
    return {
      exercises: [...currentExercises, ...newExercises],
      hasMore: result.hasMore
    };
  }

  /**
   * 清除缓存（使用新缓存系统）
   */
  public async clearCache(filters?: ExerciseFilters): Promise<void> {
    const cacheManager = await this.getCacheManager();
    if (filters) {
      // 清除特定筛选条件的缓存模式
      const pattern = `exercise:*bodyPartId=${filters.bodyPartId || 0}*`;
      await cacheManager.clear(pattern);
      console.log('🗑️ [ExerciseService] 清除特定筛选缓存:', { filters });
    } else {
      // 清除所有Exercise相关缓存
      await cacheManager.invalidate(['exercise', 'fitness']);
      console.log('🗑️ [ExerciseService] 清除所有Exercise缓存');
    }
  }

  /**
   * 获取缓存统计信息（使用新缓存系统）
   */
  public async getCacheStats(): Promise<{ totalEntries: number; validEntries: number; expiredEntries: number }> {
    const cacheManager = await this.getCacheManager();
    const stats = cacheManager.getStats();
    return {
      totalEntries: stats.totalEntries,
      validEntries: stats.memoryEntries + stats.persistentEntries,
      expiredEntries: Math.max(0, stats.totalEntries - stats.memoryEntries - stats.persistentEntries)
    };
  }

  /**
   * 清理过期缓存（使用新缓存系统）
   */
  public async cleanupExpiredCache(): Promise<void> {
    const cacheManager = await this.getCacheManager();
    await cacheManager.cleanup();
    console.log('🧹 [ExerciseService] 缓存清理完成');
  }

  /**
   * 预加载热门动作数据
   */
  public async preloadPopularExercises(): Promise<void> {
    try {
      // 预加载基础动作列表
      await this.getExercises({}, 0);
      
      // 预加载热门身体部位
      const popularBodyParts = [1, 2, 3]; // 胸部、背部、腿部
      const preloadPromises = popularBodyParts.map(bodyPartId =>
        this.getExercises({ bodyPartId }, 0)
      );
      
      await Promise.all(preloadPromises);
      console.log('🚀 [ExerciseService] 热门动作预加载完成');
      
    } catch (error) {
      console.warn('⚠️ [ExerciseService] 预加载失败:', error);
    }
  }

  /**
   * 智能缓存失效策略
   */
  public async invalidateRelatedCache(exerciseId?: string): Promise<void> {
    const cacheManager = await this.getCacheManager();
    if (exerciseId) {
      // 失效特定动作相关的缓存
      const pattern = `exercise:*`;
      await cacheManager.clear(pattern);
    } else {
      // 失效所有动作缓存（比如收藏状态变更时）
      await cacheManager.invalidate(['exercise']);
    }

    console.log('♻️ [ExerciseService] 相关缓存已失效');
  }

  /**
   * 获取动作详情（使用新缓存系统和iOS优化）
   */
  public async getExerciseDetail(
    exerciseId: string | number,
    forceRefresh: boolean = false
  ): Promise<ExerciseDetail> {
    const cacheKey = CacheKeyBuilder.create()
      .namespace('exercise')
      .resource('detail')
      .params({ exerciseId: exerciseId.toString() })
      .version('2.0')
      .build();

    console.log('[ExerciseService] 获取动作详情:', { exerciseId, cacheKey, forceRefresh });

    try {
      // 1. 检查缓存（除非强制刷新）
      if (!forceRefresh) {
        const cacheManager = await this.getCacheManager();
        const cachedDetail = await cacheManager.get<ExerciseDetail>(cacheKey);
        if (cachedDetail) {
          console.log('🎯 [ExerciseService] 动作详情缓存命中:', { exerciseId, cacheKey });
          return cachedDetail;
        }
      }

      // 2. 从API获取动作详情
      console.log('📡 [ExerciseService] 从API获取动作详情');

      const apiDetail = await this.apiClient.get<ApiExerciseDetailResponse>(
        `/api/v1/exercise/exercises/${exerciseId}/detail`
      );

      // 3. 转换数据格式
      const exerciseDetail = mapApiExerciseDetailToUIDetail(apiDetail);

      // 4. 缓存详情数据
      const cacheManager = await this.getCacheManager();
      await cacheManager.set(cacheKey, exerciseDetail, this.CACHE_CONFIG.EXERCISE_DETAILS);

      console.log('✅ [ExerciseService] 动作详情获取和缓存完成:', { exerciseId });

      return exerciseDetail;

    } catch (error) {
      console.error('❌ [ExerciseService] 获取动作详情失败:', error);
      throw error;
    }
  }

  /**
   * 增加动作点击次数（iOS触觉反馈）
   */
  public async incrementHitTime(exerciseId: string | number): Promise<void> {
    try {
      console.log('[ExerciseService] 增加动作点击次数:', { exerciseId });

      await this.apiClient.post(`/api/v1/exercise/exercises/${exerciseId}/hit`, {});

      // iOS触觉反馈
      if (this.apiClient.isNative && this.apiClient.platform === 'ios') {
        // 轻微触觉反馈表示操作成功
        try {
          const { Haptics, ImpactStyle } = await import('@capacitor/haptics');
          await Haptics.impact({ style: ImpactStyle.Light });
        } catch (error) {
          console.warn('⚠️ [ExerciseService] 触觉反馈失败:', error);
        }
      }

      console.log('✅ [ExerciseService] 动作点击次数增加成功');

    } catch (error) {
      console.error('❌ [ExerciseService] 增加动作点击次数失败:', error);
      throw error;
    }
  }

  /**
   * 更新动作收藏状态（iOS优化）
   */
  public async updateExerciseFavorite(
    exerciseId: string | number,
    isFavorite: boolean
  ): Promise<void> {
    try {
      console.log('[ExerciseService] 更新动作收藏状态:', { exerciseId, isFavorite });

      // 调用API更新收藏状态（iOS安全：布尔值转字符串）
      await this.apiClient.post(`/api/v1/exercise/exercises/${exerciseId}/favorite`, {
        is_favorite: String(isFavorite)
      });

      // iOS触觉反馈
      if (this.apiClient.isNative && this.apiClient.platform === 'ios') {
        try {
          const { Haptics, ImpactStyle } = await import('@capacitor/haptics');
          await Haptics.impact({ style: ImpactStyle.Medium });
        } catch (error) {
          console.warn('⚠️ [ExerciseService] 触觉反馈失败:', error);
        }
      }

      // 失效相关缓存
      await this.invalidateRelatedCache(exerciseId.toString());

      console.log('✅ [ExerciseService] 动作收藏状态更新成功');

    } catch (error) {
      console.error('❌ [ExerciseService] 更新动作收藏状态失败:', error);
      throw error;
    }
  }

  /**
   * 动作评分（iOS优化）
   */
  public async rateExercise(
    exerciseId: string | number,
    rating: number
  ): Promise<void> {
    try {
      console.log('[ExerciseService] 动作评分:', { exerciseId, rating });

      if (rating < 1 || rating > 5) {
        throw new Error('评分必须在1-5之间');
      }

      await this.apiClient.post(`/api/v1/exercise/exercises/${exerciseId}/rate`, {
        rating: String(rating)
      });

      // iOS触觉反馈
      if (this.apiClient.isNative && this.apiClient.platform === 'ios') {
        try {
          const { Haptics, ImpactStyle } = await import('@capacitor/haptics');
          await Haptics.impact({ style: ImpactStyle.Light });
        } catch (error) {
          console.warn('⚠️ [ExerciseService] 触觉反馈失败:', error);
        }
      }

      console.log('✅ [ExerciseService] 动作评分成功');

    } catch (error) {
      console.error('❌ [ExerciseService] 动作评分失败:', error);
      throw error;
    }
  }

  /**
   * 上传动作媒体文件（iOS优化）
   */
  public async uploadExerciseMedia(
    fileContent: File | Blob,
    fileType: 'image' | 'gif' | 'video'
  ): Promise<MediaUploadResponse> {
    try {
      console.log('[ExerciseService] 上传动作媒体文件:', { fileType });

      if (!fileContent) {
        throw new Error('文件内容为空');
      }

      // 创建FormData
      const formData = new FormData();
      formData.append('file', fileContent);
      formData.append('type', fileType);

      // 上传文件
      const result = await this.apiClient.post<{ file_name: string }>(
        `/api/v1/exercise/upload/${fileType}`,
        formData
      );

      if (!result || !result.file_name) {
        throw new Error('上传失败，服务器未返回有效的文件名');
      }

      console.log('✅ [ExerciseService] 媒体文件上传成功:', { fileName: result.file_name });

      return {
        success: true,
        fileName: result.file_name
      };

    } catch (error) {
      console.error('❌ [ExerciseService] 媒体文件上传失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : `上传${fileType}失败`
      };
    }
  }

  /**
   * 获取分类数据
   */
  public getBodyPartCategories() {
    return BODY_PART_CATEGORIES;
  }

  public getEquipmentCategories() {
    return EQUIPMENT_CATEGORIES;
  }

  public getMuscleCategories() {
    return MUSCLE_CATEGORIES_API;
  }

  /**
   * 获取缓存管理器实例（用于高级操作）
   */
  public async getCacheManagerInstance(): Promise<CacheManager> {
    return await this.getCacheManager();
  }
}

// 导出单例实例
export const exerciseService = new ExerciseService();
export default exerciseService; 