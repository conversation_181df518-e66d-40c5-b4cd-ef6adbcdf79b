/**
 * 用户认证服务
 * 整合了docs/api/auth.js的功能，适配Capacitor平台
 * 支持微信登录、手机验证码登录、token刷新等功能
 * 使用Capacitor Preferences替代localStorage，支持iOS原生存储
 */

import { apiClient } from './core/ApiClient';
import { Preferences } from '@capacitor/preferences';
import { Capacitor } from '@capacitor/core';
import { Haptics, ImpactStyle } from '@capacitor/haptics';
import { ENV_CONFIG, isDevelopment } from '../config/env';
import {
  validateAndFixExpiryTime,
  createSafeDate,
  isValidDate,
  safeToISOString,
  debugDateInfo,
  getDefaultExpiryTime
} from '../utils/dateValidation';

// 测试用户配置
const TEST_USER_CONFIG = {
  user_id: 1,
  email: '<EMAIL>',
  openid: 'oCU0j7Rg9kzigLzquCBje3KfnQXk'
} as const;

// 认证相关接口定义
export interface AuthTokens {
  accessToken: string;
  refreshToken?: string;
  expiresAt: string;
}

export interface AuthUser {
  id: number;
  email: string;
  openid: string;
  nickName: string;
  avatarUrl: string;
  phone?: string;
  gender?: number;
  country?: string;
  province?: string;
  city?: string;
  created_at?: string;
}

export interface AuthConfig {
  baseUrl: string;
  timeout: number;
  retryAttempts: number;
  mockMode: boolean;
}

export interface LoginResponse {
  success: boolean;
  token: string;
  user: AuthUser;
  expires_at?: string;
  is_new_user?: boolean;
}

// 微信登录相关类型
export interface WechatLoginData {
  code: string;
  userInfo?: WechatUserInfo | null;
  encryptedData?: string | null;
  iv?: string | null;
  openid?: string | null;
  unionid?: string | null;
}

export interface WechatUserInfo {
  nickName: string;
  avatarUrl: string;
  gender: number;
  country: string;
  province: string;
  city: string;
}

// 手机绑定相关类型
export interface PhoneBindData {
  encryptedData: string;
  iv: string;
  code: string;
}

// Token刷新响应类型
export interface TokenRefreshResponse {
  success: boolean;
  token: string;
  expires_at?: string;
}

// 开发环境配置
export interface DevEnvironmentConfig {
  USE_MOCK_DATA: boolean;
  MOCK_DELAY: number;
}

/**
 * 认证服务类
 * 整合微信登录、手机验证码登录、token管理等功能
 */
export class AuthService {
  private config: AuthConfig;
  private tokens: AuthTokens | null = null;
  private user: AuthUser | null = null;
  private refreshPromise: Promise<void> | null = null;

  // 开发环境配置
  private devConfig: DevEnvironmentConfig = {
    USE_MOCK_DATA: false, // 默认不使用模拟数据
    MOCK_DELAY: 800
  };

  constructor(config?: Partial<AuthConfig>) {
    this.config = {
      baseUrl: ENV_CONFIG.API_BASE_URL,
      timeout: 10000,
      retryAttempts: 3,
      mockMode: isDevelopment,
      ...config
    };

    this.initializeService();
  }

  /**
   * 初始化服务
   */
  private async initializeService(): Promise<void> {
    await this.loadStoredAuth();

    // 设置开发环境配置
    if (this.config.mockMode) {
      this.devConfig.USE_MOCK_DATA = true;
    }

    console.log('🔧 认证服务初始化完成:', {
      platform: Capacitor.getPlatform(),
      isNative: Capacitor.isNativePlatform(),
      mockMode: this.config.mockMode,
      useMockData: this.devConfig.USE_MOCK_DATA
    });
  }

  /**
   * 微信登录（iOS优化版）
   * @param data 微信登录数据，包含code和用户信息
   * @returns 返回登录结果
   */
  async wechatLogin(data: WechatLoginData): Promise<LoginResponse> {
    console.log('【微信登录】开始登录:', JSON.stringify(data));

    // iOS触觉反馈
    await this.triggerIOSHapticFeedback();

    try {
      // 检查是否使用模拟数据
      if (this.devConfig.USE_MOCK_DATA) {
        console.log('【微信登录】开发环境，使用模拟数据');
        return this.mockWechatLogin(data);
      }

      // 格式化数据以符合后端API要求
      const formattedData = {
        code: data.code,
        userInfo: data.userInfo || null,
        encryptedData: data.encryptedData || null,
        iv: data.iv || null,
        openid: data.openid || null,
        unionid: data.unionid || null
      };

      console.log('【微信登录】格式化后数据:', JSON.stringify(formattedData));

      // 发送登录请求
      const response = await apiClient.post<LoginResponse>(
        '/api/v1/auth/login/wechat',
        formattedData,
        true // 跳过认证
      );

      // 处理响应数据，确保avatarUrl包含完整URL
      if (response.user) {
        response.user = this.processUserAvatarUrl(response.user);

        // 处理时区问题
        if (response.user.created_at && typeof response.user.created_at === 'string') {
          response.user.created_at = response.user.created_at.replace(/([+-]\d{2}:\d{2})$/, '');
        }
      }

      // 保存认证信息
      if (response.success && response.token) {
        await this.saveAuthTokens({
          accessToken: response.token,
          expiresAt: validateAndFixExpiryTime(response.expires_at)
        });

        this.user = response.user;
        await this.saveUserToStorage();
      }

      console.log('【微信登录】登录成功:', response);
      return response;

    } catch (error) {
      console.error('【微信登录】登录失败:', error);

      // 登录失败时尝试使用模拟数据
      if (!this.devConfig.USE_MOCK_DATA) {
        console.log('【微信登录】登录失败，降级到模拟模式');
        return this.mockWechatLogin(data);
      }

      throw error;
    }
  }

  /**
   * 使用测试用户信息登录
   */
  async loginWithTestUser(): Promise<AuthUser> {
    console.log('【认证服务】开始测试用户登录');

    try {
      // 检查是否使用模拟模式
      if (this.config.mockMode) {
        console.log('【认证服务】开发环境，使用模拟数据');
        return this.mockLogin();
      }

      // 正式环境登录
      const response = await apiClient.post<LoginResponse>('/api/v1/auth/login/test', TEST_USER_CONFIG, true);

      if (!response.success) {
        throw new Error('登录失败: ' + (response as any).message);
      }

      // 保存认证信息
      await this.saveAuthTokens({
        accessToken: response.token,
        expiresAt: validateAndFixExpiryTime(response.expires_at)
      });

      this.user = response.user;
      await this.saveUserToStorage();

      console.log('【认证服务】测试用户登录成功', this.user);

      if (!this.user) {
        throw new Error('用户信息保存失败');
      }

      return this.user;

    } catch (error) {
      console.error('【认证服务】测试用户登录失败:', error);
      
      // 登录失败时尝试使用模拟数据
      if (!this.config.mockMode) {
        console.log('【认证服务】登录失败，降级到模拟模式');
        return this.mockLogin();
      }
      
      throw error;
    }
  }

  /**
   * 模拟微信登录
   */
  private async mockWechatLogin(data: WechatLoginData): Promise<LoginResponse> {
    await new Promise(resolve => setTimeout(resolve, this.devConfig.MOCK_DELAY));

    const mockResponse: LoginResponse = {
      success: true,
      token: 'mock_token_' + Date.now(),
      user: {
        id: 1,
        email: TEST_USER_CONFIG.email,
        openid: TEST_USER_CONFIG.openid,
        nickName: data.userInfo ? data.userInfo.nickName : '微信用户',
        avatarUrl: data.userInfo ? data.userInfo.avatarUrl : '/api/placeholder/40/40',
        phone: '',
        gender: data.userInfo ? data.userInfo.gender : 0,
        country: data.userInfo ? data.userInfo.country : '',
        province: data.userInfo ? data.userInfo.province : '',
        city: data.userInfo ? data.userInfo.city : '',
        created_at: new Date().toISOString()
      },
      is_new_user: Math.random() > 0.5
    };

    // 确保模拟数据中的avatarUrl是完整URL
    mockResponse.user = this.processUserAvatarUrl(mockResponse.user);

    console.log('【微信登录】返回模拟数据:', mockResponse);
    return mockResponse;
  }

  /**
   * 处理用户头像URL，确保是完整URL
   */
  private processUserAvatarUrl(user: AuthUser): AuthUser {
    if (user.avatarUrl &&
        typeof user.avatarUrl === 'string' &&
        !user.avatarUrl.startsWith('http') &&
        !user.avatarUrl.startsWith('data:')) {
      user.avatarUrl = `${this.config.baseUrl}${user.avatarUrl}`;
    }
    return user;
  }

  /**
   * 模拟登录（开发环境或降级使用）
   */
  private mockLogin(): AuthUser {
    const mockUser: AuthUser = {
      id: TEST_USER_CONFIG.user_id,
      email: TEST_USER_CONFIG.email,
      openid: TEST_USER_CONFIG.openid,
      nickName: '测试用户',
      avatarUrl: '/api/placeholder/40/40',
      phone: '138****1234',
      gender: 1,
      country: '中国',
      province: '上海',
      city: '上海',
      created_at: new Date().toISOString()
    };

    this.tokens = {
      accessToken: 'mock_token_' + Date.now(),
      expiresAt: this.getDefaultExpiry()
    };

    this.user = mockUser;
    this.saveAuthToStorage();

    console.log('【认证服务】模拟登录成功', mockUser);
    return mockUser;
  }

  /**
   * 发送手机验证码
   * @param phone 手机号
   * @returns 返回发送结果
   */
  async sendSmsCode(phone: string): Promise<{ success: boolean; message: string }> {
    console.log('【发送验证码】手机号:', phone);

    try {
      if (this.devConfig.USE_MOCK_DATA) {
        await new Promise(resolve => setTimeout(resolve, this.devConfig.MOCK_DELAY));
        return { success: true, message: '验证码发送成功' };
      }

      const response = await apiClient.post<{ success: boolean; message: string }>(
        '/api/v1/auth/sms/code',
        { phone },
        true
      );

      return response;
    } catch (error) {
      console.error('【发送验证码】失败:', error);
      // 降级到模拟模式
      return { success: true, message: '验证码发送成功（模拟）' };
    }
  }

  /**
   * 手机号验证码登录
   * @param phone 手机号
   * @param code 验证码
   * @returns 返回登录结果
   */
  async phoneLogin(phone: string, code: string): Promise<LoginResponse> {
    console.log('【手机登录】手机号:', phone, '验证码:', code);

    try {
      if (this.devConfig.USE_MOCK_DATA || code === '123456') {
        await new Promise(resolve => setTimeout(resolve, this.devConfig.MOCK_DELAY));

        const mockResponse: LoginResponse = {
          success: true,
          token: 'mock_token_' + Date.now(),
          user: {
            id: 1,
            email: TEST_USER_CONFIG.email,
            openid: TEST_USER_CONFIG.openid,
            nickName: '用户' + phone.substring(7),
            avatarUrl: '/api/placeholder/40/40',
            phone: phone,
            created_at: new Date().toISOString()
          }
        };

        mockResponse.user = this.processUserAvatarUrl(mockResponse.user);
        return mockResponse;
      }

      const response = await apiClient.post<LoginResponse>(
        '/api/v1/auth/login/phone',
        { phone, code },
        true
      );

      if (response.success && response.token) {
        await this.saveAuthTokens({
          accessToken: response.token,
          expiresAt: validateAndFixExpiryTime(response.expires_at)
        });

        this.user = response.user;
        await this.saveUserToStorage();
      }

      return response;
    } catch (error) {
      console.error('【手机登录】失败:', error);
      throw error;
    }
  }

  /**
   * 绑定微信手机号
   * @param data 包含加密数据和iv的对象
   * @returns 返回绑定结果
   */
  async bindWechatPhone(data: PhoneBindData): Promise<{ success: boolean; user: AuthUser; message: string }> {
    console.log('【绑定手机号】请求数据:', JSON.stringify(data));

    try {
      if (this.devConfig.USE_MOCK_DATA) {
        await new Promise(resolve => setTimeout(resolve, this.devConfig.MOCK_DELAY));

        const mockResponse = {
          success: true,
          user: {
            ...this.user!,
            phone: '138****1234'
          },
          message: '手机号绑定成功'
        };

        this.user = mockResponse.user;
        await this.saveUserToStorage();

        return mockResponse;
      }

      const formattedData = {
        encrypted_data: data.encryptedData,
        iv: data.iv,
        code: data.code
      };

      const response = await apiClient.post<{ success: boolean; user: AuthUser; message: string }>(
        '/api/v1/users/bind_phone',
        formattedData
      );

      if (response.success && response.user) {
        response.user = this.processUserAvatarUrl(response.user);
        this.user = response.user;
        await this.saveUserToStorage();
      }

      return response;
    } catch (error) {
      console.error('【绑定手机号】失败:', error);
      throw error;
    }
  }

  /**
   * 获取认证头信息
   */
  getAuthHeaders(): Record<string, string> {
    if (!this.tokens?.accessToken) {
      throw new Error('用户未登录，无法获取认证头');
    }

    return {
      'Authorization': `Bearer ${this.tokens.accessToken}`,
      'Content-Type': 'application/json',
      'X-User-ID': this.user?.id.toString() || '',
      'X-User-OpenID': this.user?.openid || ''
    };
  }

  /**
   * 检查token是否有效
   */
  isTokenValid(): boolean {
    if (!this.tokens) {
      console.log('【认证服务】无token信息');
      return false;
    }

    if (!this.tokens.expiresAt) {
      console.log('【认证服务】无过期时间信息');
      return false;
    }

    try {
      debugDateInfo('Token过期时间检查', this.tokens.expiresAt);

      const now = new Date();
      const expiresAt = createSafeDate(this.tokens.expiresAt);

      // 检查日期是否有效
      if (!expiresAt || !isValidDate(expiresAt)) {
        console.error('【认证服务】无效的过期时间格式:', {
          expiresAt: this.tokens.expiresAt,
          type: typeof this.tokens.expiresAt,
          parsedDate: expiresAt
        });

        // 尝试修复过期时间
        this.tokens.expiresAt = validateAndFixExpiryTime(this.tokens.expiresAt);
        console.log('【认证服务】已修复过期时间:', this.tokens.expiresAt);

        // 重新检查修复后的时间
        const fixedExpiresAt = createSafeDate(this.tokens.expiresAt);
        if (!fixedExpiresAt || !isValidDate(fixedExpiresAt)) {
          return false;
        }

        return now < fixedExpiresAt;
      }

      const isValid = now < expiresAt;
      const currentIso = safeToISOString(now);
      const expiresIso = safeToISOString(expiresAt);

      console.log('【认证服务】Token有效性检查:', {
        current: currentIso,
        expires: expiresIso,
        valid: isValid,
        timeUntilExpiry: expiresAt.getTime() - now.getTime()
      });

      return isValid;

    } catch (error) {
      console.error('【认证服务】Token有效性检查失败:', {
        error: error instanceof Error ? error.message : String(error),
        expiresAt: this.tokens.expiresAt,
        stack: error instanceof Error ? error.stack : undefined
      });
      return false;
    }
  }

  /**
   * 自动刷新token（如果需要）
   */
  async refreshTokenIfNeeded(): Promise<void> {
    // 如果token仍然有效，无需刷新
    if (this.isTokenValid()) {
      return;
    }

    // 如果已经在刷新中，等待刷新完成
    if (this.refreshPromise) {
      return this.refreshPromise;
    }

    console.log('【认证服务】开始刷新token');

    this.refreshPromise = this.performTokenRefresh();
    
    try {
      await this.refreshPromise;
    } finally {
      this.refreshPromise = null;
    }
  }

  /**
   * 执行token刷新
   */
  private async performTokenRefresh(): Promise<void> {
    try {
      if (this.config.mockMode || !this.tokens) {
        // 模拟模式或无token时，重新登录
        await this.loginWithTestUser();
        return;
      }

      const response = await apiClient.post<TokenRefreshResponse>(
        '/api/v1/auth/refresh',
        { refresh_token: this.tokens.refreshToken }
      );

      if (!response.success) {
        throw new Error('刷新token失败');
      }

      // 更新token信息
      await this.saveAuthTokens({
        accessToken: response.token,
        refreshToken: this.tokens.refreshToken,
        expiresAt: validateAndFixExpiryTime(response.expires_at)
      });

      console.log('【认证服务】Token刷新成功');

    } catch (error) {
      console.error('【认证服务】Token刷新失败:', error);

      // 刷新失败时清除认证信息并重新登录
      await this.logout();
      await this.loginWithTestUser();
    }
  }

  /**
   * 获取当前用户信息
   */
  getCurrentUser(): AuthUser | null {
    return this.user;
  }

  /**
   * 检查是否已登录
   */
  isAuthenticated(): boolean {
    return this.user !== null && this.isTokenValid();
  }

  /**
   * 登出
   */
  async logout(): Promise<void> {
    console.log('【认证服务】用户登出');

    try {
      // 尝试调用后端登出接口
      if (this.tokens?.accessToken && !this.config.mockMode) {
        await apiClient.post('/api/v1/auth/logout', {});
      }
    } catch (error) {
      console.warn('【认证服务】后端登出失败:', error);
    } finally {
      // 无论后端调用是否成功，都清除本地认证信息
      this.tokens = null;
      this.user = null;
      await this.clearStoredAuth();
    }
  }

  /**
   * 验证token有效性
   * @returns 返回验证结果
   */
  async verifyToken(): Promise<{ valid: boolean }> {
    try {
      if (!this.tokens?.accessToken) {
        return { valid: false };
      }

      if (this.devConfig.USE_MOCK_DATA) {
        await new Promise(resolve => setTimeout(resolve, this.devConfig.MOCK_DELAY));
        return { valid: true };
      }

      await apiClient.post('/api/v1/auth/verify', {});
      return { valid: true };
    } catch (error) {
      console.error('【认证服务】验证token失败:', error);
      return { valid: false };
    }
  }

  /**
   * 测试服务器连接
   */
  async testConnection(): Promise<boolean> {
    return apiClient.testConnection();
  }

  /**
   * 设置开发环境配置
   */
  setDevConfig(config: Partial<DevEnvironmentConfig>): void {
    this.devConfig = { ...this.devConfig, ...config };
  }

  /**
   * 获取环境信息（iOS增强版）
   */
  getEnvironmentInfo() {
    return {
      ...apiClient.getEnvironmentInfo(),
      mockMode: this.config.mockMode,
      useMockData: this.devConfig.USE_MOCK_DATA,
      deviceInfo: apiClient.getDeviceInfo(),
      networkStatus: apiClient.getNetworkStatus(),
      isOnline: apiClient.isNetworkOnline()
    };
  }

  /**
   * iOS触觉反馈
   */
  private async triggerIOSHapticFeedback(): Promise<void> {
    if (Capacitor.getPlatform() === 'ios' && Capacitor.isNativePlatform()) {
      try {
        await Haptics.impact({ style: ImpactStyle.Light });
      } catch (error) {
        // 触觉反馈失败不影响主要功能
        console.warn('⚠️ iOS触觉反馈失败:', error);
      }
    }
  }

  /**
   * iOS状态栏样式同步
   */
  async syncIOSStatusBar(theme: 'light' | 'dark'): Promise<void> {
    await apiClient.setIOSStatusBarStyle(theme);
  }

  /**
   * iOS网络诊断
   */
  getIOSNetworkDiagnostics() {
    return apiClient.getIOSNetworkDiagnostics();
  }

  /**
   * iOS安全存储检查
   */
  async checkIOSSecureStorage(): Promise<{ available: boolean; encrypted: boolean }> {
    try {
      // 测试存储功能
      const testKey = 'ios_storage_test';
      const testValue = 'test_value_' + Date.now();

      await Preferences.set({ key: testKey, value: testValue });
      const result = await Preferences.get({ key: testKey });
      await Preferences.remove({ key: testKey });

      const available = result.value === testValue;

      return {
        available,
        encrypted: Capacitor.getPlatform() === 'ios' // iOS Preferences默认加密
      };
    } catch (error) {
      console.error('❌ iOS存储检查失败:', error);
      return { available: false, encrypted: false };
    }
  }

  /**
   * 从本地存储加载认证信息
   */
  private async loadStoredAuth(): Promise<void> {
    try {
      const [tokensResult, userResult] = await Promise.all([
        Preferences.get({ key: 'fitmaster_auth_tokens' }),
        Preferences.get({ key: 'fitmaster_auth_user' })
      ]);

      if (tokensResult.value) {
        try {
          const parsedTokens = JSON.parse(tokensResult.value);

          // 验证token数据结构
          if (parsedTokens && typeof parsedTokens === 'object') {
            // 使用安全的日期验证和修复
            debugDateInfo('存储的过期时间', parsedTokens.expiresAt);

            parsedTokens.expiresAt = validateAndFixExpiryTime(parsedTokens.expiresAt);

            this.tokens = parsedTokens;

            // 验证修复后的日期
            const verifyDate = createSafeDate(parsedTokens.expiresAt);

            console.log('【认证服务】Token验证通过:', {
              hasAccessToken: !!parsedTokens.accessToken,
              hasRefreshToken: !!parsedTokens.refreshToken,
              expiresAt: parsedTokens.expiresAt,
              isValidDate: !!verifyDate && isValidDate(verifyDate),
              dateObject: verifyDate
            });
          } else {
            console.warn('【认证服务】存储的token数据格式无效');
            this.tokens = null;
          }
        } catch (parseError) {
          console.error('【认证服务】解析token数据失败:', parseError);
          this.tokens = null;
        }
      }

      if (userResult.value) {
        try {
          this.user = JSON.parse(userResult.value);
        } catch (parseError) {
          console.error('【认证服务】解析用户数据失败:', parseError);
          this.user = null;
        }
      }

      console.log('【认证服务】加载存储的认证信息:', {
        hasTokens: !!this.tokens,
        hasUser: !!this.user,
        tokenValid: this.tokens ? this.isTokenValid() : false,
        platform: Capacitor.getPlatform()
      });

    } catch (error) {
      console.error('【认证服务】加载存储认证信息失败:', {
        error: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined
      });
      await this.clearStoredAuth();
    }
  }

  /**
   * 保存认证令牌到存储
   */
  private async saveAuthTokens(tokens: AuthTokens): Promise<void> {
    try {
      await Preferences.set({
        key: 'fitmaster_auth_tokens',
        value: JSON.stringify(tokens)
      });

      this.tokens = tokens;

      // 同时保存到ApiClient
      await apiClient.saveAuthTokens({
        token: tokens.accessToken,
        expires_at: tokens.expiresAt
      });

      console.log('【认证服务】认证令牌已保存到存储');
    } catch (error) {
      console.error('【认证服务】保存认证令牌失败:', error);
    }
  }

  /**
   * 保存用户信息到存储
   */
  private async saveUserToStorage(): Promise<void> {
    try {
      if (this.user) {
        await Preferences.set({
          key: 'fitmaster_auth_user',
          value: JSON.stringify(this.user)
        });
        console.log('【认证服务】用户信息已保存到存储');
      }
    } catch (error) {
      console.error('【认证服务】保存用户信息失败:', error);
    }
  }

  /**
   * 保存认证信息到本地存储（兼容旧方法）
   */
  private async saveAuthToStorage(): Promise<void> {
    await Promise.all([
      this.tokens ? this.saveAuthTokens(this.tokens) : Promise.resolve(),
      this.saveUserToStorage()
    ]);
  }

  /**
   * 清除本地存储的认证信息
   */
  private async clearStoredAuth(): Promise<void> {
    try {
      await Promise.all([
        Preferences.remove({ key: 'fitmaster_auth_tokens' }),
        Preferences.remove({ key: 'fitmaster_auth_user' })
      ]);

      // 同时清除ApiClient中的令牌
      await apiClient.clearAuthTokens();

      console.log('【认证服务】已清除本地存储的认证信息');
    } catch (error) {
      console.error('【认证服务】清除存储信息失败:', error);
    }
  }

  /**
   * 获取默认过期时间（24小时后）
   */
  private getDefaultExpiry(): string {
    try {
      const defaultExpiry = getDefaultExpiryTime();

      console.log('【认证服务】生成默认过期时间:', {
        current: safeToISOString(new Date()),
        expiry: defaultExpiry,
        hoursFromNow: 24
      });

      return defaultExpiry;

    } catch (error) {
      console.error('【认证服务】生成默认过期时间失败:', error);
      // 最后的备用方案：使用工具函数的默认实现
      return getDefaultExpiryTime();
    }
  }
}

// 创建单例实例
export const authService = new AuthService();

// 导出默认实例
export default authService;
