/**
 * 用户服务
 * 整合docs/api/user.js的功能，适配Capacitor平台
 * 支持用户资料管理、头像上传、设置管理等功能
 */

import { apiClient } from '../core/ApiClient';
import { AuthUser } from '../authService';

// 用户资料相关类型
export interface UserProfile extends AuthUser {
  bio?: string;
  birthday?: string;
  height?: number;
  weight?: number;
  fitness_level?: string;
  goals?: string[];
  preferences?: UserPreferences;
}

export interface UserPreferences {
  units: 'metric' | 'imperial';
  language: string;
  notifications: NotificationSettings;
  privacy: PrivacySettings;
}

export interface NotificationSettings {
  workout_reminders: boolean;
  achievement_alerts: boolean;
  social_updates: boolean;
  email_notifications: boolean;
}

export interface PrivacySettings {
  profile_visibility: 'public' | 'friends' | 'private';
  workout_visibility: 'public' | 'friends' | 'private';
  allow_friend_requests: boolean;
}

// 用户设置类型
export interface UserSettings {
  theme: 'light' | 'dark' | 'auto';
  language: string;
  units: 'metric' | 'imperial';
  notifications: NotificationSettings;
  privacy: PrivacySettings;
}

// 头像上传相关类型
export interface AvatarUploadResponse {
  success: boolean;
  avatar_url: string;
  avatarUrl?: string;
}

// 二维码生成类型
export interface QrCodeRequest {
  page: string;
  scene: string;
  width?: number;
}

export interface QrCodeResponse {
  success: boolean;
  url: string;
}

// 分享追踪类型
export interface ShareTrackData {
  type: string;
  content_id?: string;
  platform?: string;
  user_id?: number;
}

/**
 * 用户服务类
 */
export class UserService {
  /**
   * 获取用户个人资料
   */
  async getUserProfile(): Promise<{ user: UserProfile }> {
    try {
      console.log('【用户服务】获取用户资料');
      
      const response = await apiClient.get<{ user: UserProfile }>('/api/v1/user/profile');
      
      // 处理头像URL
      if (response.user) {
        response.user = this.processUserAvatarUrl(response.user);
      }
      
      console.log('【用户服务】获取用户资料成功');
      return response;
    } catch (error) {
      console.error('【用户服务】获取用户资料失败:', error);
      throw error;
    }
  }

  /**
   * 根据openid检查用户是否存在
   */
  async checkUserByOpenid(openid: string): Promise<{ exists: boolean; user?: UserProfile }> {
    try {
      console.log('【用户服务】检查用户是否存在:', openid);
      
      const response = await apiClient.get<{ exists: boolean; user?: UserProfile }>(
        `/api/v1/user/check?openid=${openid}`
      );
      
      if (response.user) {
        response.user = this.processUserAvatarUrl(response.user);
      }
      
      return response;
    } catch (error) {
      console.error('【用户服务】检查用户失败:', error);
      throw error;
    }
  }

  /**
   * 更新用户个人资料
   */
  async updateUserProfile(data: Partial<UserProfile>): Promise<{ user: UserProfile }> {
    try {
      console.log('【用户服务】更新用户资料:', data);
      
      // 检查是否有头像base64数据需要单独处理
      if (data.avatarUrl && typeof data.avatarUrl === 'string' && data.avatarUrl.startsWith('data:')) {
        console.log('【用户服务】检测到base64头像数据，先上传头像');
        
        const avatarBase64 = data.avatarUrl;
        delete data.avatarUrl;
        
        // 先上传头像
        const avatarResult = await this.uploadAvatar(avatarBase64);
        
        // 将返回的头像URL添加到用户资料中
        if (avatarResult.avatar_url) {
          data.avatarUrl = avatarResult.avatar_url;
        }
      }
      
      // 更新其他资料
      const response = await apiClient.post<{ user: UserProfile }>('/api/v1/user/profile', data);
      
      if (response.user) {
        response.user = this.processUserAvatarUrl(response.user);
      }
      
      console.log('【用户服务】更新用户资料成功');
      return response;
    } catch (error) {
      console.error('【用户服务】更新用户资料失败:', error);
      throw error;
    }
  }

  /**
   * 上传用户头像
   */
  async uploadAvatar(avatarData: string): Promise<AvatarUploadResponse> {
    try {
      console.log('【用户服务】上传头像');
      
      let response: AvatarUploadResponse;
      
      if (avatarData.startsWith('data:')) {
        // Base64数据
        console.log('【用户服务】上传base64头像数据');
        response = await apiClient.request<AvatarUploadResponse>(
          '/api/v1/user/avatar',
          {
            method: 'POST',
            data: { avatar_url: avatarData },
            headers: {
              'Content-Type': 'application/x-www-form-urlencoded'
            }
          }
        );
      } else if (avatarData.startsWith('http://') || avatarData.startsWith('https://')) {
        // URL数据
        console.log('【用户服务】提交头像URL:', avatarData);
        response = await apiClient.post<AvatarUploadResponse>(
          '/api/v1/user/avatar',
          { avatar_url: avatarData }
        );
      } else {
        throw new Error('不支持的头像数据格式');
      }
      
      // 处理返回的头像URL
      if (response.avatar_url) {
        response.avatar_url = this.processAvatarUrl(response.avatar_url);
      }
      if (response.avatarUrl) {
        response.avatarUrl = this.processAvatarUrl(response.avatarUrl);
      }
      
      console.log('【用户服务】头像上传成功');
      return response;
    } catch (error) {
      console.error('【用户服务】头像上传失败:', error);
      throw error;
    }
  }

  /**
   * 获取用户设置
   */
  async getUserSettings(): Promise<UserSettings> {
    try {
      console.log('【用户服务】获取用户设置');
      
      const response = await apiClient.get<UserSettings>('/api/v1/user/settings');
      
      console.log('【用户服务】获取用户设置成功');
      return response;
    } catch (error) {
      console.error('【用户服务】获取用户设置失败:', error);
      throw error;
    }
  }

  /**
   * 更新用户设置
   */
  async updateUserSettings(data: Partial<UserSettings>): Promise<UserSettings> {
    try {
      console.log('【用户服务】更新用户设置:', data);
      
      const response = await apiClient.post<UserSettings>('/api/v1/user/settings', data);
      
      console.log('【用户服务】更新用户设置成功');
      return response;
    } catch (error) {
      console.error('【用户服务】更新用户设置失败:', error);
      throw error;
    }
  }

  /**
   * 生成分享二维码
   */
  async generateQrCode(data: QrCodeRequest): Promise<QrCodeResponse> {
    try {
      console.log('【用户服务】生成二维码:', data);
      
      const response = await apiClient.post<QrCodeResponse>('/api/v1/qrcode/generate', data);
      
      // 处理二维码URL
      if (response.url) {
        response.url = this.processAvatarUrl(response.url);
      }
      
      console.log('【用户服务】生成二维码成功');
      return response;
    } catch (error) {
      console.error('【用户服务】生成二维码失败:', error);
      throw error;
    }
  }

  /**
   * 记录分享数据
   */
  async trackShare(data: ShareTrackData): Promise<{ success: boolean }> {
    try {
      console.log('【用户服务】记录分享数据:', data);
      
      const response = await apiClient.post<{ success: boolean }>('/api/v1/share/track', data);
      
      console.log('【用户服务】记录分享数据成功');
      return response;
    } catch (error) {
      console.error('【用户服务】记录分享数据失败:', error);
      throw error;
    }
  }

  /**
   * 处理用户头像URL，确保是完整URL
   */
  private processUserAvatarUrl<T extends { avatarUrl?: string; avatar_url?: string }>(user: T): T {
    if (user.avatarUrl) {
      user.avatarUrl = this.processAvatarUrl(user.avatarUrl);
    }
    if (user.avatar_url) {
      user.avatar_url = this.processAvatarUrl(user.avatar_url);
    }
    return user;
  }

  /**
   * 处理头像URL
   */
  private processAvatarUrl(url: string): string {
    if (url && 
        typeof url === 'string' && 
        !url.startsWith('http') &&
        !url.startsWith('data:')) {
      return apiClient.buildResourceUrl(url);
    }
    return url;
  }
}

// 导出单例实例
export const userService = new UserService();
export default userService;
