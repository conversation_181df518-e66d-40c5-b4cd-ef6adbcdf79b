/**
 * 全局单例缓存管理器
 * 解决多个服务重复初始化缓存管理器导致的性能问题
 */

import { CacheManager, CapacitorPersistentCache } from './index';

class GlobalCacheManager {
  private static instance: CacheManager | null = null;
  private static isInitializing: boolean = false;
  private static initPromise: Promise<CacheManager> | null = null;

  /**
   * 获取全局缓存管理器单例
   */
  public static async getInstance(): Promise<CacheManager> {
    // 如果已经有实例，直接返回
    if (GlobalCacheManager.instance) {
      return GlobalCacheManager.instance;
    }

    // 如果正在初始化，等待初始化完成
    if (GlobalCacheManager.isInitializing && GlobalCacheManager.initPromise) {
      return GlobalCacheManager.initPromise;
    }

    // 开始初始化
    GlobalCacheManager.isInitializing = true;
    GlobalCacheManager.initPromise = GlobalCacheManager.createInstance();

    try {
      GlobalCacheManager.instance = await GlobalCacheManager.initPromise;
      return GlobalCacheManager.instance;
    } finally {
      GlobalCacheManager.isInitializing = false;
      GlobalCacheManager.initPromise = null;
    }
  }

  /**
   * 创建缓存管理器实例
   */
  private static async createInstance(): Promise<CacheManager> {
    console.log('[GlobalCacheManager] 开始初始化全局缓存管理器');

    try {
      // 创建持久化缓存实例
      const persistentCache = new CapacitorPersistentCache();

      // 创建缓存管理器实例
      const cacheManager = new CacheManager({
        maxMemoryEntries: 800,        // 适中的内存缓存条目数
        maxMemorySize: 30 * 1024 * 1024, // 30MB内存缓存
        maxPersistentEntries: 3000,   // 持久化缓存条目数
        maxPersistentSize: 80 * 1024 * 1024, // 80MB持久化缓存
        cleanupThreshold: 0.8,        // 80%时触发清理
        cleanupInterval: 5 * 60 * 1000 // 5分钟清理间隔
      }, persistentCache);

      console.log('[GlobalCacheManager] ✅ 全局缓存管理器初始化完成');
      return cacheManager;

    } catch (error) {
      console.error('[GlobalCacheManager] ❌ 全局缓存管理器初始化失败:', error);
      throw error;
    }
  }

  /**
   * 重置全局缓存管理器（用于测试或重新初始化）
   */
  public static reset(): void {
    GlobalCacheManager.instance = null;
    GlobalCacheManager.isInitializing = false;
    GlobalCacheManager.initPromise = null;
    console.log('[GlobalCacheManager] 🔄 全局缓存管理器已重置');
  }

  /**
   * 获取缓存统计信息
   */
  public static async getStats() {
    const instance = await GlobalCacheManager.getInstance();
    return instance.getStats();
  }

  /**
   * 清理全局缓存
   */
  public static async cleanup() {
    const instance = await GlobalCacheManager.getInstance();
    return instance.cleanup();
  }
}

export default GlobalCacheManager;
