/**
 * 服务缓存适配器
 * 统一管理跨服务的缓存失效策略和数据同步
 */

import { CacheManager } from './CacheManager'
import GlobalCacheManager from './GlobalCacheManager'

/**
 * 缓存事件类型
 */
export type CacheEventType = 
  | 'user_action'      // 用户操作（点赞、关注等）
  | 'data_mutation'    // 数据变更（创建、更新、删除）
  | 'user_preference'  // 用户偏好变更（收藏、设置等）
  | 'session_change'   // 会话变更（登录、登出）

/**
 * 缓存事件接口
 */
export interface CacheEvent {
  type: CacheEventType
  source: string       // 触发源服务
  target?: string      // 目标资源ID
  metadata?: Record<string, any>
  timestamp: number
}

/**
 * 缓存失效规则配置
 */
interface InvalidationRule {
  eventType: CacheEventType
  targetTags: string[]
  condition?: (event: CacheEvent) => boolean
  delay?: number       // 延迟失效（毫秒）
}

/**
 * 服务缓存适配器
 */
export class ServiceCacheAdapter {
  private static instance: ServiceCacheAdapter
  private cacheManager: CacheManager | null = null
  private invalidationRules: InvalidationRule[] = []
  private eventQueue: CacheEvent[] = []
  private processingQueue = false

  private constructor() {
    this.initializeCacheManager()
    this.setupDefaultRules()
    console.log('[ServiceCacheAdapter] 缓存适配器初始化完成')
  }

  private async initializeCacheManager() {
    this.cacheManager = await GlobalCacheManager.getInstance()
  }

  /**
   * 获取单例实例
   */
  static getInstance(): ServiceCacheAdapter {
    if (!this.instance) {
      this.instance = new ServiceCacheAdapter()
    }
    return this.instance
  }

  /**
   * 获取缓存管理器
   */
  async getCacheManager(): Promise<CacheManager> {
    if (!this.cacheManager) {
      await this.initializeCacheManager()
    }
    return this.cacheManager!
  }

  /**
   * 设置默认缓存失效规则
   */
  private setupDefaultRules(): void {
    this.invalidationRules = [
      // 用户操作规则
      {
        eventType: 'user_action',
        targetTags: ['feed', 'posts'],
        condition: (event) => ['like', 'unlike', 'share'].includes(event.metadata?.action)
      },
      {
        eventType: 'user_action', 
        targetTags: ['user', 'stats'],
        condition: (event) => ['follow', 'unfollow'].includes(event.metadata?.action)
      },

      // 数据变更规则
      {
        eventType: 'data_mutation',
        targetTags: ['feed', 'posts'],
        condition: (event) => ['create_post', 'update_post', 'delete_post'].includes(event.metadata?.action)
      },
      {
        eventType: 'data_mutation',
        targetTags: ['exercise', 'fitness'],
        condition: (event) => ['favorite_exercise', 'unfavorite_exercise'].includes(event.metadata?.action)
      },

      // 用户偏好规则
      {
        eventType: 'user_preference',
        targetTags: ['exercise', 'user'],
        delay: 1000 // 延迟1秒失效，避免频繁操作
      },

      // 会话变更规则
      {
        eventType: 'session_change',
        targetTags: ['user', 'feed', 'exercise'], // 清理所有用户相关缓存
        condition: (event) => event.metadata?.action === 'logout'
      }
    ]
  }

  /**
   * 发布缓存事件
   */
  async publishEvent(event: Omit<CacheEvent, 'timestamp'>): Promise<void> {
    const fullEvent: CacheEvent = {
      ...event,
      timestamp: Date.now()
    }

    this.eventQueue.push(fullEvent)
    
    console.log('[ServiceCacheAdapter] 缓存事件发布:', {
      type: fullEvent.type,
      source: fullEvent.source,
      target: fullEvent.target
    })

    // 异步处理事件队列
    if (!this.processingQueue) {
      this.processEventQueue()
    }
  }

  /**
   * 处理事件队列
   */
  private async processEventQueue(): Promise<void> {
    if (this.processingQueue || this.eventQueue.length === 0) {
      return
    }

    this.processingQueue = true

    try {
      while (this.eventQueue.length > 0) {
        const event = this.eventQueue.shift()!
        await this.handleCacheEvent(event)
      }
    } catch (error) {
      console.error('[ServiceCacheAdapter] 处理事件队列失败:', error)
    } finally {
      this.processingQueue = false
    }
  }

  /**
   * 处理单个缓存事件
   */
  private async handleCacheEvent(event: CacheEvent): Promise<void> {
    const matchingRules = this.invalidationRules.filter(rule => {
      if (rule.eventType !== event.type) return false
      if (rule.condition && !rule.condition(event)) return false
      return true
    })

    for (const rule of matchingRules) {
      try {
        if (rule.delay) {
          // 延迟失效
          setTimeout(async () => {
            if (!this.cacheManager) {
              await this.initializeCacheManager()
            }
            await this.cacheManager!.invalidate(rule.targetTags)
            console.log('[ServiceCacheAdapter] 延迟缓存失效:', {
              event: event.type,
              tags: rule.targetTags,
              delay: rule.delay
            })
          }, rule.delay)
        } else {
          // 立即失效
          if (!this.cacheManager) {
            await this.initializeCacheManager()
          }
          await this.cacheManager!.invalidate(rule.targetTags)
          console.log('[ServiceCacheAdapter] 缓存失效:', {
            event: event.type,
            tags: rule.targetTags
          })
        }
      } catch (error) {
        console.error('[ServiceCacheAdapter] 缓存失效失败:', error)
      }
    }
  }

  /**
   * 添加自定义失效规则
   */
  addInvalidationRule(rule: InvalidationRule): void {
    this.invalidationRules.push(rule)
    console.log('[ServiceCacheAdapter] 添加自定义失效规则:', rule)
  }

  /**
   * 清除所有缓存
   */
  async clearAllCache(): Promise<void> {
    if (!this.cacheManager) {
      await this.initializeCacheManager()
    }
    await this.cacheManager!.clear()
    console.log('[ServiceCacheAdapter] 所有缓存已清除')
  }

  /**
   * 获取缓存统计信息
   */
  async getCacheStats() {
    if (!this.cacheManager) {
      await this.initializeCacheManager()
    }
    return this.cacheManager!.getStats()
  }

  /**
   * 手动触发缓存清理
   */
  async cleanup(): Promise<void> {
    if (!this.cacheManager) {
      await this.initializeCacheManager()
    }
    await this.cacheManager!.cleanup()
    console.log('[ServiceCacheAdapter] 缓存清理完成')
  }

  // ===== 便捷方法 - 常用缓存操作 =====

  /**
   * 用户点赞操作
   */
  async handleUserLike(postId: string, liked: boolean): Promise<void> {
    await this.publishEvent({
      type: 'user_action',
      source: 'community',
      target: postId,
      metadata: { action: liked ? 'like' : 'unlike' }
    })
  }

  /**
   * 用户关注操作
   */
  async handleUserFollow(userId: string, following: boolean): Promise<void> {
    await this.publishEvent({
      type: 'user_action',
      source: 'community',
      target: userId,
      metadata: { action: following ? 'follow' : 'unfollow' }
    })
  }

  /**
   * 收藏动作操作
   */
  async handleExerciseFavorite(exerciseId: string, favorited: boolean): Promise<void> {
    await this.publishEvent({
      type: 'data_mutation',
      source: 'exercise',
      target: exerciseId,
      metadata: { action: favorited ? 'favorite_exercise' : 'unfavorite_exercise' }
    })
  }

  /**
   * 创建新帖子
   */
  async handlePostCreation(postId: string): Promise<void> {
    await this.publishEvent({
      type: 'data_mutation',
      source: 'community',
      target: postId,
      metadata: { action: 'create_post' }
    })
  }

  /**
   * 用户登出
   */
  async handleUserLogout(): Promise<void> {
    await this.publishEvent({
      type: 'session_change',
      source: 'auth',
      metadata: { action: 'logout' }
    })
  }

  /**
   * 智能预加载
   */
  async intelligentPreload(userBehavior: {
    recentlyViewedPosts?: string[]
    frequentExerciseTypes?: string[]
    activeHours?: number[]
  }): Promise<void> {
    try {
      console.log('[ServiceCacheAdapter] 开始智能预加载:', userBehavior)
      
      // 基于用户行为进行预加载
      // 这里可以调用各个服务的预加载方法
      
      console.log('[ServiceCacheAdapter] 智能预加载完成')
    } catch (error) {
      console.warn('[ServiceCacheAdapter] 智能预加载失败:', error)
    }
  }
}