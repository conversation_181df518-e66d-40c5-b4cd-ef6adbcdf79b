// 社区功能API服务 - 集成缓存系统
import { ApiFeedResponse, ApiFeedPost } from '../models/api';
import {
  FeedPost,

} from '../models/ui/feed/Post';
import {
  FeedUser,
  UserStats,
  FollowStatus
} from '../models/ui/feed/User';
import {
  CreatePostRequest,
  ShareWorkoutRequest
} from '../models/ui/common/Request';
import { WorkoutData } from '../models/ui/exercise/Workout';

// 服务层特定的类型定义
export interface FeedPostsResponse {
  posts: FeedPost[];
  total: number;
  page: number;
  limit: number;
  hasMore: boolean;
}

export interface FeedComment {
  id: string;
  post_id: string;
  user: FeedUser;
  content: string;
  likes: number;
  isLiked: boolean;
  created_at: string;
  replies?: FeedComment[];
}

export interface FeedNotification {
  id: string;
  type: 'like' | 'comment' | 'follow' | 'workout_share';
  from_user: FeedUser;
  post?: FeedPost;
  message: string;
  is_read: boolean;
  created_at: string;
}
import { apiClient } from './core/ApiClient';
import { authService } from './authService';
import { feedPostTransformer } from '../models/transformers';
import { CacheManager, CacheKeyBuilder } from './cache';
import GlobalCacheManager from './cache/GlobalCacheManager';

// 基础HTTP请求方法类型
type HttpMethod = 'GET' | 'POST' | 'PUT' | 'DELETE';

// 请求配置接口
interface RequestConfig {
  method: HttpMethod;
  headers?: Record<string, string>;
  body?: any;
}

// 使用ApiClient进行安全的请求处理
const request = async <T>(url: string, config: RequestConfig = { method: 'GET' }): Promise<T> => {
  const { method, headers = {}, body } = config;

  try {
    // 使用ApiClient进行请求，它已经包含了安全的响应数据处理
    const response = await apiClient.request<T>(url, {
      method: method as any,
      headers,
      data: body
    });

    return response;
  } catch (error) {
    console.error(`API request failed: ${method} ${url}`, error);
    throw error;
  }
};

// HTTP方法封装
const get = <T>(url: string, params?: Record<string, any>): Promise<T> => {
  const queryString = params ? '?' + new URLSearchParams(params).toString() : '';
  return request<T>(`${url}${queryString}`, { method: 'GET' });
};

const post = <T>(url: string, data?: any): Promise<T> => {
  return request<T>(url, { method: 'POST', body: data });
};

const put = <T>(url: string, data?: any): Promise<T> => {
  return request<T>(url, { method: 'PUT', body: data });
};



/**
 * 社区API服务类 - 集成智能缓存系统
 */
export class CommunityService {
  private static readonly BASE_PATH = '/api/v1/community';
  private static cacheManager: CacheManager | null = null;

  // 缓存配置常量
  private static readonly CACHE_CONFIG = {
    POSTS: {
      ttl: 5 * 60 * 1000,        // 5分钟
      tags: ['feed', 'posts'],
      priority: 'high' as const,
      persistent: true
    },
    POST_DETAIL: {
      ttl: 10 * 60 * 1000,       // 10分钟
      tags: ['feed', 'post-detail'],
      priority: 'medium' as const,
      persistent: true
    },
    COMMENTS: {
      ttl: 3 * 60 * 1000,        // 3分钟
      tags: ['feed', 'comments'],
      priority: 'medium' as const,
      persistent: true
    },
    USER_PROFILE: {
      ttl: 15 * 60 * 1000,       // 15分钟
      tags: ['user', 'profile'],
      priority: 'medium' as const,
      persistent: true
    },
    USER_STATS: {
      ttl: 5 * 60 * 1000,        // 5分钟
      tags: ['user', 'stats'],
      priority: 'low' as const,
      persistent: true
    }
  };

  /**
   * 获取缓存管理器实例（使用全局单例）
   */
  private static async getCacheManager(): Promise<CacheManager> {
    if (!this.cacheManager) {
      this.cacheManager = await GlobalCacheManager.getInstance();
      console.log('[CommunityService] 全局缓存管理器连接完成');
    }
    return this.cacheManager;
  }

  /**
   * 构建缓存键
   */
  private static buildCacheKey(resource: string, params?: Record<string, any>): string {
    return CacheKeyBuilder.create()
      .namespace('community')
      .resource(resource)
      .params(params || {})
      .version('1.0')
      .build();
  }

  // ===== 帖子相关 =====

  /**
   * 获取帖子列表（集成智能缓存系统）
   */
  static async getPosts(params: { skip?: number; limit?: number } = {}): Promise<FeedPostsResponse> {
    const { skip = 0, limit = 20 } = params;
    const cacheManager = await this.getCacheManager();
    const cacheKey = this.buildCacheKey('posts', { skip, limit });

    console.log('【社区服务】开始获取帖子列表:', { params, cacheKey });

    try {
      // 1. 尝试从缓存获取数据
      const cachedData = await cacheManager.get<FeedPostsResponse>(cacheKey);
      if (cachedData) {
        console.log('【社区服务】缓存命中，返回缓存数据:', {
          postsCount: cachedData.posts.length,
          cacheKey
        });
        return cachedData;
      }

      // 2. 缓存未命中，从API获取数据
      console.log('【社区服务】缓存未命中，从API获取数据');

      // 确保用户已认证
      if (!authService.isAuthenticated()) {
        console.log('【社区服务】用户未认证，开始登录');
        await authService.loginWithTestUser();
      }

      console.log('【社区服务】发起API请求:', {
        endpoint: `${this.BASE_PATH}/posts/`,
        params: { skip, limit }
      });

      // 使用新的ApiClient发起请求（iOS安全：确保参数为字符串）
      const apiResponse = await apiClient.get<ApiFeedResponse>(`${this.BASE_PATH}/posts/`, {
        skip: String(skip),
        limit: String(limit)
      });

      console.log('【社区服务】API响应:', {
        total: apiResponse.total,
        itemsCount: apiResponse.items?.length || 0
      });

      // 3. 验证和转换数据
      const validPosts: ApiFeedPost[] = [];
      const errors: string[] = [];

      if (apiResponse.items && Array.isArray(apiResponse.items)) {
        apiResponse.items.forEach((item, index) => {
          const validation = feedPostTransformer.validate(item) ? { isValid: true, errors: [] } : { isValid: false, errors: ['Validation failed'] };
          if (validation.isValid) {
            validPosts.push(item);
          } else {
            console.warn(`【社区服务】帖子 ${index} 数据验证失败:`, validation.errors);
            errors.push(...validation.errors);
          }
        });
      }

      // 转换为前端格式（异步处理）
      const transformedPosts = await Promise.all(
        validPosts.map(async (apiPost) => {
          try {
            return await feedPostTransformer.transform(apiPost);
          } catch (error) {
            console.error('【社区服务】帖子转换失败:', error);
            throw error;
          }
        })
      );

      // 4. 构建响应数据
      const response: FeedPostsResponse = {
        posts: transformedPosts,
        total: apiResponse.total,
        page: Math.floor(skip / limit) + 1,
        limit,
        hasMore: skip + limit < apiResponse.total
      };

      // 5. 缓存响应数据
      await cacheManager.set(cacheKey, response, this.CACHE_CONFIG.POSTS);

      console.log('【社区服务】数据获取和缓存完成:', {
        原始数据: apiResponse.items?.length || 0,
        有效数据: validPosts.length,
        转换成功: transformedPosts.length,
        验证错误: errors.length,
        已缓存: true
      });

      return response;

    } catch (error) {
      console.error('【社区服务】获取帖子列表失败:', {
        error: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined,
        params: { skip, limit },
        cacheKey,
        endpoint: `${this.BASE_PATH}/posts/`,
        timestamp: new Date().toISOString()
      });

      // 提供更友好的错误信息
      if (error instanceof Error) {
        // 检查是否是网络错误
        if (error.message.includes('fetch') || error.message.includes('network') || error.message.includes('NetworkError')) {
          throw new Error(`网络连接失败，请检查网络设置: ${error.message}`);
        }
        // 检查是否是认证错误
        if (error.message.includes('401') || error.message.includes('unauthorized')) {
          throw new Error(`认证失败，请重新登录: ${error.message}`);
        }
        // 检查是否是服务器错误
        if (error.message.includes('500') || error.message.includes('502') || error.message.includes('503')) {
          throw new Error(`服务器暂时不可用，请稍后重试: ${error.message}`);
        }
        throw new Error(`获取动态失败: ${error.message}`);
      }
      throw new Error('获取动态失败，请稍后重试');
    }
  }

  /**
   * 获取帖子详情（集成缓存）
   */
  static async getPostDetail(postId: string): Promise<FeedPost> {
    const cacheManager = await this.getCacheManager();
    const cacheKey = this.buildCacheKey('post-detail', { postId });

    try {
      // 1. 尝试从缓存获取
      const cachedPost = await cacheManager.get<FeedPost>(cacheKey);
      if (cachedPost) {
        console.log('【社区服务】帖子详情缓存命中:', { postId, cacheKey });
        return cachedPost;
      }

      // 2. 从API获取
      console.log('【社区服务】从API获取帖子详情:', { postId });
      const post = await get<FeedPost>(`${this.BASE_PATH}/posts/${postId}`);

      // 3. 缓存结果
      await cacheManager.set(cacheKey, post, this.CACHE_CONFIG.POST_DETAIL);

      return post;

    } catch (error) {
      console.error('【社区服务】获取帖子详情失败:', error);
      throw error;
    }
  }

  /**
   * 创建帖子
   */
  static async createPost(postData: CreatePostRequest): Promise<FeedPost> {
    return post<FeedPost>(`${this.BASE_PATH}/posts/`, postData);
  }

  /**
   * 更新帖子
   */
  static async updatePost(postId: string, postData: Partial<CreatePostRequest>): Promise<FeedPost> {
    return put<FeedPost>(`${this.BASE_PATH}/posts/${postId}`, postData);
  }

  /**
   * 点赞帖子切换（智能缓存失效）
   */
  static async togglePostLike(postId: string): Promise<{ liked: boolean; likes_count: number }> {
    try {
      const response = await post<{ liked: boolean; likes_count: number }>(`${this.BASE_PATH}/posts/${postId}/like/`);
      
      // 智能缓存失效策略
      const cacheManager = await this.getCacheManager();
      
      // 1. 失效相关的帖子列表缓存
      await cacheManager.invalidate(['feed', 'posts']);
      
      // 2. 失效特定帖子详情缓存
      const postDetailCacheKey = this.buildCacheKey('post-detail', { postId });
      await cacheManager.delete(postDetailCacheKey);
      
      console.log('【社区服务】点赞操作完成，已失效相关缓存:', {
        postId,
        liked: response.liked,
        likes_count: response.likes_count
      });

      return response;

    } catch (error) {
      console.error('【社区服务】点赞操作失败:', error);
      throw error;
    }
  }

  /**
   * 举报帖子
   */
  static async reportPost(postId: string, reason: string): Promise<{ success: boolean }> {
    return post<{ success: boolean }>(`${this.BASE_PATH}/posts/${postId}/report/`, { reason });
  }

  // ===== 评论相关 =====

  /**
   * 获取帖子的评论列表
   */
  static async getPostComments(
    postId: string,
    params: { skip?: number; limit?: number } = {}
  ): Promise<{ comments: FeedComment[]; total: number }> {
    const { skip = 0, limit = 20 } = params;
    return get<{ comments: FeedComment[]; total: number }>(
      `${this.BASE_PATH}/posts/${postId}/comments/`,
      { skip, limit }
    );
  }

  /**
   * 创建评论
   */
  static async createComment(postId: string, content: string): Promise<FeedComment> {
    try {
      return await post<FeedComment>(`${this.BASE_PATH}/posts/${postId}/comments/`, { content });
    } catch (error) {
      console.error('创建评论失败:', error);
      throw error;
    }
  }

  /**
   * 回复评论
   */
  static async replyComment(commentId: string, content: string): Promise<FeedComment> {
    try {
      return await post<FeedComment>(`${this.BASE_PATH}/comments/${commentId}/replies/`, { content });
    } catch (error) {
      console.error('回复评论失败:', error);
      throw error;
    }
  }

  /**
   * 点赞评论切换
   */
  static async toggleCommentLike(commentId: string): Promise<{ liked: boolean; likes_count: number }> {
    try {
      return await post<{ liked: boolean; likes_count: number }>(
        `${this.BASE_PATH}/comments/${commentId}/like/`
      );
    } catch (error) {
      console.error('切换评论点赞失败:', error);
      throw error;
    }
  }

  // ===== 通知相关 =====

  /**
   * 获取通知列表
   */
  static async getNotifications(
    params: { skip?: number; limit?: number } = {}
  ): Promise<{ notifications: FeedNotification[]; total: number }> {
    const { skip = 0, limit = 20 } = params;
    return get<{ notifications: FeedNotification[]; total: number }>(
      `${this.BASE_PATH}/notifications/`,
      { skip, limit }
    );
  }

  /**
   * 标记通知为已读
   */
  static async markNotificationAsRead(notificationId: string): Promise<{ success: boolean }> {
    return put<{ success: boolean }>(`${this.BASE_PATH}/notifications/${notificationId}/read/`);
  }

  /**
   * 标记所有通知为已读
   */
  static async markAllNotificationsAsRead(): Promise<{ success: boolean }> {
    return put<{ success: boolean }>(`${this.BASE_PATH}/notifications/read-all/`);
  }

  // ===== 用户相关 =====

  /**
   * 获取用户信息
   */
  static async getUserProfile(userId: string): Promise<any> {
    return get<any>(`${this.BASE_PATH}/users/${userId}`);
  }

  /**
   * 获取用户统计数据
   */
  static async getUserStats(userId: string): Promise<UserStats> {
    return get<UserStats>(`${this.BASE_PATH}/users/${userId}/stats`);
  }

  /**
   * 关注用户切换
   */
  static async toggleFollowUser(userId: string): Promise<{ following: boolean; followers_count: number }> {
    try {
      const response = await post<{ following: boolean; followers_count: number }>(
        `${this.BASE_PATH}/users/${userId}/follow/`
      );
      return response;
    } catch (error) {
      console.error('切换关注状态失败:', error);
      throw error;
    }
  }

  /**
   * 检查用户关注状态
   */
  static async checkFollowStatus(userId: string): Promise<FollowStatus> {
    try {
      return await get<FollowStatus>(`${this.BASE_PATH}/users/${userId}/follow-status/`);
    } catch (error) {
      console.error('检查关注状态失败:', error);
      throw error;
    }
  }

  // ===== 训练相关 =====

  /**
   * 获取训练详情
   */
  static async getWorkoutDetail(workoutId: string): Promise<WorkoutData> {
    return get<WorkoutData>(`${this.BASE_PATH}/daily-workouts/${workoutId}`);
  }

  /**
   * 创建训练记录
   */
  static async createWorkout(workoutData: Partial<WorkoutData>): Promise<WorkoutData> {
    return post<WorkoutData>(`${this.BASE_PATH}/daily-workouts/`, workoutData);
  }

  /**
   * 分享训练记录到社区
   */
  static async shareWorkout(workoutId: string, shareData: ShareWorkoutRequest): Promise<FeedPost> {
    try {
      const response = await post<FeedPost>(`${this.BASE_PATH}/workout/${workoutId}/share`, shareData);
      
      // 分享新内容后，失效帖子列表缓存
      const cacheManager = await this.getCacheManager();
      await cacheManager.invalidate(['feed', 'posts']);
      
      console.log('【社区服务】训练分享完成，已失效帖子列表缓存');
      return response;

    } catch (error) {
      console.error('【社区服务】分享训练失败:', error);
      throw error;
    }
  }

  // ===== 缓存管理工具方法 =====

  /**
   * 清除所有社区相关缓存
   */
  static async clearAllCache(): Promise<void> {
    const cacheManager = await this.getCacheManager();
    await cacheManager.clear('community:*');
    console.log('【社区服务】所有缓存已清除');
  }

  /**
   * 清除特定类型的缓存
   */
  static async clearCacheByType(type: 'posts' | 'comments' | 'users' | 'notifications'): Promise<void> {
    const cacheManager = await this.getCacheManager();
    
    switch (type) {
      case 'posts':
        await cacheManager.invalidate(['feed', 'posts', 'post-detail']);
        break;
      case 'comments':
        await cacheManager.invalidate(['feed', 'comments']);
        break;
      case 'users':
        await cacheManager.invalidate(['user']);
        break;
      case 'notifications':
        await cacheManager.invalidate(['notifications']);
        break;
    }
    
    console.log(`【社区服务】${type} 相关缓存已清除`);
  }

  /**
   * 获取缓存统计信息
   */
  static async getCacheStats() {
    const cacheManager = await this.getCacheManager();
    return cacheManager.getStats();
  }

  /**
   * 手动触发缓存清理
   */
  static async cleanupCache(): Promise<void> {
    const cacheManager = await this.getCacheManager();
    await cacheManager.cleanup();
    console.log('【社区服务】缓存清理完成');
  }

  /**
   * 预加载热门内容
   */
  static async preloadPopularContent(): Promise<void> {
    try {
      // 预加载第一页帖子
      await this.getPosts({ skip: 0, limit: 20 });
      console.log('【社区服务】热门内容预加载完成');
    } catch (error) {
      console.warn('【社区服务】预加载失败:', error);
    }
  }

  /**
   * 智能刷新策略 - 基于用户活动更新缓存
   */
  static async smartRefresh(userActivity: {
    viewedPosts?: string[]
    likedPosts?: string[]
    lastActiveTime?: number
  }): Promise<void> {
    const cacheManager = await this.getCacheManager();
    const now = Date.now();
    
    // 如果用户长时间不活跃，清除所有缓存强制刷新
    if (userActivity.lastActiveTime && now - userActivity.lastActiveTime > 30 * 60 * 1000) {
      await this.clearAllCache();
      return;
    }

    // 如果有点赞活动，刷新相关帖子
    if (userActivity.likedPosts && userActivity.likedPosts.length > 0) {
      for (const postId of userActivity.likedPosts) {
        const cacheKey = this.buildCacheKey('post-detail', { postId });
        await cacheManager.delete(cacheKey);
      }
    }

    console.log('【社区服务】智能刷新完成:', userActivity);
  }
}

// 导出默认实例
export const communityService = CommunityService;
