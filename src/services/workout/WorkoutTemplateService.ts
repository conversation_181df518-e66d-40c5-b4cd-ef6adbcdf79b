/**
 * 训练模板服务模块 - 训练模板管理
 * 提供完整的训练模板管理功能，包含iOS原生优化
 */

import { ApiClient } from '../core/ApiClient';
import { CacheManager, CacheKeyBuilder } from '../cache';
import GlobalCacheManager from '../cache/GlobalCacheManager';
import {
  WorkoutTemplate,
  WorkoutTemplateCreateParams,
  WorkoutTemplateApplyParams,
  WorkoutExercise,
  WorkoutOperationResult,
  iOSWorkoutFeedback
} from './types';

export class WorkoutTemplateService {
  private apiClient: ApiClient;
  private cacheManager: CacheManager | null = null;

  // 缓存配置
  private readonly CACHE_CONFIG = {
    TEMPLATES: {
      ttl: 12 * 60 * 60 * 1000,     // 12小时
      tags: ['workout-template', 'template'],
      priority: 'medium' as const,
      persistent: true
    },
    TEMPLATE_DETAIL: {
      ttl: 6 * 60 * 60 * 1000,      // 6小时
      tags: ['workout-template', 'detail'],
      priority: 'medium' as const,
      persistent: true
    },
    APPLIED_TEMPLATES: {
      ttl: 30 * 60 * 1000,          // 30分钟
      tags: ['workout-template', 'applied'],
      priority: 'high' as const,
      persistent: true
    }
  };

  constructor() {
    // 初始化API客户端
    this.apiClient = new ApiClient();

    // 异步初始化缓存管理器
    this.initializeCacheManager();

    console.log('[WorkoutTemplateService] 训练模板服务初始化完成');
  }

  /**
   * 异步初始化缓存管理器
   */
  private async initializeCacheManager(): Promise<void> {
    try {
      this.cacheManager = await GlobalCacheManager.getInstance();
      console.log('[WorkoutTemplateService] ✅ 全局缓存管理器连接成功');
    } catch (error) {
      console.error('[WorkoutTemplateService] ❌ 缓存管理器初始化失败:', error);
    }
  }

  /**
   * 获取缓存管理器（确保已初始化）
   */
  private async getCacheManager(): Promise<CacheManager> {
    if (!this.cacheManager) {
      this.cacheManager = await GlobalCacheManager.getInstance();
    }
    return this.cacheManager;
  }

  /**
   * 生成缓存键
   */
  private generateCacheKey(resource: string, params: any = {}): string {
    return CacheKeyBuilder.create()
      .namespace('workout-template')
      .resource(resource)
      .params(params)
      .version('2.0')
      .build();
  }

  /**
   * iOS触觉反馈
   */
  private async provideiOSFeedback(feedback: iOSWorkoutFeedback): Promise<void> {
    if (!this.apiClient.isNative || this.apiClient.platform !== 'ios') {
      return;
    }

    try {
      const { Haptics, ImpactStyle } = await import('@capacitor/haptics');
      
      switch (feedback.hapticStyle) {
        case 'light':
          await Haptics.impact({ style: ImpactStyle.Light });
          break;
        case 'medium':
          await Haptics.impact({ style: ImpactStyle.Medium });
          break;
        case 'heavy':
          await Haptics.impact({ style: ImpactStyle.Heavy });
          break;
      }

    } catch (error) {
      console.warn('⚠️ [WorkoutTemplateService] iOS反馈失败:', error);
    }
  }

  /**
   * 获取训练模板列表（带缓存）
   */
  public async getWorkoutTemplates(
    forceRefresh: boolean = false
  ): Promise<WorkoutOperationResult<WorkoutTemplate[]>> {
    const cacheKey = this.generateCacheKey('list');
    
    try {
      console.log('[WorkoutTemplateService] 获取训练模板列表:', { forceRefresh });

      // 检查缓存
      if (!forceRefresh) {
        const cacheManager = await this.getCacheManager();
        const cachedTemplates = await cacheManager.get<WorkoutTemplate[]>(cacheKey);
        if (cachedTemplates) {
          console.log('🎯 [WorkoutTemplateService] 模板列表缓存命中');
          return {
            success: true,
            data: cachedTemplates
          };
        }
      }

      // 从API获取
      const response = await this.apiClient.get<WorkoutTemplate[]>(
        '/api/v1/workout-templates'
      );

      // 缓存结果
      const cacheManager = await this.getCacheManager();
      await cacheManager.set(cacheKey, response, this.CACHE_CONFIG.TEMPLATES);

      console.log('✅ [WorkoutTemplateService] 训练模板列表获取成功:', {
        count: response.length
      });

      return {
        success: true,
        data: response
      };

    } catch (error) {
      console.error('❌ [WorkoutTemplateService] 获取训练模板列表失败:', error);
      return {
        success: false,
        error: {
          code: 'GET_TEMPLATES_FAILED',
          message: error instanceof Error ? error.message : '获取训练模板列表失败',
          retryable: true
        }
      };
    }
  }

  /**
   * 获取训练模板详情（带缓存）
   */
  public async getWorkoutTemplateDetail(
    templateId: string | number,
    forceRefresh: boolean = false
  ): Promise<WorkoutOperationResult<WorkoutTemplate>> {
    const cacheKey = this.generateCacheKey('detail', { templateId });
    
    try {
      console.log('[WorkoutTemplateService] 获取训练模板详情:', { templateId, forceRefresh });

      // 检查缓存
      if (!forceRefresh) {
        const cacheManager = await this.getCacheManager();
        const cachedDetail = await cacheManager.get<WorkoutTemplate>(cacheKey);
        if (cachedDetail) {
          console.log('🎯 [WorkoutTemplateService] 模板详情缓存命中');
          return {
            success: true,
            data: cachedDetail
          };
        }
      }

      // 从API获取
      const response = await this.apiClient.get<WorkoutTemplate>(
        `/api/v1/workout-templates/${templateId}`
      );

      // 缓存结果
      const cacheManager = await this.getCacheManager();
      await cacheManager.set(cacheKey, response, this.CACHE_CONFIG.TEMPLATE_DETAIL);

      console.log('✅ [WorkoutTemplateService] 训练模板详情获取成功');

      return {
        success: true,
        data: response
      };

    } catch (error) {
      console.error('❌ [WorkoutTemplateService] 获取训练模板详情失败:', error);
      return {
        success: false,
        error: {
          code: 'GET_TEMPLATE_DETAIL_FAILED',
          message: error instanceof Error ? error.message : '获取训练模板详情失败',
          retryable: true
        }
      };
    }
  }

  /**
   * 创建训练模板（iOS优化）
   */
  public async createWorkoutTemplate(
    params: WorkoutTemplateCreateParams
  ): Promise<WorkoutOperationResult<WorkoutTemplate>> {
    try {
      console.log('[WorkoutTemplateService] 创建训练模板:', { params });

      const response = await this.apiClient.post<WorkoutTemplate>(
        '/api/v1/workout-templates',
        params
      );

      // iOS触觉反馈 - 模板创建成功
      await this.provideiOSFeedback({
        hapticStyle: 'medium',
        notificationTitle: '模板创建成功',
        notificationBody: `"${params.name}" 模板已保存`
      });

      // 失效模板列表缓存
      await this.invalidateTemplateCache();

      console.log('✅ [WorkoutTemplateService] 训练模板创建成功');

      return {
        success: true,
        data: response
      };

    } catch (error) {
      console.error('❌ [WorkoutTemplateService] 创建训练模板失败:', error);
      return {
        success: false,
        error: {
          code: 'CREATE_TEMPLATE_FAILED',
          message: error instanceof Error ? error.message : '创建训练模板失败',
          retryable: true
        }
      };
    }
  }

  /**
   * 应用训练模板（iOS优化）
   */
  public async applyWorkoutTemplate(
    templateId: string | number,
    params: WorkoutTemplateApplyParams = {}
  ): Promise<WorkoutOperationResult<WorkoutExercise[]>> {
    const cacheKey = this.generateCacheKey('applied', { templateId, ...params });
    
    try {
      console.log('[WorkoutTemplateService] 应用训练模板:', { templateId, params });

      // 检查缓存
      const cacheManager = await this.getCacheManager();
      const cachedResult = await cacheManager.get<WorkoutExercise[]>(cacheKey);
      if (cachedResult) {
        console.log('🎯 [WorkoutTemplateService] 应用模板缓存命中');
        return {
          success: true,
          data: cachedResult
        };
      }

      // 调用API应用模板
      const response = await this.apiClient.post<WorkoutExercise[]>(
        `/api/v1/workout-templates/${templateId}/apply`,
        params
      );

      // iOS触觉反馈 - 模板应用成功
      await this.provideiOSFeedback({
        hapticStyle: 'light',
        notificationTitle: '模板应用成功',
        notificationBody: `已为您安排${response.length}个动作`
      });

      // 缓存应用结果
      const cacheManagerForApply = await this.getCacheManager();
      await cacheManagerForApply.set(cacheKey, response, this.CACHE_CONFIG.APPLIED_TEMPLATES);

      console.log('✅ [WorkoutTemplateService] 训练模板应用成功:', {
        exerciseCount: response.length
      });

      return {
        success: true,
        data: response
      };

    } catch (error) {
      console.error('❌ [WorkoutTemplateService] 应用训练模板失败:', error);
      return {
        success: false,
        error: {
          code: 'APPLY_TEMPLATE_FAILED',
          message: error instanceof Error ? error.message : '应用训练模板失败',
          retryable: true
        }
      };
    }
  }

  /**
   * 删除训练模板（iOS优化）
   */
  public async deleteWorkoutTemplate(
    templateId: string | number
  ): Promise<WorkoutOperationResult<void>> {
    try {
      console.log('[WorkoutTemplateService] 删除训练模板:', { templateId });

      await this.apiClient.delete(`/api/v1/workout-templates/${templateId}`);

      // iOS触觉反馈 - 模板删除
      await this.provideiOSFeedback({
        hapticStyle: 'medium',
        notificationTitle: '模板已删除',
        notificationBody: '训练模板删除成功'
      });

      // 失效相关缓存
      await this.invalidateTemplateCache(templateId.toString());

      console.log('✅ [WorkoutTemplateService] 训练模板删除成功');

      return {
        success: true
      };

    } catch (error) {
      console.error('❌ [WorkoutTemplateService] 删除训练模板失败:', error);
      return {
        success: false,
        error: {
          code: 'DELETE_TEMPLATE_FAILED',
          message: error instanceof Error ? error.message : '删除训练模板失败',
          retryable: true
        }
      };
    }
  }

  /**
   * 失效模板相关缓存
   */
  private async invalidateTemplateCache(templateId?: string): Promise<void> {
    const cacheManager = await this.getCacheManager();
    if (templateId) {
      const pattern = `workout-template:*templateId=${templateId}*`;
      await cacheManager.clear(pattern);
    } else {
      await cacheManager.invalidate(['workout-template', 'template']);
    }
    console.log('♻️ [WorkoutTemplateService] 训练模板缓存已失效');
  }

  /**
   * 获取缓存管理器实例
   */
  public async getCacheManagerInstance(): Promise<CacheManager> {
    return await this.getCacheManager();
  }
}

// 导出单例实例
export const workoutTemplateService = new WorkoutTemplateService();
export default workoutTemplateService;
