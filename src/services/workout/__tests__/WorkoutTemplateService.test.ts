/**
 * WorkoutTemplateService 集成测试
 * 测试训练模板服务的核心功能
 */

import { WorkoutTemplateService } from '../WorkoutTemplateService';
import { ApiClient } from '../../core/ApiClient';

// 模拟ApiClient
jest.mock('../../core/ApiClient');
const MockedApiClient = ApiClient as jest.MockedClass<typeof ApiClient>;

// 模拟Capacitor插件
jest.mock('@capacitor/haptics', () => ({
  Haptics: {
    impact: jest.fn()
  },
  ImpactStyle: {
    Light: 'LIGHT',
    Medium: 'MEDIUM',
    Heavy: 'HEAVY'
  }
}));

describe('WorkoutTemplateService 集成测试', () => {
  let workoutTemplateService: WorkoutTemplateService;
  let mockApiClient: jest.Mocked<ApiClient>;

  beforeEach(() => {
    jest.clearAllMocks();
    
    mockApiClient = {
      get: jest.fn(),
      post: jest.fn(),
      put: jest.fn(),
      delete: jest.fn(),
      isNative: true,
      platform: 'ios'
    } as any;

    MockedApiClient.mockImplementation(() => mockApiClient);
    workoutTemplateService = new WorkoutTemplateService();
  });

  describe('训练模板管理', () => {
    const mockTemplate = {
      id: 1,
      user_id: 1,
      name: '胸部训练模板',
      description: '专注胸部肌肉的训练模板',
      estimated_duration: 60,
      target_body_parts: [1, 2], // 胸部、肩部
      training_scenario: 'gym',
      notes: '适合中级训练者',
      visibility: 'private',
      exercise_count: 4,
      exercises: [
        {
          id: 1,
          exercise_id: 101,
          sets: [
            { set_number: 1, reps: 12, weight: 80, completed: false, workout_exercise_id: 1 },
            { set_number: 2, reps: 12, weight: 80, completed: false, workout_exercise_id: 1 },
            { set_number: 3, reps: 12, weight: 80, completed: false, workout_exercise_id: 1 }
          ],
          reps: 12,
          weight: 80,
          rest_seconds: 90
        }
      ],
      created_at: '2024-08-01T00:00:00Z',
      updated_at: '2024-08-01T00:00:00Z'
    };

    test('获取训练模板列表', async () => {
      mockApiClient.get.mockResolvedValue([mockTemplate]);

      const result = await workoutTemplateService.getWorkoutTemplates();

      expect(result.success).toBe(true);
      expect(result.data?.length).toBe(1);
      expect(result.data?.[0].name).toBe('胸部训练模板');
      expect(mockApiClient.get).toHaveBeenCalledWith('/api/v1/workout-templates');
    });

    test('获取训练模板详情', async () => {
      mockApiClient.get.mockResolvedValue(mockTemplate);

      const result = await workoutTemplateService.getWorkoutTemplateDetail(1);

      expect(result.success).toBe(true);
      expect(result.data?.id).toBe(1);
      expect(result.data?.name).toBe('胸部训练模板');
      expect(mockApiClient.get).toHaveBeenCalledWith('/api/v1/workout-templates/1');
    });

    test('创建训练模板', async () => {
      mockApiClient.post.mockResolvedValue(mockTemplate);

      const createParams = {
        name: '胸部训练模板',
        description: '专注胸部肌肉的训练模板',
        estimated_duration: 60,
        target_body_parts: [1, 2],
        training_scenario: 'gym' as const,
        visibility: 'private' as const,
        exercises: [
          {
            exercise_id: 101,
            exercise_name: '卧推',
            order: 1,
            sets: [
              { set_number: 1, reps: 12, weight: 80, completed: false, workout_exercise_id: 0 },
              { set_number: 2, reps: 12, weight: 80, completed: false, workout_exercise_id: 0 },
              { set_number: 3, reps: 12, weight: 80, completed: false, workout_exercise_id: 0 }
            ],
            reps: 12,
            weight: 80,
            rest_time: 90
          }
        ]
      };

      const result = await workoutTemplateService.createWorkoutTemplate(createParams);

      expect(result.success).toBe(true);
      expect(result.data?.name).toBe('胸部训练模板');
      expect(mockApiClient.post).toHaveBeenCalledWith(
        '/api/v1/workout-templates',
        createParams
      );
    });

    test('应用训练模板', async () => {
      const mockAppliedExercises = [
        {
          id: 1,
          exercise_id: 101,
          sets: [
            { set_number: 1, reps: 12, weight: 80, completed: false, workout_exercise_id: 1 },
            { set_number: 2, reps: 12, weight: 80, completed: false, workout_exercise_id: 1 },
            { set_number: 3, reps: 12, weight: 80, completed: false, workout_exercise_id: 1 }
          ],
          reps: 12,
          weight: 80,
          rest_seconds: 90,
          applied_date: '2024-08-01'
        }
      ];

      mockApiClient.post.mockResolvedValue(mockAppliedExercises);

      const result = await workoutTemplateService.applyWorkoutTemplate(1, {
        date: '2024-08-01'
      });

      expect(result.success).toBe(true);
      expect(result.data?.length).toBe(1);
      expect(mockApiClient.post).toHaveBeenCalledWith(
        '/api/v1/workout-templates/1/apply',
        { date: '2024-08-01' }
      );
    });

    // TODO: 实现updateWorkoutTemplate方法
    // test('更新训练模板', async () => {
    //   const updatedTemplate = {
    //     ...mockTemplate,
    //     name: '更新的胸部训练模板',
    //     description: '更新后的描述'
    //   };

    //   mockApiClient.put.mockResolvedValue(updatedTemplate);

    //   const updateParams = {
    //     name: '更新的胸部训练模板',
    //     description: '更新后的描述'
    //   };

    //   const result = await workoutTemplateService.updateWorkoutTemplate(1, updateParams);

    //   expect(result.success).toBe(true);
    //   expect(result.data?.name).toBe('更新的胸部训练模板');
    //   expect(mockApiClient.put).toHaveBeenCalledWith(
    //     '/api/v1/workout-templates/1',
    //     updateParams
    //   );
    // });

    test('删除训练模板', async () => {
      mockApiClient.delete.mockResolvedValue({ success: true });

      const result = await workoutTemplateService.deleteWorkoutTemplate(1);

      expect(result.success).toBe(true);
      expect(mockApiClient.delete).toHaveBeenCalledWith('/api/v1/workout-templates/1');
    });
  });

  describe('错误处理测试', () => {
    test('网络错误处理', async () => {
      mockApiClient.get.mockRejectedValue(new Error('Network Error'));

      const result = await workoutTemplateService.getWorkoutTemplates();

      expect(result.success).toBe(false);
      expect(result.error?.code).toBe('GET_TEMPLATES_FAILED');
      expect(result.error?.retryable).toBe(true);
    });

    test('API错误响应处理', async () => {
      mockApiClient.post.mockRejectedValue({
        status: 400,
        message: 'Invalid template data'
      });

      const result = await workoutTemplateService.createWorkoutTemplate({
        name: '',
        exercises: []
      });

      expect(result.success).toBe(false);
      expect(result.error?.code).toBe('CREATE_TEMPLATE_FAILED');
    });
  });

  describe('iOS原生功能测试', () => {
    test('创建模板触觉反馈', async () => {
      const { Haptics } = await import('@capacitor/haptics');
      mockApiClient.post.mockResolvedValue(mockTemplate);

      await workoutTemplateService.createWorkoutTemplate({
        name: '测试模板',
        exercises: []
      });

      expect(Haptics.impact).toHaveBeenCalled();
    });

    test('应用模板触觉反馈', async () => {
      const { Haptics } = await import('@capacitor/haptics');
      mockApiClient.post.mockResolvedValue([]);

      await workoutTemplateService.applyWorkoutTemplate(1);

      expect(Haptics.impact).toHaveBeenCalled();
    });
  });

  describe('缓存功能测试', () => {
    test('模板列表缓存', async () => {
      mockApiClient.get.mockResolvedValue([mockTemplate]);

      // 第一次调用
      await workoutTemplateService.getWorkoutTemplates();
      
      // 第二次调用应该使用缓存
      await workoutTemplateService.getWorkoutTemplates();

      // API应该只被调用一次
      expect(mockApiClient.get).toHaveBeenCalledTimes(1);
    });

    test('强制刷新绕过缓存', async () => {
      mockApiClient.get.mockResolvedValue([mockTemplate]);

      // 第一次调用
      await workoutTemplateService.getWorkoutTemplates();
      
      // 强制刷新
      await workoutTemplateService.getWorkoutTemplates(true);

      // API应该被调用两次
      expect(mockApiClient.get).toHaveBeenCalledTimes(2);
    });
  });
});

// 模拟模板数据
const mockTemplate = {
  id: 1,
  user_id: 1,
  name: '胸部训练模板',
  description: '专注胸部肌肉的训练模板',
  estimated_duration: 60,
  target_body_parts: [1, 2],
  training_scenario: 'gym' as const,
  notes: '适合中级训练者',
  visibility: 'private' as const,
  exercise_count: 4,
  exercises: [
    {
      id: 1,
      exercise_id: 101,
      sets: 3,
      reps: 12,
      weight: 80,
      rest_seconds: 90
    }
  ],
  created_at: '2024-08-01T00:00:00Z',
  updated_at: '2024-08-01T00:00:00Z'
};
