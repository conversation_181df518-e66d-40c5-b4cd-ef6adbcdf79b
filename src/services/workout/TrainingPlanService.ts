/**
 * 训练计划服务模块 - 训练计划管理
 * 提供完整的训练计划生命周期管理，包含iOS原生优化
 */

import { ApiClient } from '../core/ApiClient';
import { CacheManager, CacheKeyBuilder } from '../cache';
import GlobalCacheManager from '../cache/GlobalCacheManager';
import {
  TrainingPlanGenRequest,
  DailyWorkoutGenRequest,
  TrainingPlanBase,
  TrainingPlanDetail,
  TrainingPlanListParams,
  TrainingPlanPage,
  Workout,
  WorkoutOperationResult,
  iOSWorkoutFeedback
} from './types';

class TrainingPlanService {
  private apiClient: ApiClient;
  private cacheManager: CacheManager | null = null;
  private readonly pageSize = 20;

  // 缓存配置
  private readonly CACHE_CONFIG = {
    TRAINING_PLANS: {
      ttl: 24 * 60 * 60 * 1000,     // 24小时
      tags: ['training-plan', 'plan'],
      priority: 'high' as const,
      persistent: true
    },
    PLAN_DETAIL: {
      ttl: 12 * 60 * 60 * 1000,     // 12小时
      tags: ['training-plan', 'detail'],
      priority: 'high' as const,
      persistent: true
    },
    DAILY_WORKOUTS: {
      ttl: 6 * 60 * 60 * 1000,      // 6小时
      tags: ['training-plan', 'daily'],
      priority: 'medium' as const,
      persistent: true
    },
    GENERATED_PLANS: {
      ttl: 60 * 60 * 1000,          // 1小时
      tags: ['training-plan', 'generated'],
      priority: 'medium' as const,
      persistent: false
    }
  };

  constructor() {
    // 初始化API客户端
    this.apiClient = new ApiClient();

    // 异步初始化缓存管理器
    this.initializeCacheManager();

    console.log('[TrainingPlanService] 训练计划服务初始化完成');
  }

  /**
   * 异步初始化缓存管理器
   */
  private async initializeCacheManager(): Promise<void> {
    try {
      this.cacheManager = await GlobalCacheManager.getInstance();
      console.log('[TrainingPlanService] ✅ 全局缓存管理器连接成功');
    } catch (error) {
      console.error('[TrainingPlanService] ❌ 缓存管理器初始化失败:', error);
    }
  }

  /**
   * 获取缓存管理器（确保已初始化）
   */
  private async getCacheManager(): Promise<CacheManager> {
    if (!this.cacheManager) {
      this.cacheManager = await GlobalCacheManager.getInstance();
    }
    return this.cacheManager;
  }

  /**
   * 生成缓存键
   */
  private generateCacheKey(resource: string, params: any = {}): string {
    return CacheKeyBuilder.create()
      .namespace('training-plan')
      .resource(resource)
      .params(params)
      .version('2.0')
      .build();
  }

  /**
   * iOS触觉反馈
   */
  private async provideiOSFeedback(feedback: iOSWorkoutFeedback): Promise<void> {
    if (!this.apiClient.isNative || this.apiClient.platform !== 'ios') {
      return;
    }

    try {
      const { Haptics, ImpactStyle } = await import('@capacitor/haptics');
      
      switch (feedback.hapticStyle) {
        case 'light':
          await Haptics.impact({ style: ImpactStyle.Light });
          break;
        case 'medium':
          await Haptics.impact({ style: ImpactStyle.Medium });
          break;
        case 'heavy':
          await Haptics.impact({ style: ImpactStyle.Heavy });
          break;
      }

    } catch (error) {
      console.warn('⚠️ [TrainingPlanService] iOS反馈失败:', error);
    }
  }

  /**
   * AI生成训练计划（iOS优化）
   */
  public async generatePlan(
    params: TrainingPlanGenRequest
  ): Promise<WorkoutOperationResult<TrainingPlanDetail>> {
    const cacheKey = this.generateCacheKey('generated', params);
    
    try {
      console.log('[TrainingPlanService] AI生成训练计划:', { params });

      // 检查缓存（相同参数的生成结果）
      const cacheManager = await this.getCacheManager();
      const cachedPlan = await cacheManager.get<TrainingPlanDetail>(cacheKey);
      if (cachedPlan) {
        console.log('🎯 [TrainingPlanService] 生成计划缓存命中');
        return {
          success: true,
          data: cachedPlan
        };
      }

      // 调用AI生成API
      const response = await this.apiClient.post<TrainingPlanDetail>(
        '/api/v1/training-plan/generate',
        params
      );

      // iOS触觉反馈 - 计划生成完成
      await this.provideiOSFeedback({
        hapticStyle: 'medium',
        notificationTitle: '计划生成完成',
        notificationBody: `为您生成了${params.duration_weeks}周训练计划`
      });

      // 缓存生成结果
      const cacheManagerForSet = await this.getCacheManager();
      await cacheManagerForSet.set(cacheKey, response, this.CACHE_CONFIG.GENERATED_PLANS);

      console.log('✅ [TrainingPlanService] 训练计划生成成功');

      return {
        success: true,
        data: response
      };

    } catch (error) {
      console.error('❌ [TrainingPlanService] 生成训练计划失败:', error);
      return {
        success: false,
        error: {
          code: 'GENERATE_PLAN_FAILED',
          message: error instanceof Error ? error.message : '生成训练计划失败',
          retryable: true
        }
      };
    }
  }

  /**
   * 生成每日训练（iOS优化）
   */
  public async generateDailyWorkout(
    params: DailyWorkoutGenRequest
  ): Promise<WorkoutOperationResult<Workout>> {
    const cacheKey = this.generateCacheKey('daily', params);
    
    try {
      console.log('[TrainingPlanService] 生成每日训练:', { params });

      // 检查缓存
      const cacheManager = await this.getCacheManager();
      const cachedWorkout = await cacheManager.get<Workout>(cacheKey);
      if (cachedWorkout) {
        console.log('🎯 [TrainingPlanService] 每日训练缓存命中');
        return {
          success: true,
          data: cachedWorkout
        };
      }

      // 调用每日训练生成API
      const response = await this.apiClient.post<Workout>(
        '/api/v1/training-plan/daily',
        params
      );

      // iOS触觉反馈 - 每日训练生成
      await this.provideiOSFeedback({
        hapticStyle: 'light',
        notificationTitle: '今日训练',
        notificationBody: `为您安排了${params.available_time}分钟训练`
      });

      // 缓存结果
      const cacheManagerForDaily = await this.getCacheManager();
      await cacheManagerForDaily.set(cacheKey, response, this.CACHE_CONFIG.DAILY_WORKOUTS);

      console.log('✅ [TrainingPlanService] 每日训练生成成功');

      return {
        success: true,
        data: response
      };

    } catch (error) {
      console.error('❌ [TrainingPlanService] 生成每日训练失败:', error);
      return {
        success: false,
        error: {
          code: 'GENERATE_DAILY_WORKOUT_FAILED',
          message: error instanceof Error ? error.message : '生成每日训练失败',
          retryable: true
        }
      };
    }
  }

  /**
   * 获取用户训练计划列表（带缓存和分页）
   */
  public async getUserPlans(
    params: TrainingPlanListParams = {},
    forceRefresh: boolean = false
  ): Promise<WorkoutOperationResult<TrainingPlanPage>> {
    const cacheKey = this.generateCacheKey('user-plans', params);
    
    try {
      console.log('[TrainingPlanService] 获取用户训练计划:', { params, forceRefresh });

      // 检查缓存
      if (!forceRefresh) {
        const cacheManagerForPlans = await this.getCacheManager();
        const cachedPlans = await cacheManagerForPlans.get<TrainingPlanPage>(cacheKey);
        if (cachedPlans) {
          console.log('🎯 [TrainingPlanService] 用户计划缓存命中');
          return {
            success: true,
            data: cachedPlans
          };
        }
      }

      // 设置默认参数
      const queryParams = {
        skip: String(0),
        limit: String(this.pageSize),
        active_only: String(true),
        ...params
      };

      // 从API获取
      const plans = await this.apiClient.get<TrainingPlanBase[]>(
        '/api/v1/training-plan',
        { params: queryParams }
      );

      const response: TrainingPlanPage = {
        plans,
        hasMore: plans.length === Number(queryParams.limit),
        total: plans.length,
        currentPage: Math.floor(Number(queryParams.skip) / Number(queryParams.limit))
      };

      // 缓存结果
      const cacheManagerForUserPlans = await this.getCacheManager();
      await cacheManagerForUserPlans.set(cacheKey, response, this.CACHE_CONFIG.TRAINING_PLANS);

      console.log('✅ [TrainingPlanService] 用户训练计划获取成功:', {
        count: plans.length,
        hasMore: response.hasMore
      });

      return {
        success: true,
        data: response
      };

    } catch (error) {
      console.error('❌ [TrainingPlanService] 获取用户训练计划失败:', error);
      return {
        success: false,
        error: {
          code: 'GET_USER_PLANS_FAILED',
          message: error instanceof Error ? error.message : '获取用户训练计划失败',
          retryable: true
        }
      };
    }
  }

  /**
   * 获取训练计划详情（带缓存）
   */
  public async getPlanDetail(
    planId: string | number,
    forceRefresh: boolean = false
  ): Promise<WorkoutOperationResult<TrainingPlanDetail>> {
    const cacheKey = this.generateCacheKey('detail', { planId });
    
    try {
      console.log('[TrainingPlanService] 获取训练计划详情:', { planId, forceRefresh });

      // 检查缓存
      if (!forceRefresh) {
        const cacheManagerForDetail = await this.getCacheManager();
        const cachedDetail = await cacheManagerForDetail.get<TrainingPlanDetail>(cacheKey);
        if (cachedDetail) {
          console.log('🎯 [TrainingPlanService] 计划详情缓存命中');
          return {
            success: true,
            data: cachedDetail
          };
        }
      }

      // 从API获取
      const response = await this.apiClient.get<TrainingPlanDetail>(
        `/api/v1/training-plan/${planId}`
      );

      // 缓存结果
      const cacheManagerForPlanDetail = await this.getCacheManager();
      await cacheManagerForPlanDetail.set(cacheKey, response, this.CACHE_CONFIG.PLAN_DETAIL);

      console.log('✅ [TrainingPlanService] 训练计划详情获取成功');

      return {
        success: true,
        data: response
      };

    } catch (error) {
      console.error('❌ [TrainingPlanService] 获取训练计划详情失败:', error);
      return {
        success: false,
        error: {
          code: 'GET_PLAN_DETAIL_FAILED',
          message: error instanceof Error ? error.message : '获取训练计划详情失败',
          retryable: true
        }
      };
    }
  }

  /**
   * 失效训练计划相关缓存
   */
  private async invalidatePlanCache(planId?: string): Promise<void> {
    const cacheManager = await this.getCacheManager();
    if (planId) {
      const pattern = `training-plan:*planId=${planId}*`;
      await cacheManager.clear(pattern);
    } else {
      await cacheManager.invalidate(['training-plan', 'plan']);
    }
    console.log('♻️ [TrainingPlanService] 训练计划缓存已失效');
  }

  /**
   * 清除训练计划缓存
   */
  public async clearPlanCache(planId?: string): Promise<void> {
    await this.invalidatePlanCache(planId);
  }

  /**
   * 获取缓存管理器实例
   */
  public async getCacheManagerInstance(): Promise<CacheManager> {
    return await this.getCacheManager();
  }
}

// 导出单例实例
export const trainingPlanService = new TrainingPlanService();
export default trainingPlanService;
