/**
 * 训练服务模块统一导出
 * 提供完整的训练相关服务和类型定义
 */

// 导出类型定义
export * from './types';

// 导入服务实例
import { workoutService } from './WorkoutService';
import { trainingPlanService } from './TrainingPlanService';
import { workoutTemplateService } from './WorkoutTemplateService';

// 导出服务实例
export { workoutService } from './WorkoutService';
export { trainingPlanService } from './TrainingPlanService';
export { workoutTemplateService } from './WorkoutTemplateService';

// 便捷的统一服务对象
export const workoutServices = {
  workout: workoutService,
  trainingPlan: trainingPlanService,
  template: workoutTemplateService
};

// 默认导出主要服务
export default workoutServices;
