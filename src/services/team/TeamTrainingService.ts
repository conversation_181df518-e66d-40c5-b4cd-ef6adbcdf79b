/**
 * 团队训练服务模块
 * 提供完整的团队训练管理功能，包含模板创建、计划管理、会话跟踪等
 * 支持iOS原生优化和智能缓存策略
 */

import { ApiClient } from '../core/ApiClient';
import { CacheManager, CacheKeyBuilder } from '../cache';
import GlobalCacheManager from '../cache/GlobalCacheManager';
import {
  TeamTrainingTemplate,
  ClientTrainingPlan,
  TrainingSession,
  CreateTeamTemplateRequest,
  CreateClientPlanRequest,
  UpdateClientPlanRequest,
  RecordSessionSetRequest,
  CompleteSessionRequest,
  TeamTrainingSearchParams
} from '../../models/api/team/TeamTraining';

// 团队训练操作结果
export interface TeamTrainingOperationResult<T = any> {
  success: boolean;
  data?: T;
  error?: {
    code: string;
    message: string;
    retryable: boolean;
  };
}

// iOS反馈配置
export interface iOSTeamTrainingFeedback {
  hapticStyle?: 'light' | 'medium' | 'heavy';
  statusBarStyle?: 'light' | 'dark';
  notificationTitle?: string;
  notificationBody?: string;
}

/**
 * 团队训练服务类
 */
export class TeamTrainingService {
  private cacheManager: CacheManager | null = null;
  // private readonly pageSize = 20;
  
  // 缓存配置
  private readonly CACHE_CONFIG = {
    TEAM_TEMPLATES: { ttl: 30 * 60 * 1000, persistent: true }, // 30分钟
    CLIENT_PLANS: { ttl: 15 * 60 * 1000, persistent: true }, // 15分钟
    TRAINING_SESSIONS: { ttl: 10 * 60 * 1000, persistent: true }, // 10分钟
    TEAM_INFO: { ttl: 60 * 60 * 1000, persistent: true }, // 1小时
    TEAM_STATS: { ttl: 60 * 60 * 1000, persistent: true }, // 1小时
  };

  constructor(private apiClient: ApiClient) {}

  /**
   * 获取缓存管理器实例
   */
  private async getCacheManager(): Promise<CacheManager> {
    if (!this.cacheManager) {
      this.cacheManager = await GlobalCacheManager.getInstance();
    }
    return this.cacheManager;
  }

  /**
   * 生成缓存键
   */
  private generateCacheKey(type: string, params: any = {}): string {
    return CacheKeyBuilder.create()
      .namespace('team-training')
      .resource(type)
      .params(params)
      .build();
  }

  /**
   * 提供iOS原生反馈
   */
  private async provideiOSFeedback(config: iOSTeamTrainingFeedback): Promise<void> {
    try {
      if (!this.apiClient.isNative || this.apiClient.platform !== 'ios') {
        return;
      }

      // 触觉反馈
      if (config.hapticStyle) {
        const { Haptics, ImpactStyle } = await import('@capacitor/haptics');
        const style = config.hapticStyle === 'light' ? ImpactStyle.Light :
                     config.hapticStyle === 'medium' ? ImpactStyle.Medium : ImpactStyle.Heavy;
        await Haptics.impact({ style });
      }

      // 状态栏样式
      if (config.statusBarStyle) {
        const { StatusBar, Style } = await import('@capacitor/status-bar');
        const style = config.statusBarStyle === 'light' ? Style.Light : Style.Dark;
        await StatusBar.setStyle({ style });
      }

      // 本地通知
      if (config.notificationTitle && config.notificationBody) {
        const { LocalNotifications } = await import('@capacitor/local-notifications');
        await LocalNotifications.schedule({
          notifications: [{
            title: config.notificationTitle,
            body: config.notificationBody,
            id: Date.now(),
            schedule: { at: new Date(Date.now() + 100) }
          }]
        });
      }

    } catch (error) {
      console.warn('⚠️ [TeamTrainingService] iOS反馈失败:', error);
    }
  }

  /**
   * 创建团队训练模板
   */
  public async createTeamTemplate(
    request: CreateTeamTemplateRequest
  ): Promise<TeamTrainingOperationResult<TeamTrainingTemplate>> {
    try {
      console.log('[TeamTrainingService] 创建团队训练模板:', request);

      const response = await this.apiClient.post<TeamTrainingTemplate>(
        `/api/v1/teams/${request.team_id}/templates`,
        request
      );

      // iOS触觉反馈
      await this.provideiOSFeedback({
        hapticStyle: 'medium',
        notificationTitle: '训练模板已创建',
        notificationBody: `模板"${request.name}"创建成功`
      });

      // 失效相关缓存
      await this.invalidateTeamCache(request.team_id);

      console.log('✅ [TeamTrainingService] 团队训练模板创建成功');

      return {
        success: true,
        data: response
      };

    } catch (error) {
      console.error('❌ [TeamTrainingService] 创建团队训练模板失败:', error);
      return {
        success: false,
        error: {
          code: 'CREATE_TEAM_TEMPLATE_FAILED',
          message: error instanceof Error ? error.message : '创建团队训练模板失败',
          retryable: true
        }
      };
    }
  }

  /**
   * 获取团队训练模板列表
   */
  public async getTeamTemplates(
    teamId: number,
    forceRefresh: boolean = false
  ): Promise<TeamTrainingOperationResult<TeamTrainingTemplate[]>> {
    const cacheKey = this.generateCacheKey('templates', { teamId });
    
    try {
      console.log('[TeamTrainingService] 获取团队训练模板:', { teamId, forceRefresh });

      // 检查缓存
      if (!forceRefresh) {
        const cacheManager = await this.getCacheManager();
        const cachedTemplates = await cacheManager.get<TeamTrainingTemplate[]>(cacheKey);
        if (cachedTemplates) {
          console.log('🎯 [TeamTrainingService] 团队模板缓存命中');
          return {
            success: true,
            data: cachedTemplates
          };
        }
      }

      // 从API获取
      const response = await this.apiClient.get<TeamTrainingTemplate[]>(
        `/api/v1/teams/${teamId}/templates`
      );

      // 缓存结果
      const cacheManager = await this.getCacheManager();
      await cacheManager.set(cacheKey, response, this.CACHE_CONFIG.TEAM_TEMPLATES);

      console.log('✅ [TeamTrainingService] 团队训练模板获取成功');

      return {
        success: true,
        data: response
      };

    } catch (error) {
      console.error('❌ [TeamTrainingService] 获取团队训练模板失败:', error);
      return {
        success: false,
        error: {
          code: 'GET_TEAM_TEMPLATES_FAILED',
          message: error instanceof Error ? error.message : '获取团队训练模板失败',
          retryable: true
        }
      };
    }
  }

  /**
   * 创建客户训练计划
   */
  public async createClientPlan(
    request: CreateClientPlanRequest
  ): Promise<TeamTrainingOperationResult<ClientTrainingPlan>> {
    try {
      console.log('[TeamTrainingService] 创建客户训练计划:', request);

      const response = await this.apiClient.post<ClientTrainingPlan>(
        `/api/v1/clients/${request.client_relation_id}/training-plans`,
        request
      );

      // iOS触觉反馈
      await this.provideiOSFeedback({
        hapticStyle: 'medium',
        notificationTitle: '训练计划已创建',
        notificationBody: '客户训练计划创建成功'
      });

      // 失效相关缓存
      await this.invalidateClientCache(request.client_relation_id);

      console.log('✅ [TeamTrainingService] 客户训练计划创建成功');

      return {
        success: true,
        data: response
      };

    } catch (error) {
      console.error('❌ [TeamTrainingService] 创建客户训练计划失败:', error);
      return {
        success: false,
        error: {
          code: 'CREATE_CLIENT_PLAN_FAILED',
          message: error instanceof Error ? error.message : '创建客户训练计划失败',
          retryable: true
        }
      };
    }
  }

  /**
   * 获取客户训练计划列表
   */
  public async getClientPlans(
    clientRelationId: number,
    params: TeamTrainingSearchParams = {},
    forceRefresh: boolean = false
  ): Promise<TeamTrainingOperationResult<ClientTrainingPlan[]>> {
    const cacheKey = this.generateCacheKey('client-plans', { clientRelationId, params });
    
    try {
      console.log('[TeamTrainingService] 获取客户训练计划:', { clientRelationId, params, forceRefresh });

      // 检查缓存
      if (!forceRefresh) {
        const cacheManager = await this.getCacheManager();
        const cachedPlans = await cacheManager.get<ClientTrainingPlan[]>(cacheKey);
        if (cachedPlans) {
          console.log('🎯 [TeamTrainingService] 客户计划缓存命中');
          return {
            success: true,
            data: cachedPlans
          };
        }
      }

      // 构建查询参数（iOS安全：确保所有参数为字符串）
      const queryParams: any = {};
      if (params.status) queryParams.status = params.status;
      if (params.start_date) queryParams.start_date = params.start_date;
      if (params.end_date) queryParams.end_date = params.end_date;

      // 从API获取
      const response = await this.apiClient.get<ClientTrainingPlan[]>(
        `/api/v1/clients/${clientRelationId}/training-plans`,
        queryParams
      );

      // 缓存结果
      const cacheManager = await this.getCacheManager();
      await cacheManager.set(cacheKey, response, this.CACHE_CONFIG.CLIENT_PLANS);

      console.log('✅ [TeamTrainingService] 客户训练计划获取成功');

      return {
        success: true,
        data: response
      };

    } catch (error) {
      console.error('❌ [TeamTrainingService] 获取客户训练计划失败:', error);
      return {
        success: false,
        error: {
          code: 'GET_CLIENT_PLANS_FAILED',
          message: error instanceof Error ? error.message : '获取客户训练计划失败',
          retryable: true
        }
      };
    }
  }

  /**
   * 更新客户训练计划
   */
  public async updateClientPlan(
    planId: number,
    request: UpdateClientPlanRequest
  ): Promise<TeamTrainingOperationResult<ClientTrainingPlan>> {
    try {
      console.log('[TeamTrainingService] 更新客户训练计划:', { planId, request });

      const response = await this.apiClient.put<ClientTrainingPlan>(
        `/api/v1/clients/training-plans/${planId}`,
        request
      );

      // iOS触觉反馈
      await this.provideiOSFeedback({
        hapticStyle: 'light',
        notificationTitle: '训练计划已更新',
        notificationBody: '客户训练计划更新成功'
      });

      // 失效相关缓存
      await this.invalidatePlanCache(planId);

      console.log('✅ [TeamTrainingService] 客户训练计划更新成功');

      return {
        success: true,
        data: response
      };

    } catch (error) {
      console.error('❌ [TeamTrainingService] 更新客户训练计划失败:', error);
      return {
        success: false,
        error: {
          code: 'UPDATE_CLIENT_PLAN_FAILED',
          message: error instanceof Error ? error.message : '更新客户训练计划失败',
          retryable: true
        }
      };
    }
  }

  /**
   * 获取训练会话列表
   */
  public async getPlanSessions(
    planId: number,
    params: TeamTrainingSearchParams = {},
    forceRefresh: boolean = false
  ): Promise<TeamTrainingOperationResult<TrainingSession[]>> {
    const cacheKey = this.generateCacheKey('plan-sessions', { planId, params });

    try {
      console.log('[TeamTrainingService] 获取训练会话:', { planId, params, forceRefresh });

      // 检查缓存
      if (!forceRefresh) {
        const cacheManager = await this.getCacheManager();
        const cachedSessions = await cacheManager.get<TrainingSession[]>(cacheKey);
        if (cachedSessions) {
          console.log('🎯 [TeamTrainingService] 训练会话缓存命中');
          return {
            success: true,
            data: cachedSessions
          };
        }
      }

      // 构建查询参数
      const queryParams: any = {};
      if (params.status) queryParams.status = params.status;

      // 从API获取
      const response = await this.apiClient.get<TrainingSession[]>(
        `/api/v1/training-plans/${planId}/sessions`,
        queryParams
      );

      // 缓存结果
      const cacheManager = await this.getCacheManager();
      await cacheManager.set(cacheKey, response, this.CACHE_CONFIG.TRAINING_SESSIONS);

      console.log('✅ [TeamTrainingService] 训练会话获取成功');

      return {
        success: true,
        data: response
      };

    } catch (error) {
      console.error('❌ [TeamTrainingService] 获取训练会话失败:', error);
      return {
        success: false,
        error: {
          code: 'GET_PLAN_SESSIONS_FAILED',
          message: error instanceof Error ? error.message : '获取训练会话失败',
          retryable: true
        }
      };
    }
  }

  /**
   * 开始训练会话
   */
  public async startSession(
    sessionId: number
  ): Promise<TeamTrainingOperationResult<TrainingSession>> {
    try {
      console.log('[TeamTrainingService] 开始训练会话:', { sessionId });

      const response = await this.apiClient.post<TrainingSession>(
        `/api/v1/sessions/${sessionId}/start`
      );

      // iOS触觉反馈
      await this.provideiOSFeedback({
        hapticStyle: 'heavy',
        notificationTitle: '训练开始',
        notificationBody: '训练会话已开始，加油！'
      });

      // 失效相关缓存
      await this.invalidateSessionCache(sessionId);

      console.log('✅ [TeamTrainingService] 训练会话开始成功');

      return {
        success: true,
        data: response
      };

    } catch (error) {
      console.error('❌ [TeamTrainingService] 开始训练会话失败:', error);
      return {
        success: false,
        error: {
          code: 'START_SESSION_FAILED',
          message: error instanceof Error ? error.message : '开始训练会话失败',
          retryable: true
        }
      };
    }
  }

  /**
   * 记录训练组
   */
  public async recordSessionSet(
    sessionId: number,
    exerciseId: number,
    request: RecordSessionSetRequest
  ): Promise<TeamTrainingOperationResult<any>> {
    try {
      console.log('[TeamTrainingService] 记录训练组:', { sessionId, exerciseId, request });

      const response = await this.apiClient.post(
        `/api/v1/sessions/${sessionId}/exercises/${exerciseId}/records`,
        request
      );

      // iOS触觉反馈
      await this.provideiOSFeedback({
        hapticStyle: 'light',
        notificationTitle: '训练记录已保存',
        notificationBody: `${request.weight}kg × ${request.reps}次`
      });

      // 失效相关缓存
      await this.invalidateSessionCache(sessionId);

      console.log('✅ [TeamTrainingService] 训练组记录成功');

      return {
        success: true,
        data: response
      };

    } catch (error) {
      console.error('❌ [TeamTrainingService] 记录训练组失败:', error);
      return {
        success: false,
        error: {
          code: 'RECORD_SESSION_SET_FAILED',
          message: error instanceof Error ? error.message : '记录训练组失败',
          retryable: true
        }
      };
    }
  }

  /**
   * 完成训练会话
   */
  public async completeSession(
    sessionId: number,
    request: CompleteSessionRequest = {}
  ): Promise<TeamTrainingOperationResult<TrainingSession>> {
    try {
      console.log('[TeamTrainingService] 完成训练会话:', { sessionId, request });

      const response = await this.apiClient.put<TrainingSession>(
        `/api/v1/sessions/${sessionId}/complete`,
        request
      );

      // iOS触觉反馈
      await this.provideiOSFeedback({
        hapticStyle: 'heavy',
        notificationTitle: '训练完成',
        notificationBody: '恭喜完成训练！'
      });

      // 失效相关缓存
      await this.invalidateSessionCache(sessionId);

      console.log('✅ [TeamTrainingService] 训练会话完成成功');

      return {
        success: true,
        data: response
      };

    } catch (error) {
      console.error('❌ [TeamTrainingService] 完成训练会话失败:', error);
      return {
        success: false,
        error: {
          code: 'COMPLETE_SESSION_FAILED',
          message: error instanceof Error ? error.message : '完成训练会话失败',
          retryable: true
        }
      };
    }
  }

  /**
   * 失效团队相关缓存
   */
  private async invalidateTeamCache(teamId: number): Promise<void> {
    try {
      const cacheManager = await this.getCacheManager();
      await cacheManager.invalidate([`team-training:templates:${teamId}`]);
      console.log('🗑️ [TeamTrainingService] 团队缓存失效完成');
    } catch (error) {
      console.warn('⚠️ [TeamTrainingService] 缓存失效失败:', error);
    }
  }

  /**
   * 失效客户相关缓存
   */
  private async invalidateClientCache(clientRelationId: number): Promise<void> {
    try {
      const cacheManager = await this.getCacheManager();
      await cacheManager.invalidate([`team-training:client-plans:${clientRelationId}`]);
      console.log('🗑️ [TeamTrainingService] 客户缓存失效完成');
    } catch (error) {
      console.warn('⚠️ [TeamTrainingService] 缓存失效失败:', error);
    }
  }

  /**
   * 失效计划相关缓存
   */
  private async invalidatePlanCache(planId: number): Promise<void> {
    try {
      const cacheManager = await this.getCacheManager();
      await cacheManager.invalidate([`team-training:plan-sessions:${planId}`]);
      console.log('🗑️ [TeamTrainingService] 计划缓存失效完成');
    } catch (error) {
      console.warn('⚠️ [TeamTrainingService] 缓存失效失败:', error);
    }
  }

  /**
   * 失效会话相关缓存
   */
  private async invalidateSessionCache(sessionId: number): Promise<void> {
    try {
      const cacheManager = await this.getCacheManager();
      await cacheManager.invalidate([`team-training:sessions:${sessionId}`]);
      console.log('🗑️ [TeamTrainingService] 会话缓存失效完成');
    } catch (error) {
      console.warn('⚠️ [TeamTrainingService] 缓存失效失败:', error);
    }
  }

  /**
   * 清除所有团队训练缓存
   */
  public async clearAllCache(): Promise<void> {
    try {
      const cacheManager = await this.getCacheManager();
      await cacheManager.clear('team-training:*');
      console.log('🗑️ [TeamTrainingService] 所有团队训练缓存已清除');
    } catch (error) {
      console.warn('⚠️ [TeamTrainingService] 清除缓存失败:', error);
    }
  }
}

// 导出默认实例
export const teamTrainingService = new TeamTrainingService(new (await import('../core/ApiClient')).ApiClient());
