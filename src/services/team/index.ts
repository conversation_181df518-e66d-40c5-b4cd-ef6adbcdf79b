/**
 * 团队训练服务模块统一导出
 */

// 团队训练服务
export * from './TeamTrainingService';
export { teamTrainingService } from './TeamTrainingService';

// 团队训练配置
export interface TeamTrainingServiceConfig {
  enableCache: boolean;
  cacheTimeout: number;
  enableiOSFeedback: boolean;
  enableRealTimeSync: boolean;
  maxSessionDuration: number;
}

// 默认配置
export const DEFAULT_TEAM_TRAINING_CONFIG: TeamTrainingServiceConfig = {
  enableCache: true,
  cacheTimeout: 15 * 60 * 1000, // 15分钟
  enableiOSFeedback: true,
  enableRealTimeSync: true,
  maxSessionDuration: 180 * 60 * 1000 // 3小时
};

// 团队训练服务管理器
export class TeamTrainingServiceManager {
  private static instance: TeamTrainingServiceManager;
  private config: TeamTrainingServiceConfig;

  private constructor(config: TeamTrainingServiceConfig = DEFAULT_TEAM_TRAINING_CONFIG) {
    this.config = config;
  }

  public static getInstance(config?: TeamTrainingServiceConfig): TeamTrainingServiceManager {
    if (!TeamTrainingServiceManager.instance) {
      TeamTrainingServiceManager.instance = new TeamTrainingServiceManager(config);
    }
    return TeamTrainingServiceManager.instance;
  }

  public getConfig(): TeamTrainingServiceConfig {
    return this.config;
  }

  public updateConfig(newConfig: Partial<TeamTrainingServiceConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }

  /**
   * 清除所有团队训练缓存
   */
  public async clearAllCaches(): Promise<void> {
    try {
      const { teamTrainingService } = await import('./TeamTrainingService');
      await teamTrainingService.clearAllCache();
      console.log('🗑️ [TeamTrainingServiceManager] 所有团队训练缓存已清除');
    } catch (error) {
      console.warn('⚠️ [TeamTrainingServiceManager] 清除缓存失败:', error);
    }
  }
}

// 导出默认管理器实例
export const teamTrainingServiceManager = TeamTrainingServiceManager.getInstance();
