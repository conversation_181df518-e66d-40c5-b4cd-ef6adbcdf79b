/**
 * AI服务模块统一导出
 */

// 简化AI聊天服务
export * from './SimpleChatService';
export { simpleChatService } from './SimpleChatService';

// AI服务配置
export interface AIServiceConfig {
  enableCache: boolean;
  cacheTimeout: number;
  enableiOSFeedback: boolean;
  enableOfflineMode: boolean;
  maxMessageLength: number;
  maxConversations: number;
  autoRetryOfflineMessages: boolean;
}

// 默认配置
export const DEFAULT_AI_CONFIG: AIServiceConfig = {
  enableCache: true,
  cacheTimeout: 60 * 60 * 1000, // 1小时
  enableiOSFeedback: true,
  enableOfflineMode: true,
  maxMessageLength: 1000,
  maxConversations: 100,
  autoRetryOfflineMessages: true
};

// AI服务管理器
export class AIServiceManager {
  private static instance: AIServiceManager;
  private config: AIServiceConfig;
  private retryInterval: NodeJS.Timeout | null = null;

  private constructor(config: AIServiceConfig = DEFAULT_AI_CONFIG) {
    this.config = config;
    this.setupOfflineRetry();
  }

  public static getInstance(config?: AIServiceConfig): AIServiceManager {
    if (!AIServiceManager.instance) {
      AIServiceManager.instance = new AIServiceManager(config);
    }
    return AIServiceManager.instance;
  }

  public getConfig(): AIServiceConfig {
    return this.config;
  }

  public updateConfig(newConfig: Partial<AIServiceConfig>): void {
    this.config = { ...this.config, ...newConfig };
    
    // 重新设置离线重试
    if (newConfig.autoRetryOfflineMessages !== undefined) {
      this.setupOfflineRetry();
    }
  }

  /**
   * 设置离线消息自动重试
   */
  private setupOfflineRetry(): void {
    if (this.retryInterval) {
      clearInterval(this.retryInterval);
      this.retryInterval = null;
    }

    if (this.config.autoRetryOfflineMessages) {
      this.retryInterval = setInterval(async () => {
        if (navigator.onLine) {
          const { simpleChatService } = await import('./SimpleChatService');
          await simpleChatService.retryOfflineMessages();
        }
      }, 30000); // 每30秒检查一次
    }
  }

  /**
   * 获取离线消息状态
   */
  public async getOfflineStatus(): Promise<{
    isOnline: boolean;
    offlineMessageCount: number;
  }> {
    const { simpleChatService } = await import('./SimpleChatService');
    return {
      isOnline: navigator.onLine,
      offlineMessageCount: simpleChatService.getOfflineMessageCount()
    };
  }

  /**
   * 手动重试离线消息
   */
  public async retryOfflineMessages(): Promise<void> {
    const { simpleChatService } = await import('./SimpleChatService');
    await simpleChatService.retryOfflineMessages();
  }

  /**
   * 清除所有AI缓存
   */
  public async clearAllCaches(): Promise<void> {
    try {
      const { simpleChatService } = await import('./SimpleChatService');
      await simpleChatService.clearAllCache();
      console.log('🗑️ [AIServiceManager] 所有AI缓存已清除');
    } catch (error) {
      console.warn('⚠️ [AIServiceManager] 清除缓存失败:', error);
    }
  }

  /**
   * 销毁管理器
   */
  public destroy(): void {
    if (this.retryInterval) {
      clearInterval(this.retryInterval);
      this.retryInterval = null;
    }
  }
}

// 导出默认管理器实例
export const aiServiceManager = AIServiceManager.getInstance();
