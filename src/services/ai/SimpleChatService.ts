/**
 * 简化AI聊天服务模块
 * 提供基础的信息接收、展示和缓存功能
 * 支持iOS原生优化和离线模式
 */

import { ApiClient } from '../core/ApiClient';
import { CacheManager, CacheKeyBuilder } from '../cache';
import GlobalCacheManager from '../cache/GlobalCacheManager';
import {
  SimpleChatMessage,
  SimpleConversation,
  QuickReply,
  ChatStats,
  SendMessageRequest,
  SendMessageResponse,
  ConversationSearchParams,
  MessageSearchParams,
  ChatCacheConfig,
  OfflineMessage,
  SimpleChatConfig
} from '../../models/api/ai/SimpleChat';

// 聊天操作结果
export interface ChatOperationResult<T = any> {
  success: boolean;
  data?: T;
  error?: {
    code: string;
    message: string;
    retryable: boolean;
  };
}

// 分页结果
export interface ChatPage<T> {
  items: T[];
  hasMore: boolean;
  total: number;
  currentPage: number;
}

// iOS反馈配置
export interface iOSChatFeedback {
  hapticStyle?: 'light' | 'medium' | 'heavy';
  soundEnabled?: boolean;
  notificationTitle?: string;
  notificationBody?: string;
}

/**
 * 简化AI聊天服务类
 */
export class SimpleChatService {
  private cacheManager: CacheManager | null = null;
  private offlineMessages: OfflineMessage[] = [];
  private readonly pageSize = 50;
  
  // 缓存配置
  private readonly CACHE_CONFIG: ChatCacheConfig = {
    conversations_ttl: 24 * 60 * 60 * 1000, // 24小时
    messages_ttl: 60 * 60 * 1000, // 1小时
    preset_replies_ttl: 7 * 24 * 60 * 60 * 1000, // 7天
    quick_replies_ttl: 24 * 60 * 60 * 1000, // 24小时
    enable_offline_mode: true
  };

  // 聊天配置
  private readonly CHAT_CONFIG: SimpleChatConfig = {
    max_message_length: 1000,
    max_conversations: 100,
    enable_quick_replies: true,
    enable_preset_responses: true,
    auto_delete_old_conversations: true,
    conversation_retention_days: 30
  };

  constructor(private apiClient: ApiClient) {
    this.loadOfflineMessages();
  }

  /**
   * 获取缓存管理器实例
   */
  private async getCacheManager(): Promise<CacheManager> {
    if (!this.cacheManager) {
      this.cacheManager = await GlobalCacheManager.getInstance();
    }
    return this.cacheManager;
  }

  /**
   * 生成缓存键
   */
  private generateCacheKey(type: string, params: any = {}): string {
    return CacheKeyBuilder.create()
      .namespace('simple-chat')
      .resource(type)
      .params(params)
      .build();
  }

  /**
   * 提供iOS原生反馈
   */
  private async provideiOSFeedback(config: iOSChatFeedback): Promise<void> {
    try {
      if (!this.apiClient.isNative || this.apiClient.platform !== 'ios') {
        return;
      }

      // 触觉反馈
      if (config.hapticStyle) {
        const { Haptics, ImpactStyle } = await import('@capacitor/haptics');
        const style = config.hapticStyle === 'light' ? ImpactStyle.Light :
                     config.hapticStyle === 'medium' ? ImpactStyle.Medium : ImpactStyle.Heavy;
        await Haptics.impact({ style });
      }

      // 声音反馈
      if (config.soundEnabled) {
        // 可以添加声音播放逻辑
        console.log('🔊 [SimpleChatService] 播放消息提示音');
      }

      // 本地通知
      if (config.notificationTitle && config.notificationBody) {
        const { LocalNotifications } = await import('@capacitor/local-notifications');
        await LocalNotifications.schedule({
          notifications: [{
            title: config.notificationTitle,
            body: config.notificationBody,
            id: Date.now(),
            schedule: { at: new Date(Date.now() + 100) }
          }]
        });
      }

    } catch (error) {
      console.warn('⚠️ [SimpleChatService] iOS反馈失败:', error);
    }
  }

  /**
   * 发送消息
   */
  public async sendMessage(
    request: SendMessageRequest
  ): Promise<ChatOperationResult<SendMessageResponse>> {
    try {
      console.log('[SimpleChatService] 发送消息:', request);

      // 验证消息长度
      if (request.content.length > this.CHAT_CONFIG.max_message_length) {
        return {
          success: false,
          error: {
            code: 'MESSAGE_TOO_LONG',
            message: `消息长度不能超过${this.CHAT_CONFIG.max_message_length}字符`,
            retryable: false
          }
        };
      }

      // 检查网络连接，如果离线则保存到离线队列
      if (!navigator.onLine && this.CACHE_CONFIG.enable_offline_mode) {
        return await this.handleOfflineMessage(request);
      }

      // 发送API请求
      const response = await this.apiClient.post<SendMessageResponse>(
        '/api/v1/chat/simple/send',
        request
      );

      // iOS触觉反馈
      await this.provideiOSFeedback({
        hapticStyle: 'light',
        soundEnabled: true
      });

      // 缓存对话和消息
      await this.cacheConversationData(response);

      console.log('✅ [SimpleChatService] 消息发送成功');

      return {
        success: true,
        data: response
      };

    } catch (error) {
      console.error('❌ [SimpleChatService] 发送消息失败:', error);
      
      // 如果启用离线模式，保存到离线队列
      if (this.CACHE_CONFIG.enable_offline_mode) {
        await this.handleOfflineMessage(request);
      }

      return {
        success: false,
        error: {
          code: 'SEND_MESSAGE_FAILED',
          message: error instanceof Error ? error.message : '发送消息失败',
          retryable: true
        }
      };
    }
  }

  /**
   * 获取对话列表
   */
  public async getConversations(
    params: ConversationSearchParams = {},
    page: number = 0,
    forceRefresh: boolean = false
  ): Promise<ChatOperationResult<ChatPage<SimpleConversation>>> {
    const cacheKey = this.generateCacheKey('conversations', { params, page });
    
    try {
      console.log('[SimpleChatService] 获取对话列表:', { params, page, forceRefresh });

      // 检查缓存
      if (!forceRefresh) {
        const cacheManager = await this.getCacheManager();
        const cachedConversations = await cacheManager.get<ChatPage<SimpleConversation>>(cacheKey);
        if (cachedConversations) {
          console.log('🎯 [SimpleChatService] 对话列表缓存命中');
          return {
            success: true,
            data: cachedConversations
          };
        }
      }

      // 构建查询参数（iOS安全：确保所有参数为字符串）
      const queryParams: any = {
        skip: String(page * this.pageSize),
        limit: String(this.pageSize),
        ...params
      };

      // 确保布尔参数转换为字符串
      if (params.is_active !== undefined) queryParams.is_active = String(params.is_active);

      // 发送API请求
      const response = await this.apiClient.get<SimpleConversation[]>(
        '/api/v1/chat/simple/conversations',
        queryParams
      );

      // 构建分页结果
      const result: ChatPage<SimpleConversation> = {
        items: response,
        hasMore: response.length === this.pageSize,
        total: response.length,
        currentPage: page
      };

      // 缓存结果
      const cacheManager = await this.getCacheManager();
      await cacheManager.set(cacheKey, result, { ttl: this.CACHE_CONFIG.conversations_ttl, persistent: true });

      console.log('✅ [SimpleChatService] 对话列表获取成功');

      return {
        success: true,
        data: result
      };

    } catch (error) {
      console.error('❌ [SimpleChatService] 获取对话列表失败:', error);
      return {
        success: false,
        error: {
          code: 'GET_CONVERSATIONS_FAILED',
          message: error instanceof Error ? error.message : '获取对话列表失败',
          retryable: true
        }
      };
    }
  }

  /**
   * 获取对话消息
   */
  public async getConversationMessages(
    conversationId: string,
    params: MessageSearchParams = {},
    page: number = 0,
    forceRefresh: boolean = false
  ): Promise<ChatOperationResult<ChatPage<SimpleChatMessage>>> {
    const cacheKey = this.generateCacheKey('messages', { conversationId, params, page });
    
    try {
      console.log('[SimpleChatService] 获取对话消息:', { conversationId, params, page, forceRefresh });

      // 检查缓存
      if (!forceRefresh) {
        const cacheManager = await this.getCacheManager();
        const cachedMessages = await cacheManager.get<ChatPage<SimpleChatMessage>>(cacheKey);
        if (cachedMessages) {
          console.log('🎯 [SimpleChatService] 对话消息缓存命中');
          return {
            success: true,
            data: cachedMessages
          };
        }
      }

      // 构建查询参数
      const queryParams: any = {
        skip: String(page * this.pageSize),
        limit: String(this.pageSize),
        ...params
      };

      // 发送API请求
      const response = await this.apiClient.get<SimpleChatMessage[]>(
        `/api/v1/chat/simple/conversations/${conversationId}/messages`,
        queryParams
      );

      // 构建分页结果
      const result: ChatPage<SimpleChatMessage> = {
        items: response,
        hasMore: response.length === this.pageSize,
        total: response.length,
        currentPage: page
      };

      // 缓存结果
      const cacheManager = await this.getCacheManager();
      await cacheManager.set(cacheKey, result, { ttl: this.CACHE_CONFIG.messages_ttl, persistent: true });

      console.log('✅ [SimpleChatService] 对话消息获取成功');

      return {
        success: true,
        data: result
      };

    } catch (error) {
      console.error('❌ [SimpleChatService] 获取对话消息失败:', error);
      return {
        success: false,
        error: {
          code: 'GET_MESSAGES_FAILED',
          message: error instanceof Error ? error.message : '获取对话消息失败',
          retryable: true
        }
      };
    }
  }

  /**
   * 获取快速回复建议
   */
  public async getQuickReplies(
    category?: string,
    forceRefresh: boolean = false
  ): Promise<ChatOperationResult<QuickReply[]>> {
    const cacheKey = this.generateCacheKey('quick-replies', { category });

    try {
      console.log('[SimpleChatService] 获取快速回复:', { category, forceRefresh });

      // 检查缓存
      if (!forceRefresh) {
        const cacheManager = await this.getCacheManager();
        const cachedReplies = await cacheManager.get<QuickReply[]>(cacheKey);
        if (cachedReplies) {
          console.log('🎯 [SimpleChatService] 快速回复缓存命中');
          return {
            success: true,
            data: cachedReplies
          };
        }
      }

      // 构建查询参数
      const queryParams: any = {};
      if (category) queryParams.category = category;

      // 发送API请求
      const response = await this.apiClient.get<QuickReply[]>(
        '/api/v1/chat/simple/quick-replies',
        queryParams
      );

      // 缓存结果
      const cacheManager = await this.getCacheManager();
      await cacheManager.set(cacheKey, response, { ttl: this.CACHE_CONFIG.quick_replies_ttl, persistent: true });

      console.log('✅ [SimpleChatService] 快速回复获取成功');

      return {
        success: true,
        data: response
      };

    } catch (error) {
      console.error('❌ [SimpleChatService] 获取快速回复失败:', error);
      return {
        success: false,
        error: {
          code: 'GET_QUICK_REPLIES_FAILED',
          message: error instanceof Error ? error.message : '获取快速回复失败',
          retryable: true
        }
      };
    }
  }

  /**
   * 处理离线消息
   */
  private async handleOfflineMessage(
    request: SendMessageRequest
  ): Promise<ChatOperationResult<SendMessageResponse>> {
    try {
      const offlineMessage: OfflineMessage = {
        id: Date.now().toString(),
        content: request.content,
        timestamp: new Date().toISOString(),
        retry_count: 0,
        max_retries: 3
      };

      this.offlineMessages.push(offlineMessage);
      await this.saveOfflineMessages();

      // 创建模拟响应
      const mockResponse: SendMessageResponse = {
        user_message: {
          id: offlineMessage.id,
          conversation_id: request.conversation_id || 'offline',
          role: 'user',
          content: request.content,
          timestamp: offlineMessage.timestamp,
          status: 'sending',
          metadata: { cached: true, source: 'offline' }
        },
        ai_response: {
          id: `ai-${offlineMessage.id}`,
          conversation_id: request.conversation_id || 'offline',
          role: 'assistant',
          content: '您的消息已保存，将在网络恢复后发送。',
          timestamp: new Date().toISOString(),
          status: 'delivered',
          metadata: { cached: true, source: 'offline' }
        },
        conversation: {
          id: request.conversation_id || 'offline',
          user_id: 0,
          title: '离线对话',
          message_count: 1,
          is_active: true,
          created_at: offlineMessage.timestamp,
          updated_at: offlineMessage.timestamp
        }
      };

      console.log('📱 [SimpleChatService] 消息已保存到离线队列');

      return {
        success: true,
        data: mockResponse
      };

    } catch (error) {
      console.error('❌ [SimpleChatService] 处理离线消息失败:', error);
      return {
        success: false,
        error: {
          code: 'OFFLINE_MESSAGE_FAILED',
          message: '保存离线消息失败',
          retryable: true
        }
      };
    }
  }

  /**
   * 重试离线消息
   */
  public async retryOfflineMessages(): Promise<void> {
    if (!navigator.onLine || this.offlineMessages.length === 0) {
      return;
    }

    console.log(`[SimpleChatService] 重试${this.offlineMessages.length}条离线消息`);

    const messagesToRetry = [...this.offlineMessages];
    this.offlineMessages = [];

    for (const offlineMessage of messagesToRetry) {
      try {
        if (offlineMessage.retry_count >= offlineMessage.max_retries) {
          console.warn(`⚠️ [SimpleChatService] 消息${offlineMessage.id}重试次数已达上限`);
          continue;
        }

        const request: SendMessageRequest = {
          content: offlineMessage.content
        };

        const result = await this.sendMessage(request);

        if (result.success) {
          console.log(`✅ [SimpleChatService] 离线消息${offlineMessage.id}重试成功`);
        } else {
          offlineMessage.retry_count++;
          this.offlineMessages.push(offlineMessage);
        }

      } catch (error) {
        console.error(`❌ [SimpleChatService] 重试离线消息${offlineMessage.id}失败:`, error);
        offlineMessage.retry_count++;
        if (offlineMessage.retry_count < offlineMessage.max_retries) {
          this.offlineMessages.push(offlineMessage);
        }
      }
    }

    await this.saveOfflineMessages();
  }

  /**
   * 缓存对话数据
   */
  private async cacheConversationData(response: SendMessageResponse): Promise<void> {
    try {
      const cacheManager = await this.getCacheManager();

      // 缓存对话信息
      const conversationKey = this.generateCacheKey('conversation', { id: response.conversation.id });
      await cacheManager.set(conversationKey, response.conversation, {
        ttl: this.CACHE_CONFIG.conversations_ttl,
        persistent: true
      });

      // 缓存消息
      const messagesKey = this.generateCacheKey('conversation-messages', { id: response.conversation.id });
      const existingMessages = await cacheManager.get<SimpleChatMessage[]>(messagesKey) || [];
      const updatedMessages = [...existingMessages, response.user_message, response.ai_response];

      await cacheManager.set(messagesKey, updatedMessages, {
        ttl: this.CACHE_CONFIG.messages_ttl,
        persistent: true
      });

      console.log('💾 [SimpleChatService] 对话数据已缓存');

    } catch (error) {
      console.warn('⚠️ [SimpleChatService] 缓存对话数据失败:', error);
    }
  }

  /**
   * 加载离线消息
   */
  private async loadOfflineMessages(): Promise<void> {
    try {
      const cacheManager = await this.getCacheManager();
      const offlineKey = this.generateCacheKey('offline-messages');
      const savedMessages = await cacheManager.get<OfflineMessage[]>(offlineKey);

      if (savedMessages) {
        this.offlineMessages = savedMessages;
        console.log(`📱 [SimpleChatService] 加载了${savedMessages.length}条离线消息`);
      }

    } catch (error) {
      console.warn('⚠️ [SimpleChatService] 加载离线消息失败:', error);
    }
  }

  /**
   * 保存离线消息
   */
  private async saveOfflineMessages(): Promise<void> {
    try {
      const cacheManager = await this.getCacheManager();
      const offlineKey = this.generateCacheKey('offline-messages');
      await cacheManager.set(offlineKey, this.offlineMessages, {
        ttl: 7 * 24 * 60 * 60 * 1000, // 7天
        persistent: true
      });

    } catch (error) {
      console.warn('⚠️ [SimpleChatService] 保存离线消息失败:', error);
    }
  }

  /**
   * 获取离线消息数量
   */
  public getOfflineMessageCount(): number {
    return this.offlineMessages.length;
  }

  /**
   * 清除所有缓存
   */
  public async clearAllCache(): Promise<void> {
    try {
      const cacheManager = await this.getCacheManager();
      await cacheManager.clear('simple-chat:*');
      this.offlineMessages = [];
      console.log('🗑️ [SimpleChatService] 所有聊天缓存已清除');
    } catch (error) {
      console.warn('⚠️ [SimpleChatService] 清除缓存失败:', error);
    }
  }

  /**
   * 获取聊天统计
   */
  public async getChatStats(): Promise<ChatOperationResult<ChatStats>> {
    try {
      const response = await this.apiClient.get<ChatStats>('/api/v1/chat/simple/stats');

      return {
        success: true,
        data: response
      };

    } catch (error) {
      console.error('❌ [SimpleChatService] 获取聊天统计失败:', error);
      return {
        success: false,
        error: {
          code: 'GET_CHAT_STATS_FAILED',
          message: error instanceof Error ? error.message : '获取聊天统计失败',
          retryable: true
        }
      };
    }
  }
}

// 导出默认实例
export const simpleChatService = new SimpleChatService(new (await import('../core/ApiClient')).ApiClient());
