/**
 * 核心API客户端
 * 基于Capacitor HTTP插件，适配iOS平台，替代微信小程序的wx.request
 * 
 * 主要功能：
 * - 统一的HTTP请求封装
 * - 自动token刷新机制
 * - 错误处理和重试逻辑
 * - iOS平台优化
 * - 开发环境模拟数据支持
 */

import { CapacitorHttp, HttpOptions, HttpResponse } from '@capacitor/core';
import { Preferences } from '@capacitor/preferences';
import { Capacitor } from '@capacitor/core';
import { Network, ConnectionStatus } from '@capacitor/network';
import { Device, DeviceInfo } from '@capacitor/device';
import { StatusBar, Style } from '@capacitor/status-bar';

// 安全的响应数据处理函数
function safeProcessResponseData(data: any): any {
  if (data === null || data === undefined) {
    return data;
  }

  if (typeof data === 'object' && !Array.isArray(data)) {
    const processed: any = {};
    for (const [key, value] of Object.entries(data)) {
      processed[key] = safeProcessResponseData(value);
    }
    return processed;
  }

  if (Array.isArray(data)) {
    return data.map(item => safeProcessResponseData(item));
  }

  // 对于基本类型，确保安全转换
  if (typeof data === 'number') {
    return data; // 保持数字类型，不强制转换为字符串
  }

  return data;
}

// 安全的请求参数处理函数（iOS CapacitorHttp专用）
function safeProcessRequestParams(params: any): any {
  if (params === null || params === undefined) {
    return params;
  }

  if (typeof params === 'object' && !Array.isArray(params)) {
    const processed: any = {};
    for (const [key, value] of Object.entries(params)) {
      // iOS CapacitorHttp要求所有参数值都是字符串
      if (typeof value === 'number') {
        processed[key] = String(value);
      } else if (typeof value === 'boolean') {
        processed[key] = String(value);
      } else if (value === null || value === undefined) {
        processed[key] = '';
      } else if (typeof value === 'object') {
        processed[key] = JSON.stringify(value);
      } else {
        processed[key] = String(value);
      }
    }
    return processed;
  }

  return params;
}
import { getApiConfig, getPlatformNetworkConfig, getNetworkErrorConfig } from '../../config/api.config';
import {
  createSafeDate,
  isValidDate,
  safeToISOString,
  debugDateInfo,
  isTokenExpiringSoon
} from '../../utils/dateValidation';

// 基础类型定义
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
  detail?: string;
}

export interface ApiError {
  type: ApiErrorType;
  message: string;
  status?: number;
  details?: any;
}

export enum ApiErrorType {
  NETWORK_ERROR = 'network_error',
  AUTH_ERROR = 'auth_error',
  VALIDATION_ERROR = 'validation_error',
  SERVER_ERROR = 'server_error',
  TIMEOUT_ERROR = 'timeout_error'
}

// 认证相关类型
export interface AuthTokens {
  token: string;
  refresh_token?: string;
  expires_at?: string;
}

// 开发环境配置
export interface DevConfig {
  USE_MOCK_DATA: boolean;
  MOCK_DELAY: number;
}

/**
 * 核心API客户端类
 * 提供统一的HTTP请求接口，支持认证、错误处理、重试机制
 */
export class ApiClient {
  private config = getApiConfig();
  private networkConfig = getPlatformNetworkConfig();
  private errorConfig = getNetworkErrorConfig();
  private accessToken: string | null = null;
  private refreshToken: string | null = null;

  // iOS设备和网络状态
  private deviceInfo: DeviceInfo | null = null;
  private networkStatus: ConnectionStatus | null = null;
  private isOnline: boolean = true;

  // 开发环境配置
  private devConfig: DevConfig = {
    USE_MOCK_DATA: false, // 默认不使用模拟数据
    MOCK_DELAY: 800
  };

  constructor() {
    this.initializeClient();
  }

  /**
   * 获取平台信息
   */
  public get isNative(): boolean {
    return this.networkConfig.isNative;
  }

  public get platform(): string {
    return this.networkConfig.platform;
  }

  /**
   * 初始化客户端
   */
  private async initializeClient(): Promise<void> {
    // 初始化iOS原生功能
    await this.initializeIOSFeatures();

    // 输出平台和网络配置信息
    if (this.config.enableLogging) {
      console.log('🔧 FitMaster API Client 初始化:', {
        platform: this.networkConfig.platform,
        isNative: this.networkConfig.isNative,
        baseURL: this.config.baseURL,
        timeout: this.config.timeout,
        deviceInfo: this.deviceInfo,
        networkStatus: this.networkStatus
      });

      // iOS HTTP警告
      if (this.networkConfig.platform === 'ios' && this.config.baseURL.startsWith('http:')) {
        console.warn('⚠️ iOS检测到HTTP连接，请确保Info.plist已配置App Transport Security');
      }
    }

    // 从本地存储恢复token
    await this.loadTokensFromStorage();
  }

  /**
   * 初始化iOS原生功能
   */
  private async initializeIOSFeatures(): Promise<void> {
    if (!Capacitor.isNativePlatform()) {
      return;
    }

    try {
      // 获取设备信息
      this.deviceInfo = await Device.getInfo();

      // 获取网络状态
      this.networkStatus = await Network.getStatus();
      this.isOnline = this.networkStatus.connected;

      // 监听网络状态变化
      Network.addListener('networkStatusChange', (status) => {
        this.networkStatus = status;
        this.isOnline = status.connected;

        if (this.config.enableLogging) {
          console.log('📶 网络状态变化:', {
            connected: status.connected,
            connectionType: status.connectionType
          });
        }
      });

      // iOS状态栏初始化
      if (this.networkConfig.platform === 'ios') {
        await this.initializeIOSStatusBar();
      }

      if (this.config.enableLogging) {
        console.log('✅ iOS原生功能初始化完成');
      }
    } catch (error) {
      console.warn('⚠️ iOS原生功能初始化失败:', error);
    }
  }

  /**
   * 初始化iOS状态栏
   */
  private async initializeIOSStatusBar(): Promise<void> {
    try {
      await StatusBar.setStyle({ style: Style.Default });
      await StatusBar.show();

      if (this.config.enableLogging) {
        console.log('✅ iOS状态栏初始化完成');
      }
    } catch (error) {
      console.warn('⚠️ iOS状态栏初始化失败:', error);
    }
  }

  /**
   * Token管理方法
   */
  private async loadTokensFromStorage(): Promise<void> {
    try {
      const tokenData = await Preferences.get({ key: 'fitmaster_auth_tokens' });
      if (tokenData.value) {
        const tokens = JSON.parse(tokenData.value);
        this.accessToken = tokens.token || tokens.access_token;
        this.refreshToken = tokens.refresh_token;
        
        if (this.config.enableLogging) {
          console.log('✅ Token从存储中恢复成功');
        }
      }
    } catch (error) {
      console.warn('⚠️ 恢复Token失败:', error);
      await this.clearTokens();
    }
  }

  private async saveTokensToStorage(tokens: AuthTokens): Promise<void> {
    try {
      await Preferences.set({
        key: 'fitmaster_auth_tokens',
        value: JSON.stringify(tokens)
      });
      
      this.accessToken = tokens.token;
      this.refreshToken = tokens.refresh_token || null;
      
      // 保存过期时间（验证日期格式）
      if (tokens.expires_at) {
        debugDateInfo('ApiClient保存过期时间', tokens.expires_at);

        const testDate = createSafeDate(tokens.expires_at);
        if (testDate && isValidDate(testDate)) {
          await Preferences.set({
            key: 'token_expires_at',
            value: tokens.expires_at
          });

          if (this.config.enableLogging) {
            console.log('✅ 过期时间验证通过并保存:', {
              expires_at: tokens.expires_at,
              parsedDate: safeToISOString(testDate)
            });
          }
        } else {
          console.warn('⚠️ 过期时间格式无效，跳过保存:', tokens.expires_at);
        }
      }
      
      if (this.config.enableLogging) {
        console.log('✅ Token保存到存储成功');
      }
    } catch (error) {
      console.error('❌ 保存Token失败:', error);
    }
  }

  private async clearTokens(): Promise<void> {
    try {
      await Preferences.remove({ key: 'fitmaster_auth_tokens' });
      await Preferences.remove({ key: 'token_expires_at' });
      
      this.accessToken = null;
      this.refreshToken = null;
      
      if (this.config.enableLogging) {
        console.log('✅ Token清除成功');
      }
    } catch (error) {
      console.error('❌ 清除Token失败:', error);
    }
  }

  /**
   * 检查token是否需要刷新
   */
  private async shouldRefreshToken(): Promise<boolean> {
    try {
      const expiresAtData = await Preferences.get({ key: 'token_expires_at' });

      if (!expiresAtData.value) {
        if (this.config.enableLogging) {
          console.log('🔍 没有过期时间，默认需要刷新token');
        }
        return true;
      }

      debugDateInfo('ApiClient Token过期时间检查', expiresAtData.value);

      // 使用安全的日期创建和验证
      const shouldRefresh = isTokenExpiringSoon(expiresAtData.value);

      if (this.config.enableLogging) {
        const now = new Date();
        const expiresAt = createSafeDate(expiresAtData.value);

        console.log('🔍 Token刷新检查:', {
          current: safeToISOString(now),
          expires: expiresAt ? safeToISOString(expiresAt) : 'Invalid',
          shouldRefresh,
          timeUntilExpiry: expiresAt ? expiresAt.getTime() - now.getTime() : 'N/A',
          isValidDate: !!expiresAt && isValidDate(expiresAt)
        });
      }

      return shouldRefresh;

    } catch (error) {
      console.error('❌ 检查token过期时间失败:', {
        error: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined
      });
      return true;
    }
  }

  /**
   * 获取认证状态
   */
  public isAuthenticated(): boolean {
    return !!this.accessToken;
  }

  /**
   * 获取当前访问令牌
   */
  public getAccessToken(): string | null {
    return this.accessToken;
  }

  /**
   * 设置开发环境配置
   */
  public setDevConfig(config: Partial<DevConfig>): void {
    this.devConfig = { ...this.devConfig, ...config };
  }

  /**
   * 获取当前环境信息
   */
  public getEnvironmentInfo() {
    return {
      platform: this.networkConfig.platform,
      isNative: this.networkConfig.isNative,
      baseURL: this.config.baseURL,
      isDevelopment: this.config.enableLogging
    };
  }

  /**
   * 核心HTTP请求方法（iOS优化版）
   */
  public async request<T>(
    endpoint: string,
    options: Partial<HttpOptions> = {},
    skipAuth = false
  ): Promise<T> {
    // iOS网络状态检查
    if (!this.isOnline) {
      throw new Error('网络连接不可用，请检查网络设置');
    }

    const url = `${this.config.baseURL}${endpoint}`;

    let attempt = 0;
    while (attempt <= this.config.retries) {
      try {
        // 检查是否需要刷新token
        if (!skipAuth && this.accessToken && await this.shouldRefreshToken()) {
          if (this.config.enableLogging) {
            console.log('🔄 Token即将过期，尝试刷新');
          }
          await this.refreshAccessToken();
        }

        // 构建请求头
        const headers: Record<string, string> = {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          ...options.headers
        };

        // 添加认证头
        if (!skipAuth && this.accessToken) {
          headers.Authorization = `Bearer ${this.accessToken}`;
        }

        // 构建请求配置（iOS安全处理）
        const requestOptions: HttpOptions = {
          url,
          method: options.method || 'GET',
          headers,
          data: options.data, // data可以保持原样，主要问题在params
          params: safeProcessRequestParams(options.params), // 安全处理params
          connectTimeout: this.config.timeout,
          readTimeout: this.config.timeout,
          ...options
        };

        if (this.config.enableLogging) {
          console.log('📤 API请求:', {
            url,
            method: requestOptions.method,
            headers: { ...headers, Authorization: headers.Authorization ? '[HIDDEN]' : undefined },
            data: requestOptions.data
          });
        }

        // 发送请求
        const response: HttpResponse = await CapacitorHttp.request(requestOptions);

        if (this.config.enableLogging) {
          console.log('📥 API响应:', {
            status: response.status,
            url: response.url,
            dataType: typeof response.data,
            hasData: response.data !== null && response.data !== undefined
          });
        }

        // 处理401未授权错误
        if (response.status === 401 && !skipAuth && this.refreshToken) {
          if (this.config.enableLogging) {
            console.log('🔄 收到401响应，尝试刷新token');
          }

          const refreshed = await this.refreshAccessToken();
          if (refreshed) {
            // 重试原始请求
            return this.request<T>(endpoint, options, skipAuth);
          } else {
            throw new Error('Authentication failed');
          }
        }

        // 安全处理响应数据
        const safeResponseData = safeProcessResponseData(response.data);

        // 检查响应状态
        if (response.status < 200 || response.status >= 300) {
          const errorMessage = safeResponseData?.detail || safeResponseData?.message || 'Request failed';
          throw new Error(`HTTP ${response.status}: ${errorMessage}`);
        }

        return safeResponseData;

      } catch (error) {
        attempt++;

        if (attempt > this.config.retries) {
          throw this.handleError(error);
        }

        // 指数退避重试
        const delay = this.config.retryDelay * Math.pow(2, attempt - 1);
        if (this.config.enableLogging) {
          console.log(`🔄 请求失败，${delay}ms后重试 (${attempt}/${this.config.retries})`);
        }
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }

    throw new Error('Max retries exceeded');
  }

  /**
   * 便捷的HTTP方法
   */
  public async get<T>(endpoint: string, params?: Record<string, any>, skipAuth = false): Promise<T> {
    return this.request<T>(endpoint, { method: 'GET', params }, skipAuth);
  }

  public async post<T>(endpoint: string, data?: any, skipAuth = false): Promise<T> {
    return this.request<T>(endpoint, { method: 'POST', data }, skipAuth);
  }

  public async put<T>(endpoint: string, data?: any, skipAuth = false): Promise<T> {
    return this.request<T>(endpoint, { method: 'PUT', data }, skipAuth);
  }

  public async delete<T>(endpoint: string, data?: any, skipAuth = false): Promise<T> {
    return this.request<T>(endpoint, { method: 'DELETE', data }, skipAuth);
  }

  /**
   * 错误处理方法（iOS优化版）
   */
  private handleError(error: any): ApiError {
    if (this.config.enableLogging) {
      console.error('❌ API请求错误:', error);
    }

    // iOS特有的网络错误处理
    if (!this.isOnline) {
      return {
        type: ApiErrorType.NETWORK_ERROR,
        message: 'iOS设备网络连接不可用，请检查网络设置或切换到其他网络',
        details: { ...error, networkStatus: this.networkStatus }
      };
    }

    // 网络连接错误
    if (error.message?.includes('Failed to fetch') ||
        error.message?.includes('NetworkError') ||
        error.message?.includes('ERR_NETWORK')) {

      // iOS特有的网络错误信息
      let message = this.errorConfig.getErrorMessage(error);
      if (this.networkConfig.platform === 'ios') {
        if (this.networkStatus?.connectionType === 'cellular') {
          message += ' 当前使用蜂窝网络，请检查数据连接设置。';
        } else if (this.networkStatus?.connectionType === 'wifi') {
          message += ' 当前使用WiFi网络，请检查WiFi连接状态。';
        }
      }

      return {
        type: ApiErrorType.NETWORK_ERROR,
        message,
        details: { ...error, networkStatus: this.networkStatus, deviceInfo: this.deviceInfo }
      };
    }

    // 超时错误
    if (error.message?.includes('timeout') ||
        error.message?.includes('TimeoutError')) {
      return {
        type: ApiErrorType.TIMEOUT_ERROR,
        message: '请求超时，请检查网络连接',
        details: error
      };
    }

    // 认证错误
    if (error.message?.includes('401') || error.message?.includes('Authentication')) {
      return {
        type: ApiErrorType.AUTH_ERROR,
        message: '认证失败，请重新登录',
        details: error
      };
    }

    // 验证错误
    if (error.message?.includes('400') || error.message?.includes('validation')) {
      return {
        type: ApiErrorType.VALIDATION_ERROR,
        message: '请求参数错误',
        details: error
      };
    }

    // 服务器错误
    if (error.message?.includes('500') || error.message?.includes('502') || error.message?.includes('503')) {
      return {
        type: ApiErrorType.SERVER_ERROR,
        message: '服务器内部错误，请稍后重试',
        details: error
      };
    }

    return {
      type: ApiErrorType.SERVER_ERROR,
      message: error.message || '未知错误，请联系技术支持',
      details: error
    };
  }

  /**
   * Token刷新方法
   */
  private async refreshAccessToken(): Promise<boolean> {
    if (!this.refreshToken) {
      if (this.config.enableLogging) {
        console.warn('⚠️ 没有refresh token，无法刷新');
      }
      return false;
    }

    try {
      const response = await this.request<AuthTokens>(
        '/api/v1/auth/refresh',
        {
          method: 'POST',
          data: { refresh_token: this.refreshToken }
        },
        true // 跳过认证
      );

      await this.saveTokensToStorage(response);

      if (this.config.enableLogging) {
        console.log('✅ Token刷新成功');
      }

      return true;
    } catch (error) {
      console.error('❌ Token刷新失败:', error);
      await this.clearTokens();
      return false;
    }
  }

  /**
   * 保存认证令牌（供外部服务调用）
   */
  public async saveAuthTokens(tokens: AuthTokens): Promise<void> {
    await this.saveTokensToStorage(tokens);
  }

  /**
   * 清除认证令牌（供外部服务调用）
   */
  public async clearAuthTokens(): Promise<void> {
    await this.clearTokens();
  }

  /**
   * 测试服务器连接
   */
  public async testConnection(): Promise<boolean> {
    try {
      if (this.config.enableLogging) {
        console.log('🔍 测试API连接:', this.config.baseURL);
      }

      await this.get('/api/v1/ping', {}, true);

      if (this.config.enableLogging) {
        console.log('✅ API连接正常');
      }

      return true;
    } catch (error) {
      console.error('❌ API连接失败:', error);
      return false;
    }
  }

  /**
   * 构建完整的资源URL
   */
  public buildResourceUrl(path: string): string {
    if (!path) return '';

    // 如果已经是完整URL，直接返回
    if (path.startsWith('http://') || path.startsWith('https://')) {
      return path;
    }

    // 构建完整URL
    return `${this.config.baseURL}${path.startsWith('/') ? path : '/' + path}`;
  }

  /**
   * 获取iOS设备信息
   */
  public getDeviceInfo(): DeviceInfo | null {
    return this.deviceInfo;
  }

  /**
   * 获取网络状态
   */
  public getNetworkStatus(): ConnectionStatus | null {
    return this.networkStatus;
  }

  /**
   * 检查是否在线
   */
  public isNetworkOnline(): boolean {
    return this.isOnline;
  }

  /**
   * iOS状态栏样式管理
   */
  public async setIOSStatusBarStyle(style: 'light' | 'dark'): Promise<void> {
    if (this.networkConfig.platform !== 'ios' || !Capacitor.isNativePlatform()) {
      return;
    }

    try {
      const statusBarStyle = style === 'light' ? Style.Light : Style.Dark;
      await StatusBar.setStyle({ style: statusBarStyle });

      if (this.config.enableLogging) {
        console.log(`✅ iOS状态栏样式设置为: ${style}`);
      }
    } catch (error) {
      console.warn('⚠️ 设置iOS状态栏样式失败:', error);
    }
  }

  /**
   * iOS网络诊断
   */
  public getIOSNetworkDiagnostics() {
    return {
      isOnline: this.isOnline,
      networkStatus: this.networkStatus,
      deviceInfo: this.deviceInfo,
      platform: this.networkConfig.platform,
      isNative: this.networkConfig.isNative,
      baseURL: this.config.baseURL,
      timestamp: new Date().toISOString()
    };
  }

  /**
   * iOS性能监控
   */
  public startIOSPerformanceMonitoring(): void {
    if (this.networkConfig.platform !== 'ios' || !this.config.enableLogging) {
      return;
    }

    console.log('📊 开始iOS性能监控');

    // 监控内存使用情况（如果可用）
    if ('memory' in performance) {
      const memoryInfo = (performance as any).memory;
      console.log('💾 内存使用情况:', {
        used: Math.round(memoryInfo.usedJSHeapSize / 1024 / 1024) + 'MB',
        total: Math.round(memoryInfo.totalJSHeapSize / 1024 / 1024) + 'MB',
        limit: Math.round(memoryInfo.jsHeapSizeLimit / 1024 / 1024) + 'MB'
      });
    }
  }
}

// 导出单例实例
export const apiClient = new ApiClient();
export default apiClient;
